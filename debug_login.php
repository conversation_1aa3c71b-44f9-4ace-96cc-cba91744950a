<?php
require_once 'db_connection.php';

// Función para mostrar mensajes formateados
function showMessage($message, $type = 'info') {
    $color = 'black';
    switch ($type) {
        case 'success':
            $color = 'green';
            break;
        case 'error':
            $color = 'red';
            break;
        case 'warning':
            $color = 'orange';
            break;
    }
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid {$color}; background-color: #f9f9f9;'>{$message}</div>";
}

// Verificar la conexión a la base de datos
try {
    $conn = getConnection();
    showMessage("Conexión a la base de datos establecida correctamente.", "success");
    
    // Verificar si la tabla tb_usuarios existe
    $checkTableSql = "SHOW TABLES LIKE 'tb_usuarios'";
    $checkTableStmt = $conn->query($checkTableSql);
    $tableExists = $checkTableStmt->rowCount() > 0;
    
    if ($tableExists) {
        showMessage("La tabla tb_usuarios existe en la base de datos.", "success");
        
        // Contar usuarios
        $countSql = "SELECT COUNT(*) as total FROM tb_usuarios";
        $countStmt = $conn->query($countSql);
        $count = $countStmt->fetch(PDO::FETCH_ASSOC);
        
        showMessage("La tabla tb_usuarios contiene {$count['total']} usuarios.");
        
        // Mostrar usuarios (sin contraseñas completas)
        $usersSql = "SELECT id, username, SUBSTRING(password, 1, 10) as password_preview, nombre, email, rol, activo, intentos_fallidos FROM tb_usuarios";
        $usersStmt = $conn->query($usersSql);
        $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Usuarios en la base de datos:</h3>";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Password Preview</th><th>Nombre</th><th>Email</th><th>Rol</th><th>Activo</th><th>Intentos Fallidos</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            foreach ($user as $key => $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Probar autenticación con usuario admin
        echo "<h3>Prueba de autenticación:</h3>";
        
        $testUsername = "admin";
        $testPassword = "admin123";
        
        $sql = "SELECT id, username, password, nombre, rol, activo FROM tb_usuarios WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$testUsername]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            showMessage("Usuario '{$testUsername}' encontrado en la base de datos.", "success");
            
            echo "<p>Contraseña almacenada: " . $user['password'] . "</p>";
            
            // Verificar la contraseña
            if (password_verify($testPassword, $user['password'])) {
                showMessage("La contraseña para el usuario '{$testUsername}' es correcta.", "success");
            } else {
                showMessage("La contraseña para el usuario '{$testUsername}' es incorrecta.", "error");
                
                // Información adicional para depuración
                echo "<p>Detalles de la verificación:</p>";
                echo "<ul>";
                echo "<li>Contraseña ingresada: {$testPassword}</li>";
                echo "<li>Hash almacenado: {$user['password']}</li>";
                echo "<li>Algoritmo de hash: " . password_get_info($user['password'])['algoName'] . "</li>";
                echo "</ul>";
                
                // Crear un nuevo hash para comparar
                $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
                echo "<p>Nuevo hash generado: {$newHash}</p>";
                
                // Verificar si el usuario tiene la contraseña en texto plano
                if ($user['password'] === $testPassword) {
                    showMessage("ADVERTENCIA: La contraseña está almacenada en texto plano.", "warning");
                    
                    // Actualizar la contraseña con hash
                    $updateSql = "UPDATE tb_usuarios SET password = ? WHERE id = ?";
                    $updateStmt = $conn->prepare($updateSql);
                    $hashedPassword = password_hash($testPassword, PASSWORD_DEFAULT);
                    $result = $updateStmt->execute([$hashedPassword, $user['id']]);
                    
                    if ($result) {
                        showMessage("La contraseña ha sido actualizada con hash.", "success");
                    } else {
                        showMessage("Error al actualizar la contraseña.", "error");
                    }
                }
            }
            
            // Probar con el usuario nico.cornejo
            $testUsername2 = "nico.cornejo";
            $testPassword2 = "N1c0l7as17";
            
            $sql = "SELECT id, username, password, nombre, rol, activo FROM tb_usuarios WHERE username = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$testUsername2]);
            $user2 = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user2) {
                showMessage("Usuario '{$testUsername2}' encontrado en la base de datos.", "success");
                
                echo "<p>Contraseña almacenada: " . $user2['password'] . "</p>";
                
                // Verificar si es texto plano
                if ($user2['password'] === $testPassword2) {
                    showMessage("ADVERTENCIA: La contraseña está almacenada en texto plano.", "warning");
                    
                    // Actualizar la contraseña con hash
                    $updateSql = "UPDATE tb_usuarios SET password = ? WHERE id = ?";
                    $updateStmt = $conn->prepare($updateSql);
                    $hashedPassword = password_hash($testPassword2, PASSWORD_DEFAULT);
                    $result = $updateStmt->execute([$hashedPassword, $user2['id']]);
                    
                    if ($result) {
                        showMessage("La contraseña ha sido actualizada con hash.", "success");
                    } else {
                        showMessage("Error al actualizar la contraseña.", "error");
                    }
                } else {
                    // Verificar la contraseña
                    if (password_verify($testPassword2, $user2['password'])) {
                        showMessage("La contraseña para el usuario '{$testUsername2}' es correcta.", "success");
                    } else {
                        showMessage("La contraseña para el usuario '{$testUsername2}' es incorrecta.", "error");
                    }
                }
            } else {
                showMessage("Usuario '{$testUsername2}' no encontrado en la base de datos.", "error");
            }
        } else {
            showMessage("Usuario '{$testUsername}' no encontrado en la base de datos.", "error");
        }
    } else {
        showMessage("La tabla tb_usuarios no existe en la base de datos.", "error");
    }
} catch (Exception $e) {
    showMessage("Error: " . $e->getMessage(), "error");
}
?>
