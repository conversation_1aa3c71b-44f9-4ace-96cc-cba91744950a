<?php
require_once 'db_connection.php';

// Función para registrar mensajes de depuración
function debugLog($message, $type = 'info') {
    error_log("[obtener_upgrade.php] [" . $type . "] " . $message);
}

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Verificar si se proporcionó un ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    debugLog("Error: ID no proporcionado", 'error');
    echo json_encode(['status' => 'error', 'message' => 'ID no proporcionado']);
    exit;
}

$id = intval($_GET['id']);
debugLog("Obteniendo información del registro con ID: " . $id);

try {
    $conn = getConnection();
    debugLog("Conexión a la base de datos establecida");

    // Obtener información del registro
    $stmt = $conn->prepare("SELECT * FROM tb_upgrades WHERE id = ?");
    $stmt->execute([$id]);
    $registro = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$registro) {
        debugLog("Error: Registro con ID " . $id . " no encontrado", 'error');
        echo json_encode(['status' => 'error', 'message' => 'Registro no encontrado']);
        exit;
    }

    debugLog("Registro encontrado: " . print_r($registro, true));

    // Preparar la respuesta
    $response = [
        'status' => 'success',
        'data' => $registro
    ];

    echo json_encode($response);
} catch (Exception $e) {
    debugLog("Excepción capturada: " . $e->getMessage(), 'error');
    echo json_encode(['status' => 'error', 'message' => 'Error: ' . $e->getMessage()]);
}
?>
