<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener los datos enviados
$data = json_decode(file_get_contents('php://input'), true);

// Verificar que se recibieron los datos necesarios
if (!isset($data['dte_id']) || !isset($data['productos'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Datos incompletos']);
    exit;
}

// Si no hay productos, devolver un mensaje informativo
if (empty($data['productos'])) {
    $totalProductos = $data['total_productos'] ?? 0;
    if ($totalProductos > 0) {
        echo json_encode([
            'status' => 'info',
            'message' => "Hay $totalProductos productos en el DTE, pero no se pudieron procesar",
            'dte_id' => $data['dte_id'],
            'productos_guardados' => 0,
            'total_productos' => $totalProductos
        ]);
    } else {
        echo json_encode([
            'status' => 'info',
            'message' => 'No hay productos para guardar',
            'dte_id' => $data['dte_id'],
            'productos_guardados' => 0,
            'total_productos' => 0
        ]);
    }
    exit;
}

try {
    $conn = getConnection();

    // Iniciar transacción
    $conn->beginTransaction();

    // Preparar la consulta para insertar productos
    $stmt = $conn->prepare("INSERT INTO tb_dte_productos (
        dte_id,
        repuesto_id,
        nombre_producto,
        descripcion_producto,
        cantidad,
        precio_unitario,
        monto_item
    ) VALUES (
        :dte_id,
        :repuesto_id,
        :nombre_producto,
        :descripcion_producto,
        :cantidad,
        :precio_unitario,
        :monto_item
    )");

    // Contador de productos guardados correctamente
    $productosGuardados = 0;
    $productosOmitidos = 0;
    $productosConId = 0;
    $productosSinId = 0;

    // Insertar cada producto
    foreach ($data['productos'] as $producto) {
        // Verificar que el producto tenga todos los campos necesarios
        if (!isset($producto['cantidad']) ||
            !isset($producto['precio_unitario']) ||
            !isset($producto['monto_item']) ||
            !isset($producto['nombre_producto'])) {
            $productosOmitidos++;
            continue; // Saltar este producto si falta algún campo
        }

        // Verificar si tiene ID de repuesto
        if (!empty($producto['repuesto_id'])) {
            $productosConId++;
        } else {
            $productosSinId++;
        }

        $dteId = $data['dte_id'];
        $repuestoId = $producto['repuesto_id'] ?: null;
        $nombreProducto = $producto['nombre_producto'];
        $descripcionProducto = $producto['descripcion_producto'] ?? '';
        $cantidad = $producto['cantidad'];
        $precioUnitario = $producto['precio_unitario'];
        $montoItem = $producto['monto_item'];

        $stmt->bindParam(':dte_id', $dteId);
        $stmt->bindParam(':repuesto_id', $repuestoId);
        $stmt->bindParam(':nombre_producto', $nombreProducto);
        $stmt->bindParam(':descripcion_producto', $descripcionProducto);
        $stmt->bindParam(':cantidad', $cantidad);
        $stmt->bindParam(':precio_unitario', $precioUnitario);
        $stmt->bindParam(':monto_item', $montoItem);

        $stmt->execute();
        $productosGuardados++;
    }

    // Confirmar la transacción
    $conn->commit();

    // Preparar mensaje según si todos los productos se guardaron o no
    $totalProductos = $data['total_productos'] ?? count($data['productos']);
    $mensaje = '';

    if ($productosGuardados === $totalProductos) {
        $mensaje = "Se guardaron correctamente $productosGuardados productos vendidos";
        if ($productosConId > 0 && $productosSinId > 0) {
            $mensaje .= " ($productosConId con ID de repuesto y $productosSinId sin ID)";
        }
    } else {
        $mensaje = "Se guardaron $productosGuardados de $totalProductos productos. ";
        if ($productosOmitidos > 0) {
            $mensaje .= "$productosOmitidos productos fueron omitidos por falta de datos. ";
        }
    }

    echo json_encode([
        'status' => 'success',
        'message' => $mensaje,
        'dte_id' => $data['dte_id'],
        'productos_guardados' => $productosGuardados,
        'productos_omitidos' => $productosOmitidos,
        'productos_con_id' => $productosConId,
        'productos_sin_id' => $productosSinId,
        'total_productos' => $totalProductos
    ]);

} catch (Exception $e) {
    // Revertir la transacción en caso de error
    if ($conn) {
        $conn->rollBack();
    }

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error al guardar los productos: ' . $e->getMessage()
    ]);
}
?>
