/**
 * dashboard.css - Estilos para el dashboard de ventas
 */

/* Contenedor principal del dashboard */
.dashboard-container {
    padding: 20px 0;
}

/* Estilos para las tarjetas de KPI */
.kpi-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: 100%;
    transition: transform 0.3s ease;
    border-top: 4px solid #34495e;
}

.kpi-card:hover {
    transform: translateY(-5px);
}

.kpi-title {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-weight: 500;
}

.kpi-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #34495e;
    margin-bottom: 5px;
}

.variacion {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
}

.variacion.positiva {
    background-color: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.variacion.negativa {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Estilos para los valores de referencia */
.kpi-referencia {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-top: 5px;
    font-weight: 500;
}

/* Estilos para la barra de progreso */
.meta-progress-container {
    background-color: #ecf0f1;
    border-radius: 10px;
    height: 10px;
    margin: 15px 0;
    overflow: hidden;
}

.meta-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 10px;
    text-align: center;
    color: white;
    font-size: 0.7rem;
    line-height: 10px;
    transition: width 0.5s ease;
}

/* Estilos para las tarjetas de gráficos */
.chart-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    height: 100%;
}

.chart-title {
    color: #34495e;
    font-size: 1.1rem;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Estilos para el selector de período */
.periodo-selector-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 0;
}

.periodo-selector {
    background-color: #34495e;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.periodo-selector:hover {
    background-color: #2c3e50;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .kpi-card, .chart-card {
        margin-bottom: 15px;
    }

    .kpi-value {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 250px;
    }
}

/* Estilos para el título del dashboard */
.dashboard-title {
    color: #34495e;
    font-size: 1.5rem;
    margin-bottom: 0;
    font-weight: 700;
    display: flex;
    align-items: center;
}

.dashboard-title i {
    margin-right: 10px;
    color: #3498db;
}

/* Estilos para las leyendas de los gráficos */
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

/* Estilos para el título del período */
.periodo-title {
    font-size: 1.2rem;
    color: #34495e;
    margin-bottom: 20px;
    font-weight: 600;
    text-align: center;
}

/* Estilos para la sección de meta */
.meta-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Estilos para los tooltips personalizados */
.custom-tooltip {
    background-color: rgba(52, 73, 94, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    pointer-events: none;
    position: absolute;
    z-index: 100;
    display: none;
}
