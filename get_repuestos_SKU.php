<?php
// Configuración para depuración y manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'errors.log');
set_time_limit(30);

// Permitir CORS para desarrollo
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Cache-Control, Pragma');

// Si es una solicitud OPTIONS (preflight), responder inmediatamente
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit;
}

// Función para registrar errores de manera consistente
function logError($message, $context = []) {
    $contextStr = !empty($context) ? ' | Contexto: ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    error_log("SKU_SEARCH: " . $message . $contextStr);
}

// Función para enviar respuesta JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

try {
    // Verificar que el método de solicitud sea GET
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Método no permitido. Use GET.',
            'error_code' => 'METHOD_NOT_ALLOWED'
        ], 405);
    }

    // Obtener término de búsqueda
    $sku = isset($_GET['search']) ? trim($_GET['search']) : '';

    // Validar que el término de búsqueda no esté vacío
    if (empty($sku)) {
        sendJsonResponse([
            'status' => 'success',
            'data' => [],
            'message' => 'Término de búsqueda vacío'
        ]);
    }

    // Registrar la búsqueda
    logError("Iniciando búsqueda de SKU", ['search_term' => $sku]);

    // Incluir archivo de conexión
    if (!file_exists('db_connection.php')) {
        logError("Archivo de conexión no encontrado", ['path' => __DIR__]);
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Archivo de conexión no encontrado',
            'error_code' => 'FILE_NOT_FOUND',
            'debug' => [
                'current_dir' => __DIR__,
                'script_path' => $_SERVER['SCRIPT_FILENAME']
            ]
        ], 500);
    }

    require_once 'db_connection.php';

    // Verificar que la función getConnection exista
    if (!function_exists('getConnection')) {
        logError("Función getConnection no encontrada");
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Función de conexión no encontrada',
            'error_code' => 'FUNCTION_NOT_FOUND'
        ], 500);
    }

    // Obtener conexión a la base de datos
    $conn = getConnection();

    // Verificar que la conexión sea válida
    if (!($conn instanceof PDO)) {
        logError("Conexión inválida", ['type' => gettype($conn)]);
        sendJsonResponse([
            'status' => 'error',
            'message' => 'Conexión a base de datos inválida',
            'error_code' => 'INVALID_CONNECTION'
        ], 500);
    }

    // Consulta optimizada para buscar por SKU o nombre
    $query = "SELECT
                r.id,
                r.sku,
                r.nombre,
                r.precio_venta as precio,
                r.url_imagen
              FROM repuesto r
              WHERE (r.sku LIKE :sku OR r.nombre LIKE :nombre)
                AND r.activo = 1
              ORDER BY
                CASE
                    WHEN r.sku = :exact_sku THEN 1
                    WHEN r.sku LIKE :start_sku THEN 2
                    ELSE 3
                END,
                r.sku ASC
              LIMIT 15";

    try {
        $stmt = $conn->prepare($query);
        $stmt->execute([
            'sku' => "%$sku%",
            'nombre' => "%$sku%",
            'exact_sku' => $sku,
            'start_sku' => "$sku%"
        ]);

        $productos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Registrar cantidad de resultados
        logError("Búsqueda completada", [
            'search_term' => $sku,
            'results_count' => count($productos)
        ]);

        // Devolver resultados
        sendJsonResponse([
            'status' => 'success',
            'data' => $productos ?: [],
            'count' => count($productos),
            'search_term' => $sku
        ]);
    } catch (PDOException $e) {
        logError("Error en la consulta SQL: " . $e->getMessage(), [
            'query' => $query,
            'error_code' => $e->getCode()
        ]);

        sendJsonResponse([
            'status' => 'error',
            'message' => 'Error en la consulta SQL',
            'error_code' => 'SQL_ERROR',
            'error_details' => $e->getMessage()
        ], 500);
    }

} catch (PDOException $e) {
    // Error específico de base de datos
    logError("Error de base de datos: " . $e->getMessage(), [
        'search_term' => $sku ?? null,
        'error_code' => $e->getCode(),
        'trace' => $e->getTraceAsString()
    ]);

    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error de conexión a la base de datos',
        'error_code' => 'DB_ERROR',
        'error_details' => $e->getMessage(),
        'debug_info' => [
            'server' => $_SERVER['SERVER_NAME'] ?? 'unknown',
            'script' => $_SERVER['SCRIPT_NAME'] ?? 'unknown',
            'time' => date('Y-m-d H:i:s')
        ]
    ], 500);

} catch (Exception $e) {
    // Error general
    logError("Error general: " . $e->getMessage(), [
        'search_term' => $sku ?? null,
        'trace' => $e->getTraceAsString()
    ]);

    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error al procesar la solicitud',
        'error_code' => 'GENERAL_ERROR',
        'error_details' => $e->getMessage(),
        'debug_info' => [
            'server' => $_SERVER['SERVER_NAME'] ?? 'unknown',
            'script' => $_SERVER['SCRIPT_NAME'] ?? 'unknown',
            'time' => date('Y-m-d H:i:s')
        ]
    ], 500);

} finally {
    // Cerrar conexión si existe
    if (isset($conn) && function_exists('closeConnection')) {
        closeConnection($conn);
    }
}