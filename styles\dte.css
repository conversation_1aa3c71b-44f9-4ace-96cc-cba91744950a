/* Estilos para el contador DTE */
.dte-count {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    position: absolute;
    top: -5px;
    right: -5px;
    transition: all 0.3s ease;
    /* Ocultar el contador si es 0 */
    visibility: hidden; /* Oculto por defecto */
}

/* Animación para el contador cuando se actualiza */
.dte-count.updated {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}

/* Estilos para el botón DTE */
#openDTEBtn {
    position: relative;
    margin: 0 10px;
}

/* Estilos para el icono del botón DTE */
#openDTEBtn i {
    font-size: 18px;
}
