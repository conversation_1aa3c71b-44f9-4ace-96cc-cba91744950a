<?php
// Script para probar la conversión a WebP
require_once 'image_processor.php';

// Mostrar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar si se ha enviado un formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h1>Resultado de la prueba de conversión a WebP</h1>";
    
    // Verificar si se ha enviado un archivo
    if (!isset($_FILES['imagen']) || $_FILES['imagen']['error'] !== UPLOAD_ERR_OK) {
        echo "<p style='color:red'>Error: No se ha enviado ninguna imagen o ha ocurrido un error en la subida.</p>";
        if (isset($_FILES['imagen'])) {
            echo "<p>Código de error: " . $_FILES['imagen']['error'] . "</p>";
        }
    } else {
        // Mostrar información del archivo subido
        echo "<h2>Información del archivo subido</h2>";
        echo "<ul>";
        echo "<li>Nombre: " . htmlspecialchars($_FILES['imagen']['name']) . "</li>";
        echo "<li>Tipo: " . htmlspecialchars($_FILES['imagen']['type']) . "</li>";
        echo "<li>Tamaño: " . htmlspecialchars($_FILES['imagen']['size']) . " bytes</li>";
        echo "<li>Ruta temporal: " . htmlspecialchars($_FILES['imagen']['tmp_name']) . "</li>";
        echo "</ul>";
        
        // Directorio para guardar las imágenes
        $uploadDir = 'images/fotos_repuestos/';
        $absoluteUploadDir = __DIR__ . '/' . $uploadDir;
        
        // Asegurarse de que el directorio termine con una barra
        if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
            $absoluteUploadDir .= '/';
        }
        
        echo "<h2>Directorio de destino</h2>";
        echo "<p>Ruta: <code>" . htmlspecialchars($absoluteUploadDir) . "</code></p>";
        
        // Verificar que el directorio existe
        if (!file_exists($absoluteUploadDir)) {
            echo "<p>El directorio no existe, intentando crearlo...</p>";
            if (mkdir($absoluteUploadDir, 0777, true)) {
                echo "<p style='color:green'>Directorio creado correctamente.</p>";
            } else {
                echo "<p style='color:red'>Error al crear el directorio.</p>";
                exit;
            }
        }
        
        // Verificar permisos
        if (!is_writable($absoluteUploadDir)) {
            echo "<p>El directorio no tiene permisos de escritura, intentando cambiar permisos...</p>";
            if (chmod($absoluteUploadDir, 0777)) {
                echo "<p style='color:green'>Permisos cambiados correctamente.</p>";
            } else {
                echo "<p style='color:red'>Error al cambiar los permisos.</p>";
                exit;
            }
        }
        
        // Verificar soporte para WebP
        $gdInfo = gd_info();
        $webpSupported = isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'];
        
        echo "<h2>Soporte para WebP</h2>";
        if ($webpSupported) {
            echo "<p style='color:green'>✓ WebP está soportado en este servidor.</p>";
        } else {
            echo "<p style='color:orange'>⚠ WebP NO está soportado en este servidor. Se usará el formato original.</p>";
        }
        
        // Procesar la imagen usando ImageProcessor
        echo "<h2>Procesando imagen con ImageProcessor</h2>";
        
        try {
            $imageResult = ImageProcessor::processUploadedImage(
                $_FILES['imagen'],
                $absoluteUploadDir,
                'webp_test_',  // Prefijo para el nombre del archivo
                800,           // Ancho máximo
                800,           // Alto máximo
                85             // Calidad
            );
            
            if ($imageResult['success']) {
                echo "<p style='color:green'>✓ Imagen procesada correctamente.</p>";
                echo "<p>Mensaje: " . htmlspecialchars($imageResult['message']) . "</p>";
                echo "<p>Ruta de la imagen: <code>" . htmlspecialchars($imageResult['path']) . "</code></p>";
                
                // Verificar si la imagen es WebP
                $isWebP = strtolower(pathinfo($imageResult['path'], PATHINFO_EXTENSION)) === 'webp';
                
                if ($isWebP) {
                    echo "<p style='color:green'>✓ La imagen se convirtió a formato WebP correctamente.</p>";
                } else {
                    echo "<p style='color:orange'>⚠ La imagen NO se convirtió a formato WebP. Se guardó en formato original.</p>";
                }
                
                // Mostrar la imagen
                $webPath = str_replace(__DIR__, '', $imageResult['path']);
                echo "<p>Vista previa de la imagen:</p>";
                echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
                
                // Mostrar información del archivo
                $filesize = filesize($imageResult['path']);
                $filetype = mime_content_type($imageResult['path']);
                
                echo "<h3>Información del archivo procesado</h3>";
                echo "<ul>";
                echo "<li>Tamaño: " . number_format($filesize) . " bytes (" . number_format($filesize / 1024, 2) . " KB)</li>";
                echo "<li>Tipo MIME: " . htmlspecialchars($filetype) . "</li>";
                echo "<li>Extensión: " . htmlspecialchars(pathinfo($imageResult['path'], PATHINFO_EXTENSION)) . "</li>";
                echo "</ul>";
                
                // Prueba manual de conversión a WebP
                if (!$isWebP && $webpSupported) {
                    echo "<h3>Prueba manual de conversión a WebP</h3>";
                    
                    $manualWebpPath = $absoluteUploadDir . 'manual_webp_' . uniqid() . '.webp';
                    
                    if (ImageProcessor::convertToWebP($imageResult['path'], $manualWebpPath, 85)) {
                        echo "<p style='color:green'>✓ Conversión manual a WebP exitosa.</p>";
                        
                        // Mostrar la imagen WebP
                        $manualWebpWebPath = str_replace(__DIR__, '', $manualWebpPath);
                        echo "<p>Vista previa de la imagen WebP:</p>";
                        echo "<img src='" . htmlspecialchars($manualWebpWebPath) . "' style='max-width: 300px; max-height: 300px;' />";
                        
                        // Mostrar información del archivo WebP
                        $webpFilesize = filesize($manualWebpPath);
                        $webpFiletype = mime_content_type($manualWebpPath);
                        
                        echo "<h4>Información del archivo WebP</h4>";
                        echo "<ul>";
                        echo "<li>Tamaño: " . number_format($webpFilesize) . " bytes (" . number_format($webpFilesize / 1024, 2) . " KB)</li>";
                        echo "<li>Tipo MIME: " . htmlspecialchars($webpFiletype) . "</li>";
                        echo "<li>Reducción de tamaño: " . ($filesize > 0 ? number_format(100 - ($webpFilesize / $filesize * 100), 2) . "%" : "N/A") . "</li>";
                        echo "</ul>";
                    } else {
                        echo "<p style='color:red'>✗ Error en la conversión manual a WebP.</p>";
                    }
                }
            } else {
                echo "<p style='color:red'>✗ Error al procesar la imagen: " . htmlspecialchars($imageResult['message']) . "</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color:red'>✗ Excepción al procesar la imagen: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<p><a href='test_webp_conversion.php'>Volver al formulario</a></p>";
} else {
    // Mostrar formulario
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de conversión a WebP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #34495e;
        }
        form {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }
        input[type="file"] {
            margin-bottom: 20px;
        }
        button {
            background-color: #34495e;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2c3e50;
        }
        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #34495e;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Prueba de conversión a WebP</h1>
    <p>Este script prueba la conversión de imágenes a formato WebP usando la clase ImageProcessor.</p>
    
    <div class="info-box">
        <h3>Información sobre WebP</h3>
        <p>WebP es un formato de imagen moderno desarrollado por Google que ofrece compresión superior tanto para imágenes con pérdida como sin pérdida.</p>
        <p>Ventajas de WebP:</p>
        <ul>
            <li>Archivos hasta un 34% más pequeños que JPEG con la misma calidad visual</li>
            <li>Soporte para transparencia (como PNG) pero con mejor compresión</li>
            <li>Soporte para animaciones (como GIF) pero con mejor compresión</li>
        </ul>
    </div>
    
    <form action="" method="post" enctype="multipart/form-data">
        <div>
            <label for="imagen">Selecciona una imagen para convertir a WebP:</label>
            <input type="file" name="imagen" id="imagen" accept="image/*" required>
        </div>
        <div>
            <button type="submit">Procesar imagen</button>
        </div>
    </form>
    
    <h2>Información del sistema</h2>
    <ul>
        <?php
        // Verificar soporte para WebP
        $gdInfo = gd_info();
        $webpSupported = isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'];
        ?>
        <li>PHP Version: <?php echo phpversion(); ?></li>
        <li>GD Version: <?php echo isset($gdInfo['GD Version']) ? $gdInfo['GD Version'] : 'No disponible'; ?></li>
        <li>WebP Support: <?php echo $webpSupported ? '<span style="color:green">Soportado</span>' : '<span style="color:orange">No soportado</span>'; ?></li>
        <li>JPEG Support: <?php echo isset($gdInfo['JPEG Support']) && $gdInfo['JPEG Support'] ? '<span style="color:green">Soportado</span>' : '<span style="color:red">No soportado</span>'; ?></li>
        <li>PNG Support: <?php echo isset($gdInfo['PNG Support']) && $gdInfo['PNG Support'] ? '<span style="color:green">Soportado</span>' : '<span style="color:red">No soportado</span>'; ?></li>
    </ul>
</body>
</html>
<?php
}
?>
