<?php
// Script para procesar en batch todos los documentos pendientes de envío
require_once 'db_connection.php';
require_once 'config.php'; // Incluir archivo de configuración

// Función para probar si una URL es accesible
function probarURL($url) {
    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_NOBODY => true,
        CURLOPT_TIMEOUT => 5
    ]);
    curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $httpCode;
}

// Función para realizar una solicitud a nuestro endpoint, ahora con parámetro tipo_dte y token
function generarSobre($apiKey = '2037-N680-6391-2493-5987', $limite = 50, $tipoDTE = null, $requestToken = null) {
    // Probar diferentes URLs posibles para encontrar la correcta
    $host = $_SERVER['HTTP_HOST'];

    // Determinar el protocolo correcto basado en HTTPS
    $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ||
                (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') ||
                (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) ? 'https' : 'http';

    $urlsParaProbar = [
        $protocol . '://' . $host . '/generar_sobre.php',
        $protocol . '://' . $host . '/projects/tata_repuestos/generar_sobre.php',
        $protocol . '://' . $host . '/tata_repuestos/generar_sobre.php'
    ];

    $url = $urlsParaProbar[0]; // URL por defecto

    // Probar cada URL y usar la primera que responda con un código diferente a 404
    foreach ($urlsParaProbar as $urlCandidata) {
        $codigo = probarURL($urlCandidata);
        error_log("Probando URL: $urlCandidata - Código: $codigo");

        if ($codigo != 404) {
            $url = $urlCandidata;
            error_log("URL válida encontrada: $url");
            break;
        }
    }

    $postFields = [
        'apiKey' => $apiKey,
        'limite' => $limite
    ];

    // Agregar tipo_dte al POST si se especificó
    if ($tipoDTE !== null) {
        $postFields['tipo_dte'] = $tipoDTE;
    }

    // Agregar token de solicitud para prevenir duplicados
    if ($requestToken !== null) {
        $postFields['request_token'] = $requestToken . '_' . uniqid(); // Añadir un sufijo único para cada solicitud individual
    }

    // Registrar en consola los parámetros de la solicitud con formato más claro
    error_log("=== INICIANDO GENERACIÓN DE SOBRE ===");
    error_log("Tipo DTE: " . ($tipoDTE ?? 'Todos los tipos'));
    error_log("Límite de documentos: $limite");
    error_log("Token de solicitud: " . ($requestToken ?? 'No especificado'));
    error_log("URL de destino: $url");

    // Verificar si el archivo generar_sobre.php existe en la raíz
    $rutaLocal = __DIR__ . '/generar_sobre.php';
    error_log("Verificando existencia del archivo local: $rutaLocal - " . (file_exists($rutaLocal) ? 'EXISTE' : 'NO EXISTE'));

    // Verificar si existe en otras ubicaciones posibles
    $rutasAlternativas = [
        __DIR__ . '/generar_sobre.php',
        __DIR__ . '/../generar_sobre.php',
        '/var/www/html/generar_sobre.php',
        '/var/www/html/projects/tata_repuestos/generar_sobre.php'
    ];

    foreach ($rutasAlternativas as $ruta) {
        error_log("Verificando ruta alternativa: $ruta - " . (file_exists($ruta) ? 'EXISTE' : 'NO EXISTE'));
    }

    error_log("Parámetros completos: " . json_encode($postFields));

    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postFields,
        CURLOPT_TIMEOUT => 30,
        // Agregar cabeceras para depuración
        CURLOPT_HEADER => false,
        CURLOPT_VERBOSE => true
    ]);

    // Capturar información detallada de la solicitud
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);

    // Obtener información detallada de la solicitud
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);

    curl_close($ch);

    // Registrar en consola la respuesta
    error_log("Respuesta de generarSobre - Código HTTP: $httpCode");

    // Si hay un error, registrar detalles adicionales
    if ($httpCode != 200 || $error) {
        error_log("Error en generarSobre: " . ($error ?: "Código HTTP $httpCode"));
        error_log("URL solicitada: $url");
        error_log("Detalles de la solicitud: " . json_encode($info));
        error_log("Log detallado: " . $verboseLog);

        // Intentar decodificar la respuesta para obtener más detalles
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['error'])) {
            error_log("Error reportado: " . $responseData['error']);

            // Registrar detalles adicionales si están disponibles
            if (isset($responseData['detalle_error'])) {
                error_log("Detalle del error: " . $responseData['detalle_error']);
            }

            if (isset($responseData['details'])) {
                error_log("Diagnóstico: " . json_encode($responseData['details']));
            }
        } else {
            // Si no se puede decodificar como JSON, mostrar los primeros 500 caracteres
            error_log("Respuesta no JSON: " . substr($response, 0, 500));
        }
    }

    // Esperar un breve tiempo entre solicitudes para evitar sobrecargar la API
    usleep(500000); // 500ms de espera

    return [
        'code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'info' => $info,
        'verbose_log' => $verboseLog
    ];
}

try {
    // Obtener parámetros de la solicitud
    $limite = isset($_GET['limite']) ? (int)$_GET['limite'] : 50; // Cambiado a 50 por defecto
    $maxSobres = isset($_GET['maxSobres']) ? (int)$_GET['maxSobres'] : 1; // Cambiado a 1 por defecto para generar un solo sobre
    $tipoDTE = isset($_GET['tipo_dte']) ? $_GET['tipo_dte'] : null; // Opcional: procesar solo un tipo específico
    $requestToken = isset($_GET['request_token']) ? $_GET['request_token'] : null; // Token para prevenir solicitudes duplicadas

    // Validar parámetros
    if ($limite <= 0 || $limite > 100) $limite = 50; // Cambiado a 50 por defecto
    if ($maxSobres <= 0 || $maxSobres > 50) $maxSobres = 1; // Cambiado a 1 por defecto

    $conn = getConnection();

    // Verificar si ya existe una solicitud en proceso con este token
    if ($requestToken) {
        // Crear tabla de control de solicitudes si no existe
        $conn->exec("CREATE TABLE IF NOT EXISTS tb_request_control (
            id INT AUTO_INCREMENT PRIMARY KEY,
            request_token VARCHAR(100) NOT NULL,
            request_type VARCHAR(50) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME NULL,
            status TINYINT DEFAULT 0,
            UNIQUE KEY (request_token)
        )");

        // Verificar si el token ya existe y está en proceso
        $stmt = $conn->prepare("SELECT id, status FROM tb_request_control WHERE request_token = :token");
        $stmt->bindParam(':token', $requestToken);
        $stmt->execute();
        $existingRequest = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRequest) {
            // Si la solicitud ya existe y está en proceso (status = 0)
            if ($existingRequest['status'] == 0) {
                echo json_encode([
                    'error' => 'Ya existe una solicitud en proceso con este token',
                    'request_id' => $existingRequest['id']
                ]);
                exit;
            }
        } else {
            // Registrar la nueva solicitud
            $stmt = $conn->prepare("INSERT INTO tb_request_control (request_token, request_type) VALUES (:token, 'procesar_sobres_pendientes')");
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();
        }
    } else {
        // Si no hay token, generamos uno para control interno
        $requestToken = 'batch_' . uniqid();
    }

    // Si se especificó un tipo_dte específico, procesar solo ese tipo
    if ($tipoDTE !== null) {
        // Procesar solo documentos del tipo especificado
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM tb_facturas_dte WHERE estado_sobre = 0 AND tipo_dte = :tipo_dte");
        $stmt->bindParam(':tipo_dte', $tipoDTE);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pendientes = [['tipo_dte' => $tipoDTE, 'total' => $result['total']]];
    } else {
        // Obtener conteo de documentos pendientes agrupados por tipo_dte
        $stmt = $conn->query("SELECT tipo_dte, COUNT(*) as total
                             FROM tb_facturas_dte
                             WHERE estado_sobre = 0
                             GROUP BY tipo_dte
                             ORDER BY total DESC");
        $pendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Verificar si hay documentos pendientes
    if (empty($pendientes)) {
        echo json_encode(['message' => 'No hay documentos pendientes de envío']);
        exit;
    }

    $procesados = 0;
    $errores = 0;
    $resultados = [];
    $sobresGenerados = 0;
    $resultadosPorTipo = [];

    // Procesar cada tipo de DTE por separado
    foreach ($pendientes as $pendiente) {
        $tipoDTE = $pendiente['tipo_dte'];
        $totalPendientes = $pendiente['total'];

        // Inicializar resultados para este tipo de DTE
        $resultadosPorTipo[$tipoDTE] = [
            'tipo_dte' => $tipoDTE,
            'total_inicial' => $totalPendientes,
            'procesados' => 0,
            'errores' => 0,
            'sobres_generados' => 0
        ];

        // Procesar un solo sobre por tipo de DTE, con un límite de 50 documentos
        $sobresPorTipoDTE = 0;
        // Forzar a generar solo un sobre por tipo de DTE
        $maxSobresPorTipo = 1;

        // Solo procesamos un sobre por tipo de DTE
        if ($totalPendientes > 0) {

            // Generar un token único para cada solicitud individual basado en el token principal
            $sobreToken = $requestToken . '_' . $tipoDTE . '_' . $sobresPorTipoDTE;
            $result = generarSobre('2037-N680-6391-2493-5987', $limite, $tipoDTE, $sobreToken);
            $response = json_decode($result['response'], true);

            // Detalles del intento para el log
            $intentoLog = [
                'tipo_dte' => $tipoDTE,
                'http_code' => $result['code'],
                'curl_error' => $result['error'] ?: 'ninguno'
            ];

            // Registrar información detallada de la solicitud en la consola
            error_log("Procesando respuesta para tipo DTE: $tipoDTE");
            error_log("Código HTTP: " . $result['code']);

            if ($result['error']) {
                error_log("Error cURL: " . $result['error']);
            }

            if ($result['code'] == 200 && isset($response['success']) && $response['success']) {
                $sobresGenerados++;
                $sobresPorTipoDTE++;
                $procesadosEnEsteLote = $response['documentos_incluidos'];
                $procesados += $procesadosEnEsteLote;
                $resultadosPorTipo[$tipoDTE]['procesados'] += $procesadosEnEsteLote;
                $resultadosPorTipo[$tipoDTE]['sobres_generados']++;

                $intentoLog['exito'] = true;
                $intentoLog['sobre_id'] = $response['sobre_id'];
                $intentoLog['documentos_incluidos'] = $procesadosEnEsteLote;

                // Registrar éxito en la consola
                error_log("Sobre generado exitosamente - ID: " . $response['sobre_id']);
                error_log("Documentos incluidos: $procesadosEnEsteLote");

                if (isset($response['tipos_dte'])) {
                    error_log("Tipos DTE incluidos: " . implode(', ', $response['tipos_dte']));
                }

                // Actualizar el conteo de pendientes para este tipo
                $totalPendientes -= $procesadosEnEsteLote;
            } else {
                $errores++;
                $resultadosPorTipo[$tipoDTE]['errores']++;

                $intentoLog['exito'] = false;
                $intentoLog['error'] = $response['error'] ?? 'Error desconocido';

                // Registrar detalles del error en la consola
                error_log("ERROR al generar sobre para tipo DTE: $tipoDTE");
                error_log("Código HTTP: " . $result['code']);
                error_log("Mensaje de error: " . ($response['error'] ?? 'Error desconocido'));

                // Registrar información detallada sobre el error HTTP 404
                if ($result['code'] == 404) {
                    error_log("ERROR 404: Recurso no encontrado");
                    error_log("URL solicitada: " . ($result['info']['url'] ?? 'No disponible'));

                    // Verificar si hay información sobre archivos XML
                    if (isset($response['details']['diagnostico']['info_archivos'])) {
                        error_log("Información de archivos XML:");
                        foreach ($response['details']['diagnostico']['info_archivos'] as $archivo) {
                            error_log("  Archivo: " . basename($archivo['ruta']) .
                                      ", Existe: " . $archivo['existe'] .
                                      ", Tamaño: " . ($archivo['tamaño'] ?? 'N/A'));
                        }
                    }
                }

                // Registrar detalles adicionales si están disponibles
                if (isset($response['detalle_error'])) {
                    error_log("Detalle del error: " . $response['detalle_error']);
                    $intentoLog['detalle_error'] = $response['detalle_error'];
                } else {
                    // Si no hay detalle_error, crear uno más descriptivo basado en el código HTTP
                    if ($result['code'] == 404) {
                        $intentoLog['detalle_error'] = "Error 404: No se encontró el recurso solicitado. Verifique que los archivos XML existan en las rutas especificadas.";
                    } elseif ($result['code'] == 500) {
                        $intentoLog['detalle_error'] = "Error 500: Error interno del servidor al procesar la solicitud.";
                    } elseif ($result['code'] == 400) {
                        $intentoLog['detalle_error'] = "Error 400: Solicitud incorrecta. Verifique los parámetros enviados.";
                    } else {
                        $intentoLog['detalle_error'] = "Error HTTP " . $result['code'] . ": Ocurrió un problema al procesar la solicitud.";
                    }
                    error_log("Detalle generado: " . $intentoLog['detalle_error']);
                }

                // Registrar información sobre la respuesta completa
                error_log("Respuesta completa: " . substr($result['response'], 0, 500) . (strlen($result['response']) > 500 ? '...' : ''));

                if (isset($response['details'])) {
                    error_log("Diagnóstico: " . json_encode($response['details']));
                    $intentoLog['diagnostico'] = $response['details'];
                } else {
                    // Crear un diagnóstico básico si no hay uno disponible
                    $intentoLog['diagnostico'] = [
                        'http_code' => $result['code'],
                        'curl_error' => $result['error'] ?: 'ninguno',
                        'url' => $result['info']['url'] ?? 'No disponible',
                        'tiempo_respuesta' => $result['info']['total_time'] ?? 'No disponible',
                        'tamaño_respuesta' => $result['info']['size_download'] ?? 'No disponible'
                    ];
                    error_log("Diagnóstico generado: " . json_encode($intentoLog['diagnostico']));
                }

                if (isset($response['logs']) && is_array($response['logs'])) {
                    error_log("Logs del proceso:");
                    foreach ($response['logs'] as $idx => $log) {
                        $logMsg = "[{$log['type']}] {$log['message']}";
                        error_log("  Log #{$idx}: $logMsg");

                        // Si hay datos adicionales en el log y es un error, mostrarlos
                        if ($log['type'] == 'error' && isset($log['data'])) {
                            error_log("  Datos: " . json_encode($log['data']));
                        }
                    }
                    $intentoLog['logs'] = $response['logs'];
                }

                // Si hay un error, esperar 2 segundos antes de intentar el siguiente
                sleep(2);
            }

            $resultados[] = $intentoLog;

            // Salir del bucle después de generar un sobre
            break;
        }

        // Actualizar pendientes restantes para este tipo
        $resultadosPorTipo[$tipoDTE]['pendientes_restantes'] = $totalPendientes;
    }

    // Actualizar el estado de la solicitud si se usó un token
    if ($requestToken) {
        try {
            $status = ($errores > 0) ? 2 : 1; // 1 = completado con éxito, 2 = completado con errores
            $stmt = $conn->prepare("UPDATE tb_request_control SET status = :status, completed_at = NOW() WHERE request_token = :token");
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();
        } catch (Exception $updateEx) {
            // Solo registrar el error, no interrumpir el flujo principal
            error_log("Error al actualizar estado de solicitud: " . $updateEx->getMessage());
        }
    }

    // Preparar un mensaje detallado para la consola
    error_log("=== RESUMEN DEL PROCESAMIENTO DE SOBRES ===");
    error_log("Total documentos procesados: $procesados");
    error_log("Total errores: $errores");
    error_log("Total sobres generados: $sobresGenerados");

    // Registrar detalles por tipo de DTE
    error_log("Detalles por tipo de DTE:");
    foreach ($resultadosPorTipo as $tipo => $detalle) {
        error_log("  Tipo DTE $tipo: {$detalle['total_inicial']} iniciales, {$detalle['procesados']} procesados, {$detalle['errores']} errores, {$detalle['sobres_generados']} sobres generados");
    }

    // Registrar resultados detallados de cada intento
    error_log("Resultados detallados:");
    foreach ($resultados as $idx => $resultado) {
        $exito = isset($resultado['exito']) && $resultado['exito'] ? "ÉXITO" : "ERROR";
        error_log("  Intento #" . ($idx + 1) . " ($exito) - Tipo DTE: {$resultado['tipo_dte']}, Código HTTP: {$resultado['http_code']}");

        if (isset($resultado['error']) && $resultado['error'] !== 'ninguno') {
            error_log("    Error: {$resultado['error']}");

            // Si hay detalle del error, mostrarlo
            if (isset($resultado['detalle_error'])) {
                error_log("    Detalle: {$resultado['detalle_error']}");
            }

            // Si hay diagnóstico, mostrarlo
            if (isset($resultado['diagnostico'])) {
                error_log("    Diagnóstico: " . json_encode($resultado['diagnostico']));
            }
        }
    }

    // Incluir información detallada en la respuesta JSON
    echo json_encode([
        'success' => true,
        'procesados' => $procesados,
        'errores' => $errores,
        'sobres_generados' => $sobresGenerados,
        'detalle_por_tipo' => array_values($resultadosPorTipo),
        'resultados' => $resultados,
        'request_token' => $requestToken,
        // Agregar un mensaje claro para la interfaz
        'mensaje_detallado' => $errores > 0
            ? "Se encontraron $errores errores durante el procesamiento. Revise los logs para más detalles."
            : "Procesamiento completado con éxito. Se generaron $sobresGenerados sobres con $procesados documentos."
    ]);

} catch (Exception $e) {
    // Actualizar el estado de la solicitud en caso de error
    if (isset($requestToken)) {
        try {
            $stmt = $conn->prepare("UPDATE tb_request_control SET status = 2, completed_at = NOW() WHERE request_token = :token");
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();
        } catch (Exception $updateEx) {
            // Solo registrar el error
            error_log("Error al actualizar estado de solicitud: " . $updateEx->getMessage());
        }
    }

    echo json_encode([
        'error' => 'Error interno: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
