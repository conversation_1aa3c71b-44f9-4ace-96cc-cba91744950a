<?php
/**
 * Script de diagnóstico del sistema
 * Este script verifica problemas comunes y muestra información detallada para ayudar a diagnosticar errores
 */

// Configuración de visualización de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Incluir archivos necesarios
require_once 'db_connection.php';
require_once 'error_log_handler.php';

// Función para verificar permisos de directorios
function verificarPermisos($directorio) {
    $resultado = [
        'directorio' => $directorio,
        'existe' => file_exists($directorio) ? 'sí' : 'no',
        'permisos' => file_exists($directorio) ? decoct(fileperms($directorio) & 0777) : 'N/A',
        'is_readable' => is_readable($directorio) ? 'sí' : 'no',
        'is_writable' => is_writable($directorio) ? 'sí' : 'no'
    ];
    
    // Verificar si podemos crear un archivo de prueba
    if (file_exists($directorio) && is_dir($directorio)) {
        $archivoTest = $directorio . '/test_' . uniqid() . '.txt';
        $escritura = @file_put_contents($archivoTest, 'Test de escritura');
        $resultado['prueba_escritura'] = $escritura !== false ? 'exitosa' : 'fallida';
        
        if ($escritura !== false) {
            @unlink($archivoTest); // Eliminar archivo de prueba
        }
    }
    
    return $resultado;
}

// Función para verificar archivos XML pendientes
function verificarXMLPendientes($conn) {
    $resultado = [];
    
    try {
        $stmt = $conn->query("
            SELECT id, tipo_dte, nombre_archivo
            FROM tb_facturas_dte
            WHERE estado_sobre = 0
            LIMIT 20
        ");

        $pendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($pendientes as $doc) {
            $resultado[] = [
                'id' => $doc['id'],
                'tipo_dte' => $doc['tipo_dte'],
                'ruta' => $doc['nombre_archivo'],
                'existe' => file_exists($doc['nombre_archivo']) ? 'sí' : 'no',
                'tamaño' => file_exists($doc['nombre_archivo']) ? filesize($doc['nombre_archivo']) : 'N/A',
                'permisos' => file_exists($doc['nombre_archivo']) ? decoct(fileperms($doc['nombre_archivo']) & 0777) : 'N/A',
                'readable' => is_readable($doc['nombre_archivo']) ? 'sí' : 'no'
            ];
        }
    } catch (Exception $e) {
        $resultado = ['error' => $e->getMessage()];
    }
    
    return $resultado;
}

// Función para verificar la base de datos
function verificarBaseDatos($conn) {
    $resultado = [];
    
    try {
        // Verificar conexión
        $resultado['conexion'] = 'exitosa';
        
        // Verificar tablas
        $tablas = [
            'tb_facturas_dte',
            'tb_sobre_envios',
            'tb_sobre_documentos',
            'tb_error_logs',
            'tb_request_control'
        ];
        
        $tablasExistentes = [];
        foreach ($tablas as $tabla) {
            $stmt = $conn->query("SHOW TABLES LIKE '$tabla'");
            $tablasExistentes[$tabla] = $stmt->rowCount() > 0 ? 'existe' : 'no existe';
        }
        $resultado['tablas'] = $tablasExistentes;
        
        // Verificar registros en tb_error_logs
        if ($tablasExistentes['tb_error_logs'] === 'existe') {
            $stmt = $conn->query("SELECT COUNT(*) as total FROM tb_error_logs");
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            $resultado['errores_registrados'] = $count['total'];
            
            // Obtener los últimos 5 errores
            $stmt = $conn->query("SELECT * FROM tb_error_logs ORDER BY fecha DESC LIMIT 5");
            $resultado['ultimos_errores'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
    } catch (Exception $e) {
        $resultado = ['error' => $e->getMessage()];
    }
    
    return $resultado;
}

// Función para verificar archivos de log
function verificarLogs() {
    $resultado = [];
    
    // Verificar logs de PHP
    $phpErrorLog = ini_get('error_log');
    $resultado['php_error_log'] = [
        'ruta' => $phpErrorLog,
        'existe' => file_exists($phpErrorLog) ? 'sí' : 'no',
        'tamaño' => file_exists($phpErrorLog) ? filesize($phpErrorLog) : 'N/A',
        'readable' => is_readable($phpErrorLog) ? 'sí' : 'no'
    ];
    
    // Verificar nuestro log personalizado
    $customLog = 'sobres_error.log';
    $resultado['custom_error_log'] = [
        'ruta' => $customLog,
        'existe' => file_exists($customLog) ? 'sí' : 'no',
        'tamaño' => file_exists($customLog) ? filesize($customLog) : 'N/A',
        'readable' => is_readable($customLog) ? 'sí' : 'no'
    ];
    
    // Leer últimas líneas de los logs
    if (file_exists($customLog) && is_readable($customLog)) {
        $lines = [];
        $file = new SplFileObject($customLog, 'r');
        $file->seek(PHP_INT_MAX); // Ir al final del archivo
        $totalLines = $file->key(); // Obtener el número total de líneas
        
        // Leer las últimas 20 líneas o todas si hay menos de 20
        $linesToRead = min(20, $totalLines);
        $startLine = max(0, $totalLines - $linesToRead);
        
        $file->seek($startLine);
        while (!$file->eof()) {
            $lines[] = $file->fgets();
        }
        
        $resultado['custom_error_log']['ultimas_lineas'] = $lines;
    }
    
    return $resultado;
}

// Función para verificar la configuración del sistema
function verificarConfiguracion() {
    $resultado = [
        'php_version' => phpversion(),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'post_max_size' => ini_get('post_max_size'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'display_errors' => ini_get('display_errors'),
        'error_reporting' => ini_get('error_reporting'),
        'curl_enabled' => function_exists('curl_version') ? 'sí' : 'no',
        'json_enabled' => function_exists('json_encode') ? 'sí' : 'no',
        'pdo_enabled' => class_exists('PDO') ? 'sí' : 'no'
    ];
    
    if (function_exists('curl_version')) {
        $curl = curl_version();
        $resultado['curl_version'] = $curl['version'];
    }
    
    return $resultado;
}

// Ejecutar diagnóstico
try {
    $conn = getConnection();
    
    $diagnostico = [
        'timestamp' => date('Y-m-d H:i:s'),
        'directorios' => [
            'Documents/sobreEnvio/' => verificarPermisos('Documents/sobreEnvio/'),
            'Documents/folios/' => verificarPermisos('Documents/folios/'),
            'Documents/' => verificarPermisos('Documents/')
        ],
        'xml_pendientes' => verificarXMLPendientes($conn),
        'base_datos' => verificarBaseDatos($conn),
        'logs' => verificarLogs(),
        'configuracion' => verificarConfiguracion()
    ];
    
    // Guardar el diagnóstico en un archivo
    $diagnosticoFile = 'diagnostico_' . date('Ymd_His') . '.json';
    file_put_contents($diagnosticoFile, json_encode($diagnostico, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // Mostrar resultados
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'diagnostico' => $diagnostico,
        'archivo_guardado' => $diagnosticoFile
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
