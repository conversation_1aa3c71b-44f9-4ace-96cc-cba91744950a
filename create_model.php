
<?php
header('Content-Type: application/json');
require_once 'db_connection.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    $required_fields = ['modelo_id', 'nombre', 'anio_inicio', 'anio_fin'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("El campo $field es requerido");
        }
    }

    $conn = getConnection();
    
    $stmt = $conn->prepare("INSERT INTO modelo (marca_id, nombre, anio_inicio, anio_fin, activo) VALUES (?, ?, ?, ?, 1)");
    
    $stmt->execute([
        $_POST['marca_id'],
        $_POST['nombre'],
        $_POST['anio_inicio'],
        $_POST['anio_fin']
    ]);

    echo json_encode([
        'status' => 'success',
        'message' => 'Modelo creado exitosamente'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
