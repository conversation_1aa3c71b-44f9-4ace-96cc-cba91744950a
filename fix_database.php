<?php
// Script para verificar y corregir problemas de base de datos
require_once 'db_connection.php';

// Configurar manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar buffer de salida
ob_start();

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Verificación de Base de Datos</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 20px; margin-bottom: 20px; }
        h1 { color: #2c3e50; }
        h2 { color: #3498db; border-bottom: 1px solid #eee; padding-bottom: 10px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        button { background: #3498db; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <h1>Verificación y Reparación de Base de Datos</h1>";

try {
    echo "<h2>Verificando Conexión</h2>";
    echo "<p>Intentando conectar a la base de datos...</p>";
    
    $conn = getConnection();
    
    echo "<p class='success'>✓ Conexión establecida exitosamente.</p>";
    
    // Verificar si la tabla tb_upgrades existe
    echo "<h2>Verificando Tabla tb_upgrades</h2>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'tb_upgrades'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p class='success'>✓ La tabla tb_upgrades existe.</p>";
        
        // Verificar estructura de la tabla
        echo "<h3>Verificando Estructura</h3>";
        $stmt = $conn->query("DESCRIBE tb_upgrades");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = [
            'id', 'fecha_registro', 'pagina_modificada', 'descripcion', 
            'ruta_archivo', 'tipo_archivo', 'usuario'
        ];
        
        $missingColumns = array_diff($requiredColumns, $columnNames);
        
        if (empty($missingColumns)) {
            echo "<p class='success'>✓ La estructura de la tabla es correcta.</p>";
        } else {
            echo "<p class='warning'>⚠ Faltan columnas en la tabla: " . implode(', ', $missingColumns) . "</p>";
            
            // Añadir columnas faltantes
            echo "<h3>Corrigiendo Estructura</h3>";
            
            foreach ($missingColumns as $column) {
                try {
                    switch ($column) {
                        case 'ruta_archivo':
                            $conn->exec("ALTER TABLE tb_upgrades ADD COLUMN ruta_archivo VARCHAR(255)");
                            break;
                        case 'tipo_archivo':
                            $conn->exec("ALTER TABLE tb_upgrades ADD COLUMN tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno'");
                            break;
                        case 'usuario':
                            $conn->exec("ALTER TABLE tb_upgrades ADD COLUMN usuario VARCHAR(100) DEFAULT 'admin'");
                            break;
                    }
                    echo "<p class='success'>✓ Columna '$column' agregada correctamente.</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>✗ Error al agregar la columna '$column': " . $e->getMessage() . "</p>";
                }
            }
        }
    } else {
        echo "<p class='warning'>⚠ La tabla tb_upgrades no existe.</p>";
        
        // Crear la tabla
        echo "<h3>Creando Tabla</h3>";
        
        try {
            $createTableSql = "
                CREATE TABLE tb_upgrades (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    pagina_modificada VARCHAR(100) NOT NULL,
                    descripcion TEXT NOT NULL,
                    ruta_archivo VARCHAR(255),
                    tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno',
                    usuario VARCHAR(100) DEFAULT 'admin'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $conn->exec($createTableSql);
            echo "<p class='success'>✓ Tabla tb_upgrades creada exitosamente.</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error al crear la tabla: " . $e->getMessage() . "</p>";
        }
    }
    
    // Probar inserción en la tabla
    echo "<h2>Probando Inserción</h2>";
    
    try {
        $conn->beginTransaction();
        
        $stmt = $conn->prepare("
            INSERT INTO tb_upgrades 
            (pagina_modificada, descripcion, ruta_archivo, tipo_archivo) 
            VALUES (?, ?, ?, ?)
        ");
        
        $timestamp = date('YmdHis');
        $pagina = "test_fix_db_{$timestamp}.php";
        $descripcion = "Prueba de reparación de base de datos - " . date('Y-m-d H:i:s');
        $rutaArchivo = null;
        $tipoArchivo = 'ninguno';
        
        $result = $stmt->execute([$pagina, $descripcion, $rutaArchivo, $tipoArchivo]);
        
        if ($result) {
            $lastId = $conn->lastInsertId();
            echo "<p class='success'>✓ Inserción de prueba exitosa con ID: {$lastId}.</p>";
            
            // Verificar el registro insertado
            $verifyStmt = $conn->prepare("SELECT * FROM tb_upgrades WHERE id = ?");
            $verifyStmt->execute([$lastId]);
            $record = $verifyStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($record) {
                echo "<p class='success'>✓ Verificación de inserción exitosa.</p>";
                echo "<pre>" . print_r($record, true) . "</pre>";
                
                // Eliminar el registro de prueba
                $deleteStmt = $conn->prepare("DELETE FROM tb_upgrades WHERE id = ?");
                $deleteStmt->execute([$lastId]);
                echo "<p class='success'>✓ Registro de prueba eliminado.</p>";
            } else {
                echo "<p class='error'>✗ No se pudo verificar la inserción.</p>";
                $conn->rollBack();
            }
        } else {
            echo "<p class='error'>✗ Error en la inserción de prueba: " . print_r($stmt->errorInfo(), true) . "</p>";
            $conn->rollBack();
        }
        
        $conn->commit();
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        echo "<p class='error'>✗ Error en la prueba de inserción: " . $e->getMessage() . "</p>";
    }
    
    // Verificar permisos del directorio de imágenes
    echo "<h2>Verificando Directorio de Imágenes</h2>";
    
    $uploadDir = __DIR__ . '/images/upgrades/';
    
    if (!file_exists($uploadDir)) {
        echo "<p class='warning'>⚠ El directorio no existe: {$uploadDir}</p>";
        
        // Crear el directorio
        if (mkdir($uploadDir, 0777, true)) {
            echo "<p class='success'>✓ Directorio creado exitosamente.</p>";
        } else {
            echo "<p class='error'>✗ No se pudo crear el directorio.</p>";
        }
    } else {
        echo "<p class='success'>✓ El directorio existe: {$uploadDir}</p>";
        
        // Verificar permisos
        if (is_writable($uploadDir)) {
            echo "<p class='success'>✓ El directorio tiene permisos de escritura.</p>";
        } else {
            echo "<p class='warning'>⚠ El directorio no tiene permisos de escritura.</p>";
            
            // Intentar cambiar permisos
            if (chmod($uploadDir, 0777)) {
                echo "<p class='success'>✓ Permisos del directorio actualizados.</p>";
            } else {
                echo "<p class='error'>✗ No se pudieron cambiar los permisos del directorio.</p>";
            }
        }
    }
    
    // Resumen y recomendaciones
    echo "<h2>Resumen de Verificación</h2>";
    echo "<ul>";
    echo "<li class='success'>Conexión a la base de datos: OK</li>";
    echo "<li class='success'>Tabla tb_upgrades: " . ($tableExists ? "OK" : "Creada") . "</li>";
    echo "<li class='success'>Prueba de inserción: OK</li>";
    echo "<li class='success'>Directorio de imágenes: " . (file_exists($uploadDir) && is_writable($uploadDir) ? "OK" : "Problemas detectados") . "</li>";
    echo "</ul>";
    
    echo "<h3>Recomendaciones</h3>";
    echo "<ol>";
    echo "<li>Asegúrese de que el usuario de la base de datos tenga suficientes privilegios (INSERT, SELECT, CREATE).</li>";
    echo "<li>Verifique que la conexión a la base de datos sea estable y no esté limitada por el firewall.</li>";
    echo "<li>Revise los logs de PHP y MySQL para identificar cualquier error adicional.</li>";
    echo "</ol>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<button onclick='window.location.href=\"mod_config.php\"'>Volver al Módulo de Configuración</button>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>Error</h2>";
    echo "<p>Se ha producido un error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "        </div>
    </div>
</body>
</html>";

// Limpiar y enviar el buffer de salida
ob_end_flush();
