/**
 * Funcionalidad de autocompletado para campos SKU
 * Versión mejorada con diseño moderno
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar autocompletado para el campo SKU en el modal de entrada
    setTimeout(function() {
        initSKUAutocomplete();
    }, 500); // Pequeño retraso para asegurar que el DOM esté completamente cargado
});

/**
 * Inicializa la funcionalidad de autocompletado para el campo SKU
 */
function initSKUAutocomplete() {
    const skuInput = document.getElementById('repuesto_id');
    if (!skuInput) {
        console.error('Campo SKU no encontrado en el modal de entrada');
        return;
    }

    // Primero, verificar si ya existe un contenedor fijo
    let autocompleteContainer = document.getElementById('sku-autocomplete-container');

    // Si no existe, crearlo y añadirlo al DOM
    if (!autocompleteContainer) {
        autocompleteContainer = document.createElement('div');
        autocompleteContainer.id = 'sku-autocomplete-container';
        autocompleteContainer.className = 'autocomplete-container';

        // Añadirlo al body para evitar problemas de posicionamiento
        document.body.appendChild(autocompleteContainer);
    }

    // Función para actualizar la posición del contenedor
    const updateContainerPosition = () => {
        const rect = skuInput.getBoundingClientRect();
        autocompleteContainer.style.top = (rect.bottom + window.scrollY) + 'px';
        autocompleteContainer.style.left = (rect.left + window.scrollX) + 'px';
        autocompleteContainer.style.width = (rect.width) + 'px';
    };

    // Actualizar posición inicial
    updateContainerPosition();

    // Actualizar posición al hacer scroll o redimensionar
    window.addEventListener('scroll', updateContainerPosition);
    window.addEventListener('resize', updateContainerPosition);

    // Añadir un mensaje de ayuda debajo del campo
    const inputParent = skuInput.parentNode;

    // Eliminar mensaje de ayuda existente si hay
    const existingHelpText = inputParent.querySelector('.form-text');
    if (existingHelpText) {
        inputParent.removeChild(existingHelpText);
    }

    const helpText = document.createElement('small');
    helpText.className = 'form-text text-muted';
    helpText.innerHTML = 'Comience a escribir para ver sugerencias';
    helpText.style.marginTop = '4px';
    inputParent.appendChild(helpText);

    // Añadir clase de autocompletado al campo de entrada
    skuInput.classList.add('autocomplete-input');
    skuInput.placeholder = "Escriba para buscar...";

    // Variable para almacenar el timeout de búsqueda
    let searchTimeout;

    // Agregar evento de input para detectar cambios en el campo
    skuInput.addEventListener('input', function() {
        const searchValue = this.value.trim();

        // Limpiar timeout anterior
        clearTimeout(searchTimeout);

        // Ocultar el contenedor si el valor está vacío
        if (!searchValue) {
            autocompleteContainer.style.display = 'none';
            return;
        }

        // Establecer un timeout para evitar muchas peticiones
        searchTimeout = setTimeout(() => {
            // Realizar la búsqueda
            fetchSKUSuggestions(searchValue, autocompleteContainer, skuInput);
        }, 300);
    });

    // Cerrar el autocompletado al hacer clic fuera
    document.addEventListener('click', function(e) {
        if (e.target !== skuInput && !autocompleteContainer.contains(e.target)) {
            autocompleteContainer.style.display = 'none';
        }
    });

    // Manejar teclas de navegación
    skuInput.addEventListener('keydown', function(e) {
        if (autocompleteContainer.style.display === 'none') return;

        const items = autocompleteContainer.querySelectorAll('.autocomplete-item');
        if (!items.length) return;

        let activeItem = autocompleteContainer.querySelector('.autocomplete-item.active');
        let activeIndex = -1;

        if (activeItem) {
            activeIndex = Array.from(items).indexOf(activeItem);
        }

        // Tecla flecha abajo
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (activeItem) activeItem.classList.remove('active');

            activeIndex = (activeIndex + 1) % items.length;
            items[activeIndex].classList.add('active');
            items[activeIndex].scrollIntoView({ block: 'nearest' });
        }

        // Tecla flecha arriba
        else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (activeItem) activeItem.classList.remove('active');

            activeIndex = (activeIndex - 1 + items.length) % items.length;
            items[activeIndex].classList.add('active');
            items[activeIndex].scrollIntoView({ block: 'nearest' });
        }

        // Tecla Enter
        else if (e.key === 'Enter') {
            e.preventDefault();
            if (activeItem) {
                activeItem.click();
            }
        }

        // Tecla Escape
        else if (e.key === 'Escape') {
            autocompleteContainer.style.display = 'none';
        }
    });
}

/**
 * Busca sugerencias de SKU en el servidor
 * @param {string} searchValue - Valor de búsqueda
 * @param {HTMLElement} container - Contenedor donde mostrar resultados
 * @param {HTMLElement} input - Campo de entrada
 */
function fetchSKUSuggestions(searchValue, container, input) {
    // Actualizar la posición del contenedor antes de mostrarlo
    const rect = input.getBoundingClientRect();
    container.style.top = (rect.bottom + window.scrollY) + 'px';
    container.style.left = (rect.left + window.scrollX) + 'px';
    container.style.width = (rect.width) + 'px';

    // Mostrar pantalla de carga
    container.innerHTML = `
        <div class="autocomplete-title">
            <span>Buscando...</span>
        </div>
        <div class="autocomplete-loading">
            <div class="loading-spinner"></div>
            <div>Buscando "${searchValue}"</div>
        </div>
    `;

    // Mostrar el contenedor con animación
    container.style.display = 'block';
    setTimeout(() => {
        container.classList.add('visible');
    }, 10);

    const params = new URLSearchParams({
        search: searchValue
    });

    // Implementación mejorada para detectar la ruta correcta
    let baseUrl = '';

    // Método 1: Usar la ruta del documento actual
    if (window.location.pathname.includes('/')) {
        baseUrl = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
    }

    // Si estamos en la raíz, baseUrl será una cadena vacía, lo cual es correcto
    console.log('Base URL detectada:', baseUrl);

    const url = baseUrl + 'get_repuestos_SKU.php?' + params.toString();
    console.log('URL completa de búsqueda:', url);

    // Mostrar mensaje de depuración en el contenedor
    container.innerHTML += `
        <div style="padding: 5px; font-size: 10px; color: #999; text-align: center;">
            Conectando a: ${url}
        </div>
    `;

    // Implementar un timeout para la solicitud
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos de timeout

    fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        },
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);

        console.log('Respuesta recibida:', {
            status: response.status,
            statusText: response.statusText,
            headers: [...response.headers].map(h => `${h[0]}: ${h[1]}`).join(', ')
        });

        if (!response.ok) {
            // Intentar leer el cuerpo de la respuesta para obtener más detalles
            return response.text().then(text => {
                console.error('Cuerpo de la respuesta de error:', text);
                throw new Error(`Error HTTP ${response.status}: ${response.statusText}`);
            });
        }

        return response.json();
    })
    .then(data => {
        // Asegurarse de que el contenedor esté visible
        container.style.display = 'block';

        if (data.status === 'success' && data.data && data.data.length > 0) {
            // Mostrar sugerencias
            displaySuggestions(data.data, container, input, searchValue);
        } else {
            // No hay resultados
            container.innerHTML = `
                <div class="autocomplete-title">
                    <span>Resultados</span>
                    <span class="result-count">0</span>
                </div>
                <div class="autocomplete-no-results">
                    <i>🔍</i>
                    <div>No se encontraron resultados para "${searchValue}"</div>
                </div>
            `;
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);

        // Determinar el tipo de error para mostrar un mensaje más específico
        let errorTitle = 'Error';
        let errorMessage = 'Error al buscar sugerencias';
        let errorDetail = error.message;
        let errorIcon = '⚠️';

        if (error.name === 'AbortError') {
            errorTitle = 'Tiempo de espera agotado';
            errorMessage = 'La solicitud tardó demasiado en responder';
            errorDetail = 'Verifique su conexión a internet o inténtelo más tarde';
            errorIcon = '⏱️';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorTitle = 'Error de red';
            errorMessage = 'No se pudo conectar al servidor';
            errorDetail = 'Verifique su conexión a internet o la disponibilidad del servidor';
            errorIcon = '🌐';
        } else if (error.message.includes('404')) {
            errorTitle = 'Archivo no encontrado';
            errorMessage = 'El archivo PHP no existe en la ruta especificada';
            errorDetail = 'Ruta: ' + url;
            errorIcon = '🔍';
        } else if (error.message.includes('500')) {
            errorTitle = 'Error del servidor';
            errorMessage = 'Error interno en el servidor';
            errorDetail = 'Contacte al administrador del sistema';
            errorIcon = '🔧';
        }

        console.error('Error al buscar sugerencias de SKU:', {
            error: error,
            type: error.name,
            message: error.message,
            url: url
        });

        container.innerHTML = `
            <div class="autocomplete-title" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                <span>${errorTitle}</span>
            </div>
            <div class="autocomplete-no-results">
                <i>${errorIcon}</i>
                <div>${errorMessage}</div>
                <div style="font-size: 0.85em; margin-top: 5px; opacity: 0.7;">${errorDetail}</div>
                <div style="font-size: 0.75em; margin-top: 10px; color: #999; border-top: 1px solid #eee; padding-top: 8px;">
                    URL: ${url}<br>
                    Tiempo: ${new Date().toLocaleTimeString()}
                </div>
            </div>
        `;
    });
}

/**
 * Muestra las sugerencias en el contenedor
 * @param {Array} suggestions - Lista de sugerencias
 * @param {HTMLElement} container - Contenedor donde mostrar resultados
 * @param {HTMLElement} input - Campo de entrada
 * @param {string} searchTerm - Término de búsqueda para resaltar coincidencias
 */
function displaySuggestions(suggestions, container, input, searchTerm) {
    // Limpiar contenedor
    container.innerHTML = '';

    // Añadir título con contador de resultados
    container.innerHTML = `
        <div class="autocomplete-title">
            <span>Resultados</span>
            <span class="result-count">${suggestions.length}</span>
        </div>
    `;

    // Crear elementos para cada sugerencia
    suggestions.forEach((item) => {
        const suggestionItem = document.createElement('div');
        suggestionItem.className = 'autocomplete-item';

        // Preparar los datos para mostrar
        let skuDisplay = item.sku || 'Sin SKU';
        let nombreDisplay = item.nombre || 'Sin nombre';
        let precioDisplay = item.precio ? `$${parseInt(item.precio).toLocaleString()}` : 'Precio no disponible';

        // Resaltar coincidencias si hay término de búsqueda
        if (searchTerm) {
            const searchTermLower = searchTerm.toLowerCase();

            // Resaltar coincidencias en SKU
            if (skuDisplay.toLowerCase().includes(searchTermLower)) {
                const regex = new RegExp(searchTerm, 'gi');
                skuDisplay = skuDisplay.replace(regex, match => `<mark>${match}</mark>`);
            }

            // Resaltar coincidencias en nombre
            if (nombreDisplay.toLowerCase().includes(searchTermLower)) {
                const regex = new RegExp(searchTerm, 'gi');
                nombreDisplay = nombreDisplay.replace(regex, match => `<mark>${match}</mark>`);
            }
        }

        // Preparar imagen o placeholder
        const imageSrc = item.url_imagen ? item.url_imagen : '';
        const imageHtml = imageSrc
            ? `<img src="${imageSrc}" alt="${item.sku}" class="autocomplete-item-image">`
            : `<div class="autocomplete-item-image">📦</div>`;

        // Construir el HTML del item
        suggestionItem.innerHTML = `
            ${imageHtml}
            <div class="autocomplete-item-content">
                <div class="suggestion-sku">${skuDisplay}</div>
                <div class="suggestion-name">${nombreDisplay}</div>
                <div class="suggestion-price">${precioDisplay}</div>
            </div>
        `;

        // Almacenar datos del repuesto en el elemento
        suggestionItem.dataset.id = item.id;
        suggestionItem.dataset.sku = item.sku;
        suggestionItem.dataset.nombre = item.nombre || '';
        suggestionItem.dataset.precio = item.precio || '';

        // Agregar evento de clic
        suggestionItem.addEventListener('click', function() {
            // Mostrar el SKU en el campo de entrada
            input.value = this.dataset.sku;

            // Almacenar el ID del repuesto para usarlo al agregar
            input.dataset.repuestoId = this.dataset.id;
            input.dataset.repuestoNombre = this.dataset.nombre;
            input.dataset.repuestoPrecio = this.dataset.precio;

            // Ocultar el contenedor con animación
            container.classList.remove('visible');
            setTimeout(() => {
                container.style.display = 'none';
            }, 300);

            // Enfocar el campo de cantidad
            const cantidadInput = document.getElementById('cantidad');
            if (cantidadInput) {
                cantidadInput.focus();
            }

            // Disparar un evento personalizado
            const event = new CustomEvent('repuestoSelected', {
                detail: {
                    id: this.dataset.id,
                    sku: this.dataset.sku,
                    nombre: this.dataset.nombre,
                    precio: this.dataset.precio
                }
            });
            input.dispatchEvent(event);
        });

        // Agregar al contenedor
        container.appendChild(suggestionItem);
    });

    // Si no hay sugerencias, mostrar un mensaje (aunque esto no debería ocurrir aquí)
    if (suggestions.length === 0) {
        container.innerHTML += `
            <div class="autocomplete-no-results">
                <i>🔍</i>
                <div>No se encontraron resultados</div>
            </div>
        `;
    }
}
