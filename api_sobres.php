<?php
// API para gestionar sobres de envío
header('Content-Type: application/json');
require_once 'db_connection.php';
require_once 'constants.php';

// Obtener la acción solicitada
$action = $_GET['action'] ?? '';

try {
    $conn = getConnection();
    
    switch ($action) {
        case 'list':
            // Listar todos los sobres
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
            
            $stmt = $conn->prepare("
                SELECT * FROM tb_sobre_envios 
                ORDER BY fecha DESC 
                LIMIT :limit
            ");
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            $sobres = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Agregar texto de estado a cada sobre
            foreach ($sobres as &$sobre) {
                // Convert estado_envio to integer to ensure proper indexing
                $estadoInt = (int)$sobre['estado_envio'];
                
                // Add a text representation of the status
                $sobre['estado_texto'] = $ESTADO_ENVIO_TEXTO[$estadoInt] ?? 'Desconocido';
            }
            
            // Obtener el total de sobres
            $stmt = $conn->query("SELECT COUNT(*) as total FROM tb_sobre_envios");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo json_encode([
                'success' => true,
                'sobres' => $sobres,
                'total_sobres' => $total
            ]);
            break;
            
        case 'pending_count':
            // Contar documentos pendientes de envío, limitado a 2 por tipo
            $stmt = $conn->query("
                SELECT
                    tipo_dte,
                    CASE
                        WHEN COUNT(*) > 2 THEN 2
                        ELSE COUNT(*)
                    END as cantidad
                FROM tb_facturas_dte
                WHERE estado_sobre = 0
                GROUP BY tipo_dte
            ");
            $pendingByType = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'pending_by_type' => $pendingByType
            ]);
            break;
            
        case 'pending_detail':
            // Obtener detalles de documentos pendientes agrupados por tipo_dte
            $stmt = $conn->query("
                SELECT tipo_dte, COUNT(*) as cantidad
                FROM tb_facturas_dte
                WHERE estado_sobre = 0
                GROUP BY tipo_dte
                ORDER BY cantidad DESC
            ");

            $pendingByType = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'pending_by_type' => $pendingByType
            ]);
            break;
            
        case 'get':
            // Obtener un sobre específico
            $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
            
            if ($id <= 0) {
                echo json_encode(['error' => 'ID de sobre no válido']);
                exit;
            }
            
            $stmt = $conn->prepare("SELECT * FROM tb_sobre_envios WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            $sobre = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$sobre) {
                echo json_encode(['error' => 'Sobre no encontrado']);
                exit;
            }
            
            echo json_encode([
                'success' => true,
                'sobre' => $sobre
            ]);
            break;
            
        case 'update_status':
            // Actualizar el estado de un sobre
            // Esta acción requiere método POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['error' => 'Se requiere método POST']);
                exit;
            }
            
            $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
            $status = $_POST['status'] ?? '';
            
            if ($id <= 0 || empty($status)) {
                echo json_encode(['error' => 'Parámetros inválidos']);
                exit;
            }
            
            $stmt = $conn->prepare("UPDATE tb_sobre_envios SET estado_envio = :status WHERE id = :id");
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            
            if ($stmt->rowCount() === 0) {
                echo json_encode(['error' => 'No se pudo actualizar el sobre o no se encontró']);
                exit;
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Estado actualizado correctamente'
            ]);
            break;
            
        default:
            echo json_encode(['error' => 'Acción no válida']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'Error interno: ' . $e->getMessage()
    ]);
}
?>
