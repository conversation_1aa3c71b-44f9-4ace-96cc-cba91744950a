<?php
/**
 * Funciones para manejo de tokens CSRF
 * Incluir este archivo en cualquier script que necesite protección CSRF
 */

/**
 * Genera un nuevo token CSRF y lo almacena en la sesión
 * @return string Token CSRF generado
 */
function generateCsrfToken() {
    // Generar un token aleatorio de 32 bytes (64 caracteres hexadecimales)
    $token = bin2hex(random_bytes(32));
    
    // Almacenar el token en la sesión
    $_SESSION['csrf_token'] = $token;
    
    // Registrar la generación del token para depuración
    error_log('Nuevo token CSRF generado: ' . substr($token, 0, 8) . '...');
    
    return $token;
}

/**
 * Obtiene el token CSRF actual o genera uno nuevo si no existe
 * @return string Token CSRF
 */
function getCsrfToken() {
    // Si no hay token o está vacío, generar uno nuevo
    if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
        return generateCsrfToken();
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verifica si el token CSRF enviado es válido
 * @param string $token Token CSRF a verificar
 * @return bool True si el token es válido, False en caso contrario
 */
function verifyCsrfToken($token) {
    // Si no hay token almacenado en la sesión, la verificación falla
    if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
        error_log('Error CSRF: No hay token en la sesión');
        return false;
    }
    
    // Comparar el token enviado con el almacenado
    $valid = hash_equals($_SESSION['csrf_token'], $token);
    
    // Registrar el resultado de la verificación para depuración
    error_log('Verificación CSRF: ' . ($valid ? 'Válido' : 'Inválido') . 
              ' (Recibido: ' . substr($token, 0, 8) . '..., ' . 
              'Esperado: ' . substr($_SESSION['csrf_token'], 0, 8) . '...)');
    
    return $valid;
}

/**
 * Genera un campo de formulario hidden con el token CSRF
 * @return string HTML del campo hidden
 */
function csrfTokenField() {
    $token = getCsrfToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
}

/**
 * Genera etiquetas meta con el token CSRF para usar con JavaScript
 * @return string HTML de la etiqueta meta
 */
function csrfTokenMeta() {
    $token = getCsrfToken();
    return '<meta name="csrf-token" content="' . htmlspecialchars($token) . '">';
}

/**
 * Verifica el token CSRF en una solicitud POST
 * @return bool True si la verificación es exitosa, False en caso contrario
 */
function verifyCsrfTokenPost() {
    // Verificar si es una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return true; // No es POST, no necesita verificación
    }
    
    // Verificar si se envió el token
    if (!isset($_POST['csrf_token'])) {
        error_log('Error CSRF: No se envió token en POST');
        return false;
    }
    
    return verifyCsrfToken($_POST['csrf_token']);
}

/**
 * Verifica el token CSRF en headers HTTP (para solicitudes AJAX)
 * @return bool True si la verificación es exitosa, False en caso contrario
 */
function verifyCsrfTokenHeader() {
    // Obtener el token del header
    $token = null;
    
    // Buscar en diferentes posibles headers
    if (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
        $token = $_SERVER['HTTP_X_CSRF_TOKEN'];
    } elseif (isset($_SERVER['HTTP_X_XSRF_TOKEN'])) {
        $token = $_SERVER['HTTP_X_XSRF_TOKEN'];
    } elseif (function_exists('apache_request_headers')) {
        $headers = apache_request_headers();
        if (isset($headers['X-CSRF-Token'])) {
            $token = $headers['X-CSRF-Token'];
        } elseif (isset($headers['X-XSRF-Token'])) {
            $token = $headers['X-XSRF-Token'];
        }
    }
    
    // Si no hay token, verificación fallida
    if ($token === null) {
        error_log('Error CSRF: No se encontró token en headers');
        return false;
    }
    
    return verifyCsrfToken($token);
}

/**
 * Verifica el token CSRF en solicitudes POST o AJAX
 * @return bool True si la verificación es exitosa, False en caso contrario
 */
function validateCsrfToken() {
    // Para solicitudes AJAX, verificar el header
    $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
              strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    
    if ($isAjax) {
        return verifyCsrfTokenHeader();
    }
    
    // Para solicitudes POST normales, verificar en $_POST
    return verifyCsrfTokenPost();
}

/**
 * Script para incluir en archivos PHP que procesen formularios o peticiones AJAX
 * Uso: require_once 'csrf_token.php'; checkCsrfToken();
 */
function checkCsrfToken() {
    // Verificar si es una solicitud que modifica datos (POST, PUT, DELETE)
    $method = $_SERVER['REQUEST_METHOD'];
    if ($method === 'GET') {
        return true; // No verificar GET para evitar problemas con caching
    }
    
    // Verificar el token
    if (!validateCsrfToken()) {
        // Responder con error 403 Forbidden
        http_response_code(403);
        
        // Si es una solicitud AJAX, devolver JSON
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'Error de validación CSRF. Por favor, recargue la página e intente nuevamente.'
            ]);
        } else {
            // Para solicitudes normales, mostrar página de error
            echo '<html><head><title>Error 403 - Forbidden</title></head><body>';
            echo '<h1>Error 403 - Forbidden</h1>';
            echo '<p>Error de validación CSRF. Por favor, <a href="javascript:history.back()">regrese</a> e intente nuevamente.</p>';
            echo '</body></html>';
        }
        
        exit;
    }
    
    return true;
}

/**
 * Función para usar al inicio de scripts que procesan datos sensibles
 * Verifica sesión y CSRF token en un solo paso
 */
function validateSessionAndCsrf() {
    // Verificar si el usuario está logueado
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        // Responder con error 401 Unauthorized
        http_response_code(401);
        
        // Si es una solicitud AJAX, devolver JSON
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'No autorizado. Por favor, inicie sesión nuevamente.'
            ]);
        } else {
            // Para solicitudes normales, redirigir al login
            header('Location: login.php');
        }
        
        exit;
    }
    
    // Verificar CSRF token
    checkCsrfToken();
    
    return true;
}
?>