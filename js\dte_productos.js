// Función para recopilar los productos del formulario DTE
function recopilarProductosDTE() {
    const productos = [];
    const itemRows = document.querySelectorAll('#itemsContainer .item-row');
    let totalProductos = 0;
    let productosConId = 0;

    itemRows.forEach(row => {
        totalProductos++;

        // Obtener el ID del producto (si existe)
        const dataRepuestoId = row.getAttribute('data-repuesto-id');
        const inputField = row.querySelector('.repuesto-id');
        const inputRepuestoId = inputField ? inputField.value : null;

        // Inspeccionar el HTML del elemento para depuración
        const rowHtml = row.outerHTML;
        const rowHtmlShort = rowHtml.length > 100 ? rowHtml.substring(0, 100) + '...' : rowHtml;

        console.log('Debug ID para fila ' + totalProductos + ':', {
            dataRepuestoId: dataRepuestoId,
            inputRepuestoId: inputRepuestoId,
            hasDataAttribute: row.hasAttribute('data-repuesto-id'),
            hasInputField: !!inputField,
            rowHtmlShort: rowHtmlShort
        });

        // Usar el ID del atributo data o del campo oculto
        const repuestoId = dataRepuestoId || inputRepuestoId;

        // Obtener los valores de los campos
        const cantidad = parseInt(row.querySelector('.item-cantidad')?.value) || 0;
        const precioUnitario = parseFloat(row.querySelector('.item-precio')?.value) || 0;
        const montoItem = parseFloat(row.querySelector('.item-monto')?.value) || 0;

        // Obtener el nombre y descripción del producto
        const nombreProducto = row.querySelector('.item-nombre')?.value || 'Producto sin nombre';
        const descripcionProducto = row.querySelector('.item-descripcion')?.value || '';

        // Contar productos con ID
        if (repuestoId) {
            productosConId++;
        }

        // Agregar el producto a la lista (con o sin ID)
        productos.push({
            repuesto_id: repuestoId || null,
            nombre_producto: nombreProducto,
            descripcion_producto: descripcionProducto,
            cantidad: cantidad,
            precio_unitario: precioUnitario,
            monto_item: montoItem
        });
    });

    console.log(`Total de productos en el DTE: ${totalProductos}, Productos con ID: ${productosConId}`);

    return {
        productos: productos,
        totalProductos: totalProductos,
        productosConId: productosConId
    };
}

// Función para guardar los productos en la base de datos
async function guardarProductosDTE(dteId) {
    try {
        // Recopilar los productos del formulario
        const resultado = recopilarProductosDTE();
        const { productos, totalProductos, productosConId } = resultado;

        // Si no hay productos, no hacer nada
        if (productos.length === 0) {
            console.log('No hay productos en el DTE para guardar');
            return { success: false, message: 'No hay productos en el DTE para guardar' };
        }

        // Preparar los datos para enviar al servidor
        const data = {
            dte_id: dteId,
            productos: productos,
            total_productos: totalProductos
        };

        // Enviar los datos al servidor
        const response = await fetch('guardar_dte_productos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        // Procesar la respuesta
        const result = await response.json();

        if (result.status === 'success') {
            console.log('Productos guardados correctamente:', result);

            // Mensaje personalizado según si todos o solo algunos productos tenían ID
            let mensaje = `Se guardaron correctamente ${result.productos_guardados} productos`;
            if (productosConId > 0 && productosConId < totalProductos) {
                mensaje += ` (${productosConId} con ID de repuesto y ${totalProductos - productosConId} sin ID)`;
            }

            // Registrar las salidas de inventario para los productos con ID
            if (productosConId > 0) {
                try {
                    const resultadoSalida = await registrarSalidaInventario(dteId, productos);
                    console.log('Resultado de registrar salidas de inventario:', resultadoSalida);

                    if (resultadoSalida.success) {
                        mensaje += `. ${resultadoSalida.message}`;
                    } else {
                        mensaje += `. Advertencia: ${resultadoSalida.message}`;
                    }
                } catch (errorSalida) {
                    console.error('Error al registrar salidas de inventario:', errorSalida);
                    mensaje += `. Error al actualizar el inventario: ${errorSalida.message}`;
                }
            }

            return {
                success: true,
                message: mensaje,
                productosGuardados: result.productos_guardados,
                totalProductos: totalProductos,
                productosConId: productosConId
            };
        } else {
            console.error('Error al guardar los productos:', result);
            return {
                success: false,
                message: 'Error al guardar los productos: ' + result.message,
                productosConId: productosConId,
                totalProductos: totalProductos
            };
        }
    } catch (error) {
        console.error('Error al guardar los productos:', error);
        return { success: false, message: 'Error al guardar los productos: ' + error.message };
    }
}

// Función para registrar salidas de inventario
async function registrarSalidaInventario(dteId, productos) {
    try {
        console.log('🚀 === INICIANDO ACTUALIZACIÓN DE INVENTARIO PARA DTE ===');
        console.log('🆔 DTE ID:', dteId);
        console.log('⏰ Timestamp:', new Date().toISOString());

        // Verificar si se debe actualizar el inventario
        console.log('⚙️ Verificando configuración de inventario...');
        const enableInventario = document.getElementById('enableInventarioCheckbox')?.checked;
        console.log('⚙️ Inventario habilitado:', enableInventario);
        
        if (!enableInventario) {
            console.log('⚠️ Actualización de inventario desactivada por el usuario');
            return {
                success: true,
                message: 'No se actualizó el inventario (desactivado por el usuario)'
            };
        }

        // Filtrar solo los productos que tienen ID de repuesto
        console.log('🔍 Filtrando productos con ID de repuesto...');
        console.log('🔍 Total productos recibidos:', productos.length);
        const productosConId = productos.filter(producto => producto.repuesto_id);
        console.log('🔍 Productos con ID encontrados:', productosConId.length);
        console.log('🔍 Productos filtrados:', JSON.stringify(productosConId, null, 2));

        if (productosConId.length === 0) {
            console.log('⚠️ No hay productos con ID para registrar salida de inventario');
            return {
                success: true,
                message: 'No se registraron salidas de inventario (ningún producto tiene ID)'
            };
        }

        // Obtener el ID del almacén seleccionado
        console.log('🏢 Obteniendo almacén seleccionado...');
        const almacenSelect = document.getElementById('almacen_id');
        const almacenId = almacenSelect?.value || 1; // Valor por defecto si no está disponible
        console.log('🏢 Almacén ID obtenido:', almacenId);

        // Preparar los datos para enviar al servidor
        console.log('📦 Preparando datos para envío al servidor...');
        const data = {
            dte_id: dteId,
            productos: productosConId,
            // Obtener el ID del usuario si está disponible en el sistema
            usuario_id: window.usuarioId || 1, // Valor por defecto si no está disponible
            almacen_id: almacenId
        };
        console.log('📦 Datos preparados:', JSON.stringify(data, null, 2));

        // Enviar los datos al servidor
        console.log('🌐 Enviando solicitud al servidor...');
        console.log('🌐 URL destino: registrar_salida_inventario.php');
        console.log('🌐 Método: POST');
        console.log('🌐 Cuerpo de la solicitud:', JSON.stringify(data));
        
        const response = await fetch('registrar_salida_inventario.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(data)
        });
        
        console.log('🌐 Respuesta del servidor recibida');
        console.log('🌐 Status:', response.status);
        console.log('🌐 StatusText:', response.statusText);

        // Verificar si la respuesta es exitosa
        console.log('✅ Verificando si la respuesta es exitosa...');
        if (!response.ok) {
            console.log('❌ Error en la respuesta del servidor');
            throw new Error(`Error en la respuesta del servidor: ${response.status} ${response.statusText}`);
        }
        console.log('✅ Respuesta del servidor exitosa');

        // Procesar la respuesta
        console.log('📖 Procesando respuesta JSON...');
        const responseText = await response.text();
        console.log('📖 Texto de respuesta crudo:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('📖 JSON parseado exitosamente:', JSON.stringify(result, null, 2));
        } catch (parseError) {
            console.log('❌ Error al parsear JSON:', parseError);
            throw new Error(`Error al parsear respuesta JSON: ${parseError.message}`);
        }

        if (result.status === 'success') {
            console.log('🎉 Actualización de inventario exitosa!');
            console.log('🎉 Mensaje del servidor:', result.message);
            console.log('🎉 Productos registrados:', result.productos_registrados);
            console.log('🎉 Productos omitidos:', result.productos_omitidos);

            // Verificar si hubo errores de stock
            if (result.errores_stock && result.errores_stock.length > 0) {
                console.log('⚠️ Se encontraron errores de stock:');
                result.errores_stock.forEach((error, index) => {
                    console.log(`⚠️ Error ${index + 1}: ${error.nombre} - Solicitado: ${error.cantidad_solicitada}, Disponible: ${error.stock_disponible}`);
                });
                
                // Crear mensaje de advertencia con los productos que no tenían stock suficiente
                let mensajeError = 'Algunos productos no tenían stock suficiente: ';
                result.errores_stock.forEach((error, index) => {
                    mensajeError += `${error.nombre} (solicitado: ${error.cantidad_solicitada}, disponible: ${error.stock_disponible})`;
                    if (index < result.errores_stock.length - 1) {
                        mensajeError += ', ';
                    }
                });

                return {
                    success: false,
                    message: mensajeError,
                    detalles: result
                };
            }

            console.log('✅ Actualización de inventario completada exitosamente');
            return {
                success: true,
                message: `Se actualizó el inventario para ${result.productos_registrados} productos`,
                detalles: result
            };
        } else {
            console.log('❌ Error en la actualización de inventario');
            console.error('❌ Detalles del error:', result);
            return {
                success: false,
                message: 'Error al actualizar el inventario: ' + result.message,
                detalles: result
            };
        }
    } catch (error) {
        console.log('❌ === ERROR EN ACTUALIZACIÓN DE INVENTARIO ===');
        console.error('❌ Error capturado:', error);
        console.error('❌ Stack trace:', error.stack);
        return {
            success: false,
            message: 'Error al actualizar el inventario: ' + error.message
        };
    }
}
