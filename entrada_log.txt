[2025-04-26 04:04:19][INFO] Nueva solicitud recibida
[2025-04-26 04:04:19][INFO] Método HTTP: POST
[2025-04-26 04:04:19][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.5","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=ro9r207lbga3ntmittoep9bkrg"}
[2025-04-26 04:04:19][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:04:19][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:04:19][INFO] Conexión a base de datos establecida
[2025-04-26 04:04:19][INFO] Iniciando transacción
[2025-04-26 04:04:19][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-04-26 04:04:19][INFO] Procesando repuesto: {"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}
[2025-04-26 04:04:19][INFO] Movimiento insertado correctamente para repuesto ID: 12
[2025-04-26 04:04:19][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 12
[2025-04-26 04:04:19][INFO] Transacción completada exitosamente
[2025-04-26 04:04:19][INFO] Conexión cerrada
[2025-04-26 04:04:46][INFO] Nueva solicitud recibida
[2025-04-26 04:04:46][INFO] Método HTTP: POST
[2025-04-26 04:04:46][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.5","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=ro9r207lbga3ntmittoep9bkrg"}
[2025-04-26 04:04:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:04:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:04:46][INFO] Conexión a base de datos establecida
[2025-04-26 04:04:46][INFO] Iniciando transacción
[2025-04-26 04:04:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-04-26 04:04:46][INFO] Procesando repuesto: {"repuesto_id":12,"cantidad":12,"nombre":"KIT DE DISTRIBUCION"}
[2025-04-26 04:04:46][INFO] Movimiento insertado correctamente para repuesto ID: 12
[2025-04-26 04:04:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 12
[2025-04-26 04:04:46][INFO] Transacción completada exitosamente
[2025-04-26 04:04:46][INFO] Conexión cerrada
[2025-04-26 04:14:52][INFO] Nueva solicitud recibida
[2025-04-26 04:14:52][INFO] Método HTTP: POST
[2025-04-26 04:14:52][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.5","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=ro9r207lbga3ntmittoep9bkrg"}
[2025-04-26 04:14:52][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":123,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:14:52][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":12,"cantidad":123,"nombre":"KIT DE DISTRIBUCION"}]}
[2025-04-26 04:14:52][INFO] Conexión a base de datos establecida
[2025-04-26 04:14:52][INFO] Iniciando transacción
[2025-04-26 04:14:52][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-04-26 04:14:52][INFO] Procesando repuesto: {"repuesto_id":12,"cantidad":123,"nombre":"KIT DE DISTRIBUCION"}
[2025-04-26 04:14:52][INFO] Movimiento insertado correctamente para repuesto ID: 12
[2025-04-26 04:14:52][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 12
[2025-04-26 04:14:52][INFO] Transacción completada exitosamente
[2025-04-26 04:14:52][INFO] Conexión cerrada
[2025-04-30 13:17:24][INFO] Nueva solicitud recibida
[2025-04-30 13:17:24][INFO] Método HTTP: POST
[2025-04-30 13:17:24][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"258","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.5","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=hre99adnp1gsoa07p966fj9pm1"}
[2025-04-30 13:17:24][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-0001","notas":"Regularizacion stock","repuestos":[{"repuesto_id":12,"cantidad":15,"nombre":"KIT DE DISTRIBUCION"},{"repuesto_id":22,"cantidad":30,"nombre":"VALVULAS DE ESCAPE JUEGO 4U"}]}
[2025-04-30 13:17:24][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-0001","notas":"Regularizacion stock","repuestos":[{"repuesto_id":12,"cantidad":15,"nombre":"KIT DE DISTRIBUCION"},{"repuesto_id":22,"cantidad":30,"nombre":"VALVULAS DE ESCAPE JUEGO 4U"}]}
[2025-04-30 13:17:24][INFO] Conexión a base de datos establecida
[2025-04-30 13:17:24][INFO] Iniciando transacción
[2025-04-30 13:17:24][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-04-30 13:17:24][INFO] Procesando repuesto: {"repuesto_id":12,"cantidad":15,"nombre":"KIT DE DISTRIBUCION"}
[2025-04-30 13:17:24][INFO] Movimiento insertado correctamente para repuesto ID: 12
[2025-04-30 13:17:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 12
[2025-04-30 13:17:24][INFO] Procesando repuesto: {"repuesto_id":22,"cantidad":30,"nombre":"VALVULAS DE ESCAPE JUEGO 4U"}
[2025-04-30 13:17:24][INFO] Movimiento insertado correctamente para repuesto ID: 22
[2025-04-30 13:17:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 22
[2025-04-30 13:17:24][INFO] Transacción completada exitosamente
[2025-04-30 13:17:24][INFO] Conexión cerrada
[2025-05-05 15:16:16][INFO] Nueva solicitud recibida
[2025-05-05 15:16:16][INFO] Método HTTP: POST
[2025-05-05 15:16:16][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:16:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":23,"cantidad":2,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:16:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":23,"cantidad":2,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:16:16][INFO] Conexión a base de datos establecida
[2025-05-05 15:16:16][INFO] Iniciando transacción
[2025-05-05 15:16:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:16:16][INFO] Procesando repuesto: {"repuesto_id":23,"cantidad":2,"nombre":"Kit de Distribucion"}
[2025-05-05 15:16:16][INFO] Movimiento insertado correctamente para repuesto ID: 23
[2025-05-05 15:16:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 23
[2025-05-05 15:16:16][INFO] Transacción completada exitosamente
[2025-05-05 15:16:16][INFO] Conexión cerrada
[2025-05-05 15:36:32][INFO] Nueva solicitud recibida
[2025-05-05 15:36:32][INFO] Método HTTP: POST
[2025-05-05 15:36:32][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"175","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:36:32][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":24,"cantidad":1,"nombre":"Kit de Accesorio con Correa y Tensor"}]}
[2025-05-05 15:36:32][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":24,"cantidad":1,"nombre":"Kit de Accesorio con Correa y Tensor"}]}
[2025-05-05 15:36:32][INFO] Conexión a base de datos establecida
[2025-05-05 15:36:32][INFO] Iniciando transacción
[2025-05-05 15:36:32][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:36:32][INFO] Procesando repuesto: {"repuesto_id":24,"cantidad":1,"nombre":"Kit de Accesorio con Correa y Tensor"}
[2025-05-05 15:36:32][INFO] Movimiento insertado correctamente para repuesto ID: 24
[2025-05-05 15:36:32][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 24
[2025-05-05 15:36:32][INFO] Transacción completada exitosamente
[2025-05-05 15:36:32][INFO] Conexión cerrada
[2025-05-05 15:43:46][INFO] Nueva solicitud recibida
[2025-05-05 15:43:46][INFO] Método HTTP: POST
[2025-05-05 15:43:46][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:43:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":25,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:43:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":25,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:43:46][INFO] Conexión a base de datos establecida
[2025-05-05 15:43:46][INFO] Iniciando transacción
[2025-05-05 15:43:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:43:46][INFO] Procesando repuesto: {"repuesto_id":25,"cantidad":1,"nombre":"Kit de Distribucion"}
[2025-05-05 15:43:46][INFO] Movimiento insertado correctamente para repuesto ID: 25
[2025-05-05 15:43:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 25
[2025-05-05 15:43:46][INFO] Transacción completada exitosamente
[2025-05-05 15:43:46][INFO] Conexión cerrada
[2025-05-05 15:48:35][INFO] Nueva solicitud recibida
[2025-05-05 15:48:35][INFO] Método HTTP: POST
[2025-05-05 15:48:35][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:48:35][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":26,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:48:35][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":26,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 15:48:35][INFO] Conexión a base de datos establecida
[2025-05-05 15:48:35][INFO] Iniciando transacción
[2025-05-05 15:48:35][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:48:35][INFO] Procesando repuesto: {"repuesto_id":26,"cantidad":1,"nombre":"Kit de Distribucion"}
[2025-05-05 15:48:35][INFO] Movimiento insertado correctamente para repuesto ID: 26
[2025-05-05 15:48:35][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 26
[2025-05-05 15:48:35][INFO] Transacción completada exitosamente
[2025-05-05 15:48:35][INFO] Conexión cerrada
[2025-05-05 15:56:35][INFO] Nueva solicitud recibida
[2025-05-05 15:56:35][INFO] Método HTTP: POST
[2025-05-05 15:56:35][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:56:35][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 15:56:35][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 15:56:35][INFO] Conexión a base de datos establecida
[2025-05-05 15:56:35][INFO] Iniciando transacción
[2025-05-05 15:56:35][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:56:35][INFO] Procesando repuesto: {"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-05-05 15:56:35][INFO] Movimiento insertado correctamente para repuesto ID: 27
[2025-05-05 15:56:35][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 27
[2025-05-05 15:56:35][INFO] Transacción completada exitosamente
[2025-05-05 15:56:35][INFO] Conexión cerrada
[2025-05-05 15:56:54][INFO] Nueva solicitud recibida
[2025-05-05 15:56:54][INFO] Método HTTP: POST
[2025-05-05 15:56:54][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 15:56:54][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 15:56:54][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 15:56:54][INFO] Conexión a base de datos establecida
[2025-05-05 15:56:54][INFO] Iniciando transacción
[2025-05-05 15:56:54][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 15:56:54][INFO] Procesando repuesto: {"repuesto_id":27,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-05-05 15:56:54][INFO] Movimiento insertado correctamente para repuesto ID: 27
[2025-05-05 15:56:54][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 27
[2025-05-05 15:56:54][INFO] Transacción completada exitosamente
[2025-05-05 15:56:54][INFO] Conexión cerrada
[2025-05-05 16:00:07][INFO] Nueva solicitud recibida
[2025-05-05 16:00:07][INFO] Método HTTP: POST
[2025-05-05 16:00:07][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 16:00:07][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 16:00:07][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 16:00:07][INFO] Conexión a base de datos establecida
[2025-05-05 16:00:07][INFO] Iniciando transacción
[2025-05-05 16:00:07][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 16:00:07][INFO] Procesando repuesto: {"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}
[2025-05-05 16:00:07][INFO] Movimiento insertado correctamente para repuesto ID: 28
[2025-05-05 16:00:07][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 28
[2025-05-05 16:00:07][INFO] Transacción completada exitosamente
[2025-05-05 16:00:07][INFO] Conexión cerrada
[2025-05-05 16:04:42][INFO] Nueva solicitud recibida
[2025-05-05 16:04:42][INFO] Método HTTP: POST
[2025-05-05 16:04:42][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 16:04:42][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":29,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 16:04:42][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":29,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 16:04:42][INFO] Conexión a base de datos establecida
[2025-05-05 16:04:42][INFO] Iniciando transacción
[2025-05-05 16:04:42][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 16:04:42][INFO] Procesando repuesto: {"repuesto_id":29,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-05-05 16:04:42][INFO] Movimiento insertado correctamente para repuesto ID: 29
[2025-05-05 16:04:42][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 29
[2025-05-05 16:04:42][INFO] Transacción completada exitosamente
[2025-05-05 16:04:42][INFO] Conexión cerrada
[2025-05-05 16:19:48][INFO] Nueva solicitud recibida
[2025-05-05 16:19:48][INFO] Método HTTP: POST
[2025-05-05 16:19:48][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"170","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 16:19:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":30,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}]}
[2025-05-05 16:19:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":30,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}]}
[2025-05-05 16:19:48][INFO] Conexión a base de datos establecida
[2025-05-05 16:19:48][INFO] Iniciando transacción
[2025-05-05 16:19:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 16:19:48][INFO] Procesando repuesto: {"repuesto_id":30,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}
[2025-05-05 16:19:48][INFO] Movimiento insertado correctamente para repuesto ID: 30
[2025-05-05 16:19:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 30
[2025-05-05 16:19:48][INFO] Transacción completada exitosamente
[2025-05-05 16:19:48][INFO] Conexión cerrada
[2025-05-05 16:38:23][INFO] Nueva solicitud recibida
[2025-05-05 16:38:23][INFO] Método HTTP: POST
[2025-05-05 16:38:23][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 16:38:23][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 16:38:23][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}]}
[2025-05-05 16:38:23][INFO] Conexión a base de datos establecida
[2025-05-05 16:38:23][INFO] Iniciando transacción
[2025-05-05 16:38:23][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 16:38:23][INFO] Procesando repuesto: {"repuesto_id":28,"cantidad":1,"nombre":"Kit de Distribucion"}
[2025-05-05 16:38:23][INFO] Movimiento insertado correctamente para repuesto ID: 28
[2025-05-05 16:38:23][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 28
[2025-05-05 16:38:23][INFO] Transacción completada exitosamente
[2025-05-05 16:38:23][INFO] Conexión cerrada
[2025-05-05 16:40:48][INFO] Nueva solicitud recibida
[2025-05-05 16:40:48][INFO] Método HTTP: POST
[2025-05-05 16:40:48][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"170","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 16:40:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":32,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}]}
[2025-05-05 16:40:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":32,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}]}
[2025-05-05 16:40:48][INFO] Conexión a base de datos establecida
[2025-05-05 16:40:48][INFO] Iniciando transacción
[2025-05-05 16:40:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 16:40:48][INFO] Procesando repuesto: {"repuesto_id":32,"cantidad":1,"nombre":"Kit Distribucion con Bomba Agua"}
[2025-05-05 16:40:48][INFO] Movimiento insertado correctamente para repuesto ID: 32
[2025-05-05 16:40:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 32
[2025-05-05 16:40:48][INFO] Transacción completada exitosamente
[2025-05-05 16:40:48][INFO] Conexión cerrada
[2025-05-05 17:43:43][INFO] Nueva solicitud recibida
[2025-05-05 17:43:43][INFO] Método HTTP: POST
[2025-05-05 17:43:43][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 17:43:43][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":33,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 17:43:43][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":33,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-05-05 17:43:43][INFO] Conexión a base de datos establecida
[2025-05-05 17:43:43][INFO] Iniciando transacción
[2025-05-05 17:43:43][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 17:43:43][INFO] Procesando repuesto: {"repuesto_id":33,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-05-05 17:43:43][INFO] Movimiento insertado correctamente para repuesto ID: 33
[2025-05-05 17:43:43][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 33
[2025-05-05 17:43:43][INFO] Transacción completada exitosamente
[2025-05-05 17:43:43][INFO] Conexión cerrada
[2025-05-05 17:55:24][INFO] Nueva solicitud recibida
[2025-05-05 17:55:24][INFO] Método HTTP: POST
[2025-05-05 17:55:24][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"153","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 17:55:24][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":34,"cantidad":2,"nombre":"Kit Accesorios"}]}
[2025-05-05 17:55:24][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":34,"cantidad":2,"nombre":"Kit Accesorios"}]}
[2025-05-05 17:55:24][INFO] Conexión a base de datos establecida
[2025-05-05 17:55:24][INFO] Iniciando transacción
[2025-05-05 17:55:24][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 17:55:24][INFO] Procesando repuesto: {"repuesto_id":34,"cantidad":2,"nombre":"Kit Accesorios"}
[2025-05-05 17:55:24][INFO] Movimiento insertado correctamente para repuesto ID: 34
[2025-05-05 17:55:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 34
[2025-05-05 17:55:24][INFO] Transacción completada exitosamente
[2025-05-05 17:55:24][INFO] Conexión cerrada
[2025-05-05 17:59:18][INFO] Nueva solicitud recibida
[2025-05-05 17:59:18][INFO] Método HTTP: POST
[2025-05-05 17:59:18][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"153","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 17:59:18][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":35,"cantidad":1,"nombre":"Kit Accesorios"}]}
[2025-05-05 17:59:18][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":35,"cantidad":1,"nombre":"Kit Accesorios"}]}
[2025-05-05 17:59:18][INFO] Conexión a base de datos establecida
[2025-05-05 17:59:18][INFO] Iniciando transacción
[2025-05-05 17:59:18][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 17:59:18][INFO] Procesando repuesto: {"repuesto_id":35,"cantidad":1,"nombre":"Kit Accesorios"}
[2025-05-05 17:59:18][INFO] Movimiento insertado correctamente para repuesto ID: 35
[2025-05-05 17:59:18][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 35
[2025-05-05 17:59:18][INFO] Transacción completada exitosamente
[2025-05-05 17:59:18][INFO] Conexión cerrada
[2025-05-05 18:03:40][INFO] Nueva solicitud recibida
[2025-05-05 18:03:40][INFO] Método HTTP: POST
[2025-05-05 18:03:40][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"153","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=mh1r67r2hamsi4gf50v9t6kril"}
[2025-05-05 18:03:40][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":36,"cantidad":1,"nombre":"Kit Accesorios"}]}
[2025-05-05 18:03:40][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":36,"cantidad":1,"nombre":"Kit Accesorios"}]}
[2025-05-05 18:03:40][INFO] Conexión a base de datos establecida
[2025-05-05 18:03:40][INFO] Iniciando transacción
[2025-05-05 18:03:40][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-05 18:03:40][INFO] Procesando repuesto: {"repuesto_id":36,"cantidad":1,"nombre":"Kit Accesorios"}
[2025-05-05 18:03:40][INFO] Movimiento insertado correctamente para repuesto ID: 36
[2025-05-05 18:03:40][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 36
[2025-05-05 18:03:40][INFO] Transacción completada exitosamente
[2025-05-05 18:03:40][INFO] Conexión cerrada
[2025-05-07 15:47:31][INFO] Nueva solicitud recibida
[2025-05-07 15:47:31][INFO] Método HTTP: POST
[2025-05-07 15:47:31][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"148","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 15:47:31][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}]}
[2025-05-07 15:47:31][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}]}
[2025-05-07 15:47:31][INFO] Conexión a base de datos establecida
[2025-05-07 15:47:31][INFO] Iniciando transacción
[2025-05-07 15:47:31][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 15:47:31][INFO] Procesando repuesto: {"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}
[2025-05-07 15:47:31][INFO] Movimiento insertado correctamente para repuesto ID: 37
[2025-05-07 15:47:31][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 37
[2025-05-07 15:47:31][INFO] Transacción completada exitosamente
[2025-05-07 15:47:31][INFO] Conexión cerrada
[2025-05-07 15:48:25][INFO] Nueva solicitud recibida
[2025-05-07 15:48:25][INFO] Método HTTP: POST
[2025-05-07 15:48:25][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"148","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 15:48:25][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}]}
[2025-05-07 15:48:25][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}]}
[2025-05-07 15:48:25][INFO] Conexión a base de datos establecida
[2025-05-07 15:48:25][INFO] Iniciando transacción
[2025-05-07 15:48:25][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 15:48:25][INFO] Procesando repuesto: {"repuesto_id":37,"cantidad":1,"nombre":"Cazoleta "}
[2025-05-07 15:48:25][INFO] Movimiento insertado correctamente para repuesto ID: 37
[2025-05-07 15:48:25][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 37
[2025-05-07 15:48:25][INFO] Transacción completada exitosamente
[2025-05-07 15:48:25][INFO] Conexión cerrada
[2025-05-07 16:06:23][INFO] Nueva solicitud recibida
[2025-05-07 16:06:23][INFO] Método HTTP: POST
[2025-05-07 16:06:23][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"152","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:06:23][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":38,"cantidad":2,"nombre":"Kit Cazoleta "}]}
[2025-05-07 16:06:23][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":38,"cantidad":2,"nombre":"Kit Cazoleta "}]}
[2025-05-07 16:06:23][INFO] Conexión a base de datos establecida
[2025-05-07 16:06:23][INFO] Iniciando transacción
[2025-05-07 16:06:23][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:06:23][INFO] Procesando repuesto: {"repuesto_id":38,"cantidad":2,"nombre":"Kit Cazoleta "}
[2025-05-07 16:06:23][INFO] Movimiento insertado correctamente para repuesto ID: 38
[2025-05-07 16:06:23][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 38
[2025-05-07 16:06:23][INFO] Transacción completada exitosamente
[2025-05-07 16:06:23][INFO] Conexión cerrada
[2025-05-07 16:12:04][INFO] Nueva solicitud recibida
[2025-05-07 16:12:04][INFO] Método HTTP: POST
[2025-05-07 16:12:04][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"147","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:12:04][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":39,"cantidad":2,"nombre":"Cazoleta"}]}
[2025-05-07 16:12:04][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":39,"cantidad":2,"nombre":"Cazoleta"}]}
[2025-05-07 16:12:04][INFO] Conexión a base de datos establecida
[2025-05-07 16:12:04][INFO] Iniciando transacción
[2025-05-07 16:12:04][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:12:04][INFO] Procesando repuesto: {"repuesto_id":39,"cantidad":2,"nombre":"Cazoleta"}
[2025-05-07 16:12:04][INFO] Movimiento insertado correctamente para repuesto ID: 39
[2025-05-07 16:12:04][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 39
[2025-05-07 16:12:04][INFO] Transacción completada exitosamente
[2025-05-07 16:12:04][INFO] Conexión cerrada
[2025-05-07 16:18:17][INFO] Nueva solicitud recibida
[2025-05-07 16:18:17][INFO] Método HTTP: POST
[2025-05-07 16:18:17][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"154","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:18:17][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":41,"cantidad":1,"nombre":"Kit Cazoleta LH"}]}
[2025-05-07 16:18:17][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":41,"cantidad":1,"nombre":"Kit Cazoleta LH"}]}
[2025-05-07 16:18:17][INFO] Conexión a base de datos establecida
[2025-05-07 16:18:17][INFO] Iniciando transacción
[2025-05-07 16:18:17][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:18:17][INFO] Procesando repuesto: {"repuesto_id":41,"cantidad":1,"nombre":"Kit Cazoleta LH"}
[2025-05-07 16:18:17][INFO] Movimiento insertado correctamente para repuesto ID: 41
[2025-05-07 16:18:17][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 41
[2025-05-07 16:18:17][INFO] Transacción completada exitosamente
[2025-05-07 16:18:17][INFO] Conexión cerrada
[2025-05-07 16:18:46][INFO] Nueva solicitud recibida
[2025-05-07 16:18:46][INFO] Método HTTP: POST
[2025-05-07 16:18:46][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"154","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:18:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":40,"cantidad":1,"nombre":"Kit Cazoleta RH"}]}
[2025-05-07 16:18:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":40,"cantidad":1,"nombre":"Kit Cazoleta RH"}]}
[2025-05-07 16:18:46][INFO] Conexión a base de datos establecida
[2025-05-07 16:18:46][INFO] Iniciando transacción
[2025-05-07 16:18:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:18:46][INFO] Procesando repuesto: {"repuesto_id":40,"cantidad":1,"nombre":"Kit Cazoleta RH"}
[2025-05-07 16:18:46][INFO] Movimiento insertado correctamente para repuesto ID: 40
[2025-05-07 16:18:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 40
[2025-05-07 16:18:46][INFO] Transacción completada exitosamente
[2025-05-07 16:18:46][INFO] Conexión cerrada
[2025-05-07 16:29:04][INFO] Nueva solicitud recibida
[2025-05-07 16:29:04][INFO] Método HTTP: POST
[2025-05-07 16:29:04][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"152","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:29:04][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":42,"cantidad":4,"nombre":"Kit Cazoleta "}]}
[2025-05-07 16:29:04][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":42,"cantidad":4,"nombre":"Kit Cazoleta "}]}
[2025-05-07 16:29:04][INFO] Conexión a base de datos establecida
[2025-05-07 16:29:04][INFO] Iniciando transacción
[2025-05-07 16:29:04][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:29:04][INFO] Procesando repuesto: {"repuesto_id":42,"cantidad":4,"nombre":"Kit Cazoleta "}
[2025-05-07 16:29:04][INFO] Movimiento insertado correctamente para repuesto ID: 42
[2025-05-07 16:29:04][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 42
[2025-05-07 16:29:04][INFO] Transacción completada exitosamente
[2025-05-07 16:29:04][INFO] Conexión cerrada
[2025-05-07 16:32:54][INFO] Nueva solicitud recibida
[2025-05-07 16:32:54][INFO] Método HTTP: POST
[2025-05-07 16:32:54][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"151","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:32:54][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":43,"cantidad":2,"nombre":"Kit Cazoleta"}]}
[2025-05-07 16:32:54][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":43,"cantidad":2,"nombre":"Kit Cazoleta"}]}
[2025-05-07 16:32:54][INFO] Conexión a base de datos establecida
[2025-05-07 16:32:54][INFO] Iniciando transacción
[2025-05-07 16:32:54][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:32:54][INFO] Procesando repuesto: {"repuesto_id":43,"cantidad":2,"nombre":"Kit Cazoleta"}
[2025-05-07 16:32:54][INFO] Movimiento insertado correctamente para repuesto ID: 43
[2025-05-07 16:32:54][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 43
[2025-05-07 16:32:54][INFO] Transacción completada exitosamente
[2025-05-07 16:32:54][INFO] Conexión cerrada
[2025-05-07 16:37:17][INFO] Nueva solicitud recibida
[2025-05-07 16:37:17][INFO] Método HTTP: POST
[2025-05-07 16:37:17][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"151","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:37:17][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":44,"cantidad":2,"nombre":"Kit Cazoleta"}]}
[2025-05-07 16:37:17][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":44,"cantidad":2,"nombre":"Kit Cazoleta"}]}
[2025-05-07 16:37:17][INFO] Conexión a base de datos establecida
[2025-05-07 16:37:17][INFO] Iniciando transacción
[2025-05-07 16:37:17][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:37:17][INFO] Procesando repuesto: {"repuesto_id":44,"cantidad":2,"nombre":"Kit Cazoleta"}
[2025-05-07 16:37:17][INFO] Movimiento insertado correctamente para repuesto ID: 44
[2025-05-07 16:37:17][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 44
[2025-05-07 16:37:17][INFO] Transacción completada exitosamente
[2025-05-07 16:37:17][INFO] Conexión cerrada
[2025-05-07 16:44:42][INFO] Nueva solicitud recibida
[2025-05-07 16:44:42][INFO] Método HTTP: POST
[2025-05-07 16:44:42][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"150","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 16:44:42][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":45,"cantidad":1,"nombre":"Cazoleta LH"}]}
[2025-05-07 16:44:42][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":45,"cantidad":1,"nombre":"Cazoleta LH"}]}
[2025-05-07 16:44:42][INFO] Conexión a base de datos establecida
[2025-05-07 16:44:42][INFO] Iniciando transacción
[2025-05-07 16:44:42][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 16:44:42][INFO] Procesando repuesto: {"repuesto_id":45,"cantidad":1,"nombre":"Cazoleta LH"}
[2025-05-07 16:44:42][INFO] Movimiento insertado correctamente para repuesto ID: 45
[2025-05-07 16:44:42][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 45
[2025-05-07 16:44:42][INFO] Transacción completada exitosamente
[2025-05-07 16:44:42][INFO] Conexión cerrada
[2025-05-07 18:22:47][INFO] Nueva solicitud recibida
[2025-05-07 18:22:47][INFO] Método HTTP: POST
[2025-05-07 18:22:47][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:22:47][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":46,"cantidad":2,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:22:47][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":46,"cantidad":2,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:22:47][INFO] Conexión a base de datos establecida
[2025-05-07 18:22:47][INFO] Iniciando transacción
[2025-05-07 18:22:47][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:22:47][INFO] Procesando repuesto: {"repuesto_id":46,"cantidad":2,"nombre":"Tensor de Correa de Accesorios"}
[2025-05-07 18:22:47][INFO] Movimiento insertado correctamente para repuesto ID: 46
[2025-05-07 18:22:47][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 46
[2025-05-07 18:22:47][INFO] Transacción completada exitosamente
[2025-05-07 18:22:47][INFO] Conexión cerrada
[2025-05-07 18:23:29][INFO] Nueva solicitud recibida
[2025-05-07 18:23:29][INFO] Método HTTP: POST
[2025-05-07 18:23:29][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:23:29][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":46,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:23:29][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":46,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:23:29][INFO] Conexión a base de datos establecida
[2025-05-07 18:23:29][INFO] Iniciando transacción
[2025-05-07 18:23:29][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:23:29][INFO] Procesando repuesto: {"repuesto_id":46,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}
[2025-05-07 18:23:29][INFO] Movimiento insertado correctamente para repuesto ID: 46
[2025-05-07 18:23:29][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 46
[2025-05-07 18:23:29][INFO] Transacción completada exitosamente
[2025-05-07 18:23:29][INFO] Conexión cerrada
[2025-05-07 18:32:40][INFO] Nueva solicitud recibida
[2025-05-07 18:32:40][INFO] Método HTTP: POST
[2025-05-07 18:32:40][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:32:40][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":47,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:32:40][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":47,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}]}
[2025-05-07 18:32:40][INFO] Conexión a base de datos establecida
[2025-05-07 18:32:40][INFO] Iniciando transacción
[2025-05-07 18:32:40][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:32:40][INFO] Procesando repuesto: {"repuesto_id":47,"cantidad":1,"nombre":"Tensor de Correa de Accesorios"}
[2025-05-07 18:32:40][INFO] Movimiento insertado correctamente para repuesto ID: 47
[2025-05-07 18:32:40][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 47
[2025-05-07 18:32:40][INFO] Transacción completada exitosamente
[2025-05-07 18:32:40][INFO] Conexión cerrada
[2025-05-07 18:37:16][INFO] Nueva solicitud recibida
[2025-05-07 18:37:16][INFO] Método HTTP: POST
[2025-05-07 18:37:16][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:37:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":48,"cantidad":1,"nombre":"Tensor de Correa de Alternador"}]}
[2025-05-07 18:37:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":48,"cantidad":1,"nombre":"Tensor de Correa de Alternador"}]}
[2025-05-07 18:37:16][INFO] Conexión a base de datos establecida
[2025-05-07 18:37:16][INFO] Iniciando transacción
[2025-05-07 18:37:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:37:16][INFO] Procesando repuesto: {"repuesto_id":48,"cantidad":1,"nombre":"Tensor de Correa de Alternador"}
[2025-05-07 18:37:16][INFO] Movimiento insertado correctamente para repuesto ID: 48
[2025-05-07 18:37:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 48
[2025-05-07 18:37:16][INFO] Transacción completada exitosamente
[2025-05-07 18:37:16][INFO] Conexión cerrada
[2025-05-07 18:40:58][INFO] Nueva solicitud recibida
[2025-05-07 18:40:58][INFO] Método HTTP: POST
[2025-05-07 18:40:58][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:40:58][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":49,"cantidad":1,"nombre":"Tensor Guia Correa Alternador"}]}
[2025-05-07 18:40:58][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":49,"cantidad":1,"nombre":"Tensor Guia Correa Alternador"}]}
[2025-05-07 18:40:58][INFO] Conexión a base de datos establecida
[2025-05-07 18:40:58][INFO] Iniciando transacción
[2025-05-07 18:40:58][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:40:58][INFO] Procesando repuesto: {"repuesto_id":49,"cantidad":1,"nombre":"Tensor Guia Correa Alternador"}
[2025-05-07 18:40:58][INFO] Movimiento insertado correctamente para repuesto ID: 49
[2025-05-07 18:40:58][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 49
[2025-05-07 18:40:58][INFO] Transacción completada exitosamente
[2025-05-07 18:40:58][INFO] Conexión cerrada
[2025-05-07 18:56:28][INFO] Nueva solicitud recibida
[2025-05-07 18:56:28][INFO] Método HTTP: POST
[2025-05-07 18:56:28][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:56:28][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":51,"cantidad":2,"nombre":"Tensor Correa Alternador"}]}
[2025-05-07 18:56:28][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":51,"cantidad":2,"nombre":"Tensor Correa Alternador"}]}
[2025-05-07 18:56:28][INFO] Conexión a base de datos establecida
[2025-05-07 18:56:28][INFO] Iniciando transacción
[2025-05-07 18:56:28][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:56:28][INFO] Procesando repuesto: {"repuesto_id":51,"cantidad":2,"nombre":"Tensor Correa Alternador"}
[2025-05-07 18:56:28][INFO] Movimiento insertado correctamente para repuesto ID: 51
[2025-05-07 18:56:28][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 51
[2025-05-07 18:56:28][INFO] Transacción completada exitosamente
[2025-05-07 18:56:28][INFO] Conexión cerrada
[2025-05-07 18:59:24][INFO] Nueva solicitud recibida
[2025-05-07 18:59:24][INFO] Método HTTP: POST
[2025-05-07 18:59:24][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 18:59:24][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":52,"cantidad":1,"nombre":"Tensor Correa Alternador"}]}
[2025-05-07 18:59:24][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":52,"cantidad":1,"nombre":"Tensor Correa Alternador"}]}
[2025-05-07 18:59:24][INFO] Conexión a base de datos establecida
[2025-05-07 18:59:24][INFO] Iniciando transacción
[2025-05-07 18:59:24][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 18:59:24][INFO] Procesando repuesto: {"repuesto_id":52,"cantidad":1,"nombre":"Tensor Correa Alternador"}
[2025-05-07 18:59:24][INFO] Movimiento insertado correctamente para repuesto ID: 52
[2025-05-07 18:59:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 52
[2025-05-07 18:59:24][INFO] Transacción completada exitosamente
[2025-05-07 18:59:24][INFO] Conexión cerrada
[2025-05-07 19:17:00][INFO] Nueva solicitud recibida
[2025-05-07 19:17:00][INFO] Método HTTP: POST
[2025-05-07 19:17:00][INFO] Headers recibidos: {"Host":"***************","Connection":"keep-alive","Content-Length":"147","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/***************","Referer":"http:\/\/***************\/projects\/tata_repuestos\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=l5llc361i37oj5jmb2lcienb7t"}
[2025-05-07 19:17:00][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":53,"cantidad":4,"nombre":"Cazoleta"}]}
[2025-05-07 19:17:00][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":53,"cantidad":4,"nombre":"Cazoleta"}]}
[2025-05-07 19:17:00][INFO] Conexión a base de datos establecida
[2025-05-07 19:17:00][INFO] Iniciando transacción
[2025-05-07 19:17:00][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-07 19:17:00][INFO] Procesando repuesto: {"repuesto_id":53,"cantidad":4,"nombre":"Cazoleta"}
[2025-05-07 19:17:00][INFO] Movimiento insertado correctamente para repuesto ID: 53
[2025-05-07 19:17:00][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 53
[2025-05-07 19:17:00][INFO] Transacción completada exitosamente
[2025-05-07 19:17:00][INFO] Conexión cerrada
[2025-05-22 15:34:11][INFO] Nueva solicitud recibida
[2025-05-22 15:34:11][INFO] Método HTTP: POST
[2025-05-22 15:34:11][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"140","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 15:34:11][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":949,"cantidad":5,"nombre":""}]}
[2025-05-22 15:34:11][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":949,"cantidad":5,"nombre":""}]}
[2025-05-22 15:34:11][INFO] Conexión a base de datos establecida
[2025-05-22 15:34:11][INFO] Iniciando transacción
[2025-05-22 15:34:11][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 15:34:11][INFO] Procesando repuesto: {"repuesto_id":949,"cantidad":5,"nombre":""}
[2025-05-22 15:34:11][INFO] Movimiento insertado correctamente para repuesto ID: 949
[2025-05-22 15:34:11][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 949
[2025-05-22 15:34:11][INFO] Transacción completada exitosamente
[2025-05-22 15:34:11][INFO] Conexión cerrada
[2025-05-22 15:54:16][INFO] Nueva solicitud recibida
[2025-05-22 15:54:16][INFO] Método HTTP: POST
[2025-05-22 15:54:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 15:54:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":56,"cantidad":5,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 15:54:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":56,"cantidad":5,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 15:54:16][INFO] Conexión a base de datos establecida
[2025-05-22 15:54:16][INFO] Iniciando transacción
[2025-05-22 15:54:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 15:54:16][INFO] Procesando repuesto: {"repuesto_id":56,"cantidad":5,"nombre":"Valvulas Escape 4U"}
[2025-05-22 15:54:17][INFO] Movimiento insertado correctamente para repuesto ID: 56
[2025-05-22 15:54:17][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 56
[2025-05-22 15:54:17][INFO] Transacción completada exitosamente
[2025-05-22 15:54:17][INFO] Conexión cerrada
[2025-05-22 16:18:24][INFO] Nueva solicitud recibida
[2025-05-22 16:18:24][INFO] Método HTTP: POST
[2025-05-22 16:18:24][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 16:18:24][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":57,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 16:18:24][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":57,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 16:18:24][INFO] Conexión a base de datos establecida
[2025-05-22 16:18:24][INFO] Iniciando transacción
[2025-05-22 16:18:24][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 16:18:24][INFO] Procesando repuesto: {"repuesto_id":57,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-22 16:18:24][INFO] Movimiento insertado correctamente para repuesto ID: 57
[2025-05-22 16:18:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 57
[2025-05-22 16:18:24][INFO] Transacción completada exitosamente
[2025-05-22 16:18:24][INFO] Conexión cerrada
[2025-05-22 16:23:52][INFO] Nueva solicitud recibida
[2025-05-22 16:23:52][INFO] Método HTTP: POST
[2025-05-22 16:23:52][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 16:23:52][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":58,"cantidad":5,"nombre":"Valvulas Admision Juego 8U"}]}
[2025-05-22 16:23:52][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":58,"cantidad":5,"nombre":"Valvulas Admision Juego 8U"}]}
[2025-05-22 16:23:52][INFO] Conexión a base de datos establecida
[2025-05-22 16:23:52][INFO] Iniciando transacción
[2025-05-22 16:23:52][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 16:23:52][INFO] Procesando repuesto: {"repuesto_id":58,"cantidad":5,"nombre":"Valvulas Admision Juego 8U"}
[2025-05-22 16:23:52][INFO] Movimiento insertado correctamente para repuesto ID: 58
[2025-05-22 16:23:52][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 58
[2025-05-22 16:23:52][INFO] Transacción completada exitosamente
[2025-05-22 16:23:52][INFO] Conexión cerrada
[2025-05-22 16:31:02][INFO] Nueva solicitud recibida
[2025-05-22 16:31:02][INFO] Método HTTP: POST
[2025-05-22 16:31:02][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 16:31:02][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":60,"cantidad":3,"nombre":"Valvulas Escape 8U"}]}
[2025-05-22 16:31:02][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":60,"cantidad":3,"nombre":"Valvulas Escape 8U"}]}
[2025-05-22 16:31:02][INFO] Conexión a base de datos establecida
[2025-05-22 16:31:02][INFO] Iniciando transacción
[2025-05-22 16:31:02][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 16:31:02][INFO] Procesando repuesto: {"repuesto_id":60,"cantidad":3,"nombre":"Valvulas Escape 8U"}
[2025-05-22 16:31:02][INFO] Movimiento insertado correctamente para repuesto ID: 60
[2025-05-22 16:31:02][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 60
[2025-05-22 16:31:02][INFO] Transacción completada exitosamente
[2025-05-22 16:31:02][INFO] Conexión cerrada
[2025-05-22 16:31:27][INFO] Nueva solicitud recibida
[2025-05-22 16:31:27][INFO] Método HTTP: POST
[2025-05-22 16:31:27][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 16:31:27][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":59,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 16:31:27][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":59,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 16:31:27][INFO] Conexión a base de datos establecida
[2025-05-22 16:31:27][INFO] Iniciando transacción
[2025-05-22 16:31:27][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 16:31:27][INFO] Procesando repuesto: {"repuesto_id":59,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-22 16:31:27][INFO] Movimiento insertado correctamente para repuesto ID: 59
[2025-05-22 16:31:27][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 59
[2025-05-22 16:31:27][INFO] Transacción completada exitosamente
[2025-05-22 16:31:27][INFO] Conexión cerrada
[2025-05-22 16:47:16][INFO] Nueva solicitud recibida
[2025-05-22 16:47:16][INFO] Método HTTP: POST
[2025-05-22 16:47:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 16:47:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":61,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 16:47:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":61,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 16:47:16][INFO] Conexión a base de datos establecida
[2025-05-22 16:47:16][INFO] Iniciando transacción
[2025-05-22 16:47:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 16:47:16][INFO] Procesando repuesto: {"repuesto_id":61,"cantidad":2,"nombre":"Valvulas Escape 4U"}
[2025-05-22 16:47:16][INFO] Movimiento insertado correctamente para repuesto ID: 61
[2025-05-22 16:47:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 61
[2025-05-22 16:47:16][INFO] Transacción completada exitosamente
[2025-05-22 16:47:16][INFO] Conexión cerrada
[2025-05-22 17:09:11][INFO] Nueva solicitud recibida
[2025-05-22 17:09:11][INFO] Método HTTP: POST
[2025-05-22 17:09:11][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 17:09:11][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":62,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 17:09:11][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":62,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 17:09:11][INFO] Conexión a base de datos establecida
[2025-05-22 17:09:11][INFO] Iniciando transacción
[2025-05-22 17:09:11][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 17:09:11][INFO] Procesando repuesto: {"repuesto_id":62,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-22 17:09:11][INFO] Movimiento insertado correctamente para repuesto ID: 62
[2025-05-22 17:09:11][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 62
[2025-05-22 17:09:11][INFO] Transacción completada exitosamente
[2025-05-22 17:09:11][INFO] Conexión cerrada
[2025-05-22 18:24:11][INFO] Nueva solicitud recibida
[2025-05-22 18:24:11][INFO] Método HTTP: POST
[2025-05-22 18:24:11][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 18:24:11][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":63,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 18:24:11][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":63,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 18:24:11][INFO] Conexión a base de datos establecida
[2025-05-22 18:24:11][INFO] Iniciando transacción
[2025-05-22 18:24:11][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 18:24:11][INFO] Procesando repuesto: {"repuesto_id":63,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-22 18:24:11][INFO] Movimiento insertado correctamente para repuesto ID: 63
[2025-05-22 18:24:11][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 63
[2025-05-22 18:24:11][INFO] Transacción completada exitosamente
[2025-05-22 18:24:11][INFO] Conexión cerrada
[2025-05-22 18:30:21][INFO] Nueva solicitud recibida
[2025-05-22 18:30:21][INFO] Método HTTP: POST
[2025-05-22 18:30:21][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 18:30:21][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":64,"cantidad":3,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 18:30:21][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":64,"cantidad":3,"nombre":"Valvulas Admision 4U"}]}
[2025-05-22 18:30:21][INFO] Conexión a base de datos establecida
[2025-05-22 18:30:21][INFO] Iniciando transacción
[2025-05-22 18:30:21][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 18:30:21][INFO] Procesando repuesto: {"repuesto_id":64,"cantidad":3,"nombre":"Valvulas Admision 4U"}
[2025-05-22 18:30:21][INFO] Movimiento insertado correctamente para repuesto ID: 64
[2025-05-22 18:30:21][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 64
[2025-05-22 18:30:21][INFO] Transacción completada exitosamente
[2025-05-22 18:30:21][INFO] Conexión cerrada
[2025-05-22 18:33:09][INFO] Nueva solicitud recibida
[2025-05-22 18:33:09][INFO] Método HTTP: POST
[2025-05-22 18:33:09][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=jgh08l0ev2i8a30ahb08q35gvb"}
[2025-05-22 18:33:09][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":65,"cantidad":3,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 18:33:09][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":65,"cantidad":3,"nombre":"Valvulas Escape 4U"}]}
[2025-05-22 18:33:09][INFO] Conexión a base de datos establecida
[2025-05-22 18:33:09][INFO] Iniciando transacción
[2025-05-22 18:33:09][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-22 18:33:09][INFO] Procesando repuesto: {"repuesto_id":65,"cantidad":3,"nombre":"Valvulas Escape 4U"}
[2025-05-22 18:33:09][INFO] Movimiento insertado correctamente para repuesto ID: 65
[2025-05-22 18:33:09][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 65
[2025-05-22 18:33:09][INFO] Transacción completada exitosamente
[2025-05-22 18:33:09][INFO] Conexión cerrada
[2025-05-24 14:12:12][INFO] Nueva solicitud recibida
[2025-05-24 14:12:12][INFO] Método HTTP: POST
[2025-05-24 14:12:12][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:12:12][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":66,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:12:12][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":66,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:12:12][INFO] Conexión a base de datos establecida
[2025-05-24 14:12:12][INFO] Iniciando transacción
[2025-05-24 14:12:12][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:12:12][INFO] Procesando repuesto: {"repuesto_id":66,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-24 14:12:12][INFO] Movimiento insertado correctamente para repuesto ID: 66
[2025-05-24 14:12:12][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 66
[2025-05-24 14:12:12][INFO] Transacción completada exitosamente
[2025-05-24 14:12:12][INFO] Conexión cerrada
[2025-05-24 14:13:59][INFO] Nueva solicitud recibida
[2025-05-24 14:13:59][INFO] Método HTTP: POST
[2025-05-24 14:13:59][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:13:59][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":67,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:13:59][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":67,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:13:59][INFO] Conexión a base de datos establecida
[2025-05-24 14:13:59][INFO] Iniciando transacción
[2025-05-24 14:13:59][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:13:59][INFO] Procesando repuesto: {"repuesto_id":67,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-24 14:13:59][INFO] Movimiento insertado correctamente para repuesto ID: 67
[2025-05-24 14:13:59][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 67
[2025-05-24 14:13:59][INFO] Transacción completada exitosamente
[2025-05-24 14:13:59][INFO] Conexión cerrada
[2025-05-24 14:22:46][INFO] Nueva solicitud recibida
[2025-05-24 14:22:46][INFO] Método HTTP: POST
[2025-05-24 14:22:46][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:22:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":68,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:22:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":68,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:22:46][INFO] Conexión a base de datos establecida
[2025-05-24 14:22:46][INFO] Iniciando transacción
[2025-05-24 14:22:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:22:46][INFO] Procesando repuesto: {"repuesto_id":68,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-24 14:22:46][INFO] Movimiento insertado correctamente para repuesto ID: 68
[2025-05-24 14:22:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 68
[2025-05-24 14:22:46][INFO] Transacción completada exitosamente
[2025-05-24 14:22:46][INFO] Conexión cerrada
[2025-05-24 14:30:04][INFO] Nueva solicitud recibida
[2025-05-24 14:30:04][INFO] Método HTTP: POST
[2025-05-24 14:30:04][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:30:04][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":69,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:30:04][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":69,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:30:04][INFO] Conexión a base de datos establecida
[2025-05-24 14:30:04][INFO] Iniciando transacción
[2025-05-24 14:30:04][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:30:04][INFO] Procesando repuesto: {"repuesto_id":69,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-24 14:30:04][INFO] Movimiento insertado correctamente para repuesto ID: 69
[2025-05-24 14:30:04][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 69
[2025-05-24 14:30:04][INFO] Transacción completada exitosamente
[2025-05-24 14:30:04][INFO] Conexión cerrada
[2025-05-24 14:33:49][INFO] Nueva solicitud recibida
[2025-05-24 14:33:49][INFO] Método HTTP: POST
[2025-05-24 14:33:49][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:33:49][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":70,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:33:49][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":70,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:33:49][INFO] Conexión a base de datos establecida
[2025-05-24 14:33:49][INFO] Iniciando transacción
[2025-05-24 14:33:49][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:33:49][INFO] Procesando repuesto: {"repuesto_id":70,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-24 14:33:49][INFO] Movimiento insertado correctamente para repuesto ID: 70
[2025-05-24 14:33:49][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 70
[2025-05-24 14:33:49][INFO] Transacción completada exitosamente
[2025-05-24 14:33:49][INFO] Conexión cerrada
[2025-05-24 14:37:47][INFO] Nueva solicitud recibida
[2025-05-24 14:37:47][INFO] Método HTTP: POST
[2025-05-24 14:37:47][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:37:47][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":71,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:37:47][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":71,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-24 14:37:47][INFO] Conexión a base de datos establecida
[2025-05-24 14:37:47][INFO] Iniciando transacción
[2025-05-24 14:37:47][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:37:47][INFO] Procesando repuesto: {"repuesto_id":71,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-24 14:37:47][INFO] Movimiento insertado correctamente para repuesto ID: 71
[2025-05-24 14:37:47][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 71
[2025-05-24 14:37:47][INFO] Transacción completada exitosamente
[2025-05-24 14:37:47][INFO] Conexión cerrada
[2025-05-24 14:42:22][INFO] Nueva solicitud recibida
[2025-05-24 14:42:22][INFO] Método HTTP: POST
[2025-05-24 14:42:22][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=85eov6hgavsokcudp3m4vc70m7"}
[2025-05-24 14:42:22][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":72,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:42:22][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":72,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-24 14:42:22][INFO] Conexión a base de datos establecida
[2025-05-24 14:42:22][INFO] Iniciando transacción
[2025-05-24 14:42:22][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-24 14:42:22][INFO] Procesando repuesto: {"repuesto_id":72,"cantidad":2,"nombre":"Valvulas Escape 4U"}
[2025-05-24 14:42:22][INFO] Movimiento insertado correctamente para repuesto ID: 72
[2025-05-24 14:42:22][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 72
[2025-05-24 14:42:22][INFO] Transacción completada exitosamente
[2025-05-24 14:42:22][INFO] Conexión cerrada
[2025-05-26 14:28:36][INFO] Nueva solicitud recibida
[2025-05-26 14:28:36][INFO] Método HTTP: POST
[2025-05-26 14:28:36][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:28:36][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":73,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:28:36][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":73,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:28:36][INFO] Conexión a base de datos establecida
[2025-05-26 14:28:36][INFO] Iniciando transacción
[2025-05-26 14:28:36][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:28:36][INFO] Procesando repuesto: {"repuesto_id":73,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:28:36][INFO] Movimiento insertado correctamente para repuesto ID: 73
[2025-05-26 14:28:36][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 73
[2025-05-26 14:28:36][INFO] Transacción completada exitosamente
[2025-05-26 14:28:36][INFO] Conexión cerrada
[2025-05-26 14:40:33][INFO] Nueva solicitud recibida
[2025-05-26 14:40:33][INFO] Método HTTP: POST
[2025-05-26 14:40:33][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:40:33][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":74,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 14:40:33][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":74,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 14:40:33][INFO] Conexión a base de datos establecida
[2025-05-26 14:40:33][INFO] Iniciando transacción
[2025-05-26 14:40:33][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:40:33][INFO] Procesando repuesto: {"repuesto_id":74,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-26 14:40:33][INFO] Movimiento insertado correctamente para repuesto ID: 74
[2025-05-26 14:40:33][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 74
[2025-05-26 14:40:33][INFO] Transacción completada exitosamente
[2025-05-26 14:40:33][INFO] Conexión cerrada
[2025-05-26 14:40:47][INFO] Nueva solicitud recibida
[2025-05-26 14:40:47][INFO] Método HTTP: POST
[2025-05-26 14:40:47][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:40:47][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":75,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 14:40:47][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":75,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 14:40:47][INFO] Conexión a base de datos establecida
[2025-05-26 14:40:47][INFO] Iniciando transacción
[2025-05-26 14:40:47][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:40:47][INFO] Procesando repuesto: {"repuesto_id":75,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-26 14:40:47][INFO] Movimiento insertado correctamente para repuesto ID: 75
[2025-05-26 14:40:47][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 75
[2025-05-26 14:40:47][INFO] Transacción completada exitosamente
[2025-05-26 14:40:47][INFO] Conexión cerrada
[2025-05-26 14:43:19][INFO] Nueva solicitud recibida
[2025-05-26 14:43:19][INFO] Método HTTP: POST
[2025-05-26 14:43:19][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:43:19][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":76,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:43:19][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":76,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:43:19][INFO] Conexión a base de datos establecida
[2025-05-26 14:43:19][INFO] Iniciando transacción
[2025-05-26 14:43:19][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:43:19][INFO] Procesando repuesto: {"repuesto_id":76,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:43:19][INFO] Movimiento insertado correctamente para repuesto ID: 76
[2025-05-26 14:43:19][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 76
[2025-05-26 14:43:19][INFO] Transacción completada exitosamente
[2025-05-26 14:43:19][INFO] Conexión cerrada
[2025-05-26 14:45:52][INFO] Nueva solicitud recibida
[2025-05-26 14:45:52][INFO] Método HTTP: POST
[2025-05-26 14:45:52][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:45:52][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":77,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:45:52][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":77,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:45:52][INFO] Conexión a base de datos establecida
[2025-05-26 14:45:52][INFO] Iniciando transacción
[2025-05-26 14:45:52][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:45:52][INFO] Procesando repuesto: {"repuesto_id":77,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:45:52][INFO] Movimiento insertado correctamente para repuesto ID: 77
[2025-05-26 14:45:52][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 77
[2025-05-26 14:45:52][INFO] Transacción completada exitosamente
[2025-05-26 14:45:52][INFO] Conexión cerrada
[2025-05-26 14:48:48][INFO] Nueva solicitud recibida
[2025-05-26 14:48:48][INFO] Método HTTP: POST
[2025-05-26 14:48:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:48:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":78,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:48:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":78,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:48:48][INFO] Conexión a base de datos establecida
[2025-05-26 14:48:48][INFO] Iniciando transacción
[2025-05-26 14:48:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:48:48][INFO] Procesando repuesto: {"repuesto_id":78,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:48:48][INFO] Movimiento insertado correctamente para repuesto ID: 78
[2025-05-26 14:48:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 78
[2025-05-26 14:48:48][INFO] Transacción completada exitosamente
[2025-05-26 14:48:48][INFO] Conexión cerrada
[2025-05-26 14:51:49][INFO] Nueva solicitud recibida
[2025-05-26 14:51:49][INFO] Método HTTP: POST
[2025-05-26 14:51:49][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:51:49][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":79,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:51:49][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":79,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:51:49][INFO] Conexión a base de datos establecida
[2025-05-26 14:51:49][INFO] Iniciando transacción
[2025-05-26 14:51:49][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:51:49][INFO] Procesando repuesto: {"repuesto_id":79,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:51:49][INFO] Movimiento insertado correctamente para repuesto ID: 79
[2025-05-26 14:51:49][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 79
[2025-05-26 14:51:49][INFO] Transacción completada exitosamente
[2025-05-26 14:51:49][INFO] Conexión cerrada
[2025-05-26 14:55:06][INFO] Nueva solicitud recibida
[2025-05-26 14:55:06][INFO] Método HTTP: POST
[2025-05-26 14:55:06][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:55:06][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":80,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:55:06][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":80,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:55:06][INFO] Conexión a base de datos establecida
[2025-05-26 14:55:06][INFO] Iniciando transacción
[2025-05-26 14:55:06][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:55:06][INFO] Procesando repuesto: {"repuesto_id":80,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:55:06][INFO] Movimiento insertado correctamente para repuesto ID: 80
[2025-05-26 14:55:06][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 80
[2025-05-26 14:55:06][INFO] Transacción completada exitosamente
[2025-05-26 14:55:06][INFO] Conexión cerrada
[2025-05-26 14:55:55][INFO] Nueva solicitud recibida
[2025-05-26 14:55:55][INFO] Método HTTP: POST
[2025-05-26 14:55:55][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:55:55][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":80,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:55:55][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":80,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:55:55][INFO] Conexión a base de datos establecida
[2025-05-26 14:55:55][INFO] Iniciando transacción
[2025-05-26 14:55:55][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:55:55][INFO] Procesando repuesto: {"repuesto_id":80,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:55:55][INFO] Movimiento insertado correctamente para repuesto ID: 80
[2025-05-26 14:55:55][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 80
[2025-05-26 14:55:55][INFO] Transacción completada exitosamente
[2025-05-26 14:55:55][INFO] Conexión cerrada
[2025-05-26 14:59:09][INFO] Nueva solicitud recibida
[2025-05-26 14:59:09][INFO] Método HTTP: POST
[2025-05-26 14:59:09][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 14:59:09][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":81,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:59:09][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":81,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 14:59:09][INFO] Conexión a base de datos establecida
[2025-05-26 14:59:09][INFO] Iniciando transacción
[2025-05-26 14:59:09][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 14:59:09][INFO] Procesando repuesto: {"repuesto_id":81,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-26 14:59:09][INFO] Movimiento insertado correctamente para repuesto ID: 81
[2025-05-26 14:59:09][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 81
[2025-05-26 14:59:09][INFO] Transacción completada exitosamente
[2025-05-26 14:59:09][INFO] Conexión cerrada
[2025-05-26 15:01:55][INFO] Nueva solicitud recibida
[2025-05-26 15:01:55][INFO] Método HTTP: POST
[2025-05-26 15:01:55][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 15:01:55][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":82,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 15:01:55][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":82,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 15:01:55][INFO] Conexión a base de datos establecida
[2025-05-26 15:01:55][INFO] Iniciando transacción
[2025-05-26 15:01:55][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 15:01:55][INFO] Procesando repuesto: {"repuesto_id":82,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-26 15:01:55][INFO] Movimiento insertado correctamente para repuesto ID: 82
[2025-05-26 15:01:55][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 82
[2025-05-26 15:01:55][INFO] Transacción completada exitosamente
[2025-05-26 15:01:55][INFO] Conexión cerrada
[2025-05-26 15:06:00][INFO] Nueva solicitud recibida
[2025-05-26 15:06:00][INFO] Método HTTP: POST
[2025-05-26 15:06:00][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 15:06:00][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":83,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:06:00][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":83,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:06:00][INFO] Conexión a base de datos establecida
[2025-05-26 15:06:00][INFO] Iniciando transacción
[2025-05-26 15:06:00][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 15:06:00][INFO] Procesando repuesto: {"repuesto_id":83,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 15:06:00][INFO] Movimiento insertado correctamente para repuesto ID: 83
[2025-05-26 15:06:00][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 83
[2025-05-26 15:06:00][INFO] Transacción completada exitosamente
[2025-05-26 15:06:00][INFO] Conexión cerrada
[2025-05-26 15:08:48][INFO] Nueva solicitud recibida
[2025-05-26 15:08:48][INFO] Método HTTP: POST
[2025-05-26 15:08:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 15:08:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":84,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:08:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":84,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:08:48][INFO] Conexión a base de datos establecida
[2025-05-26 15:08:48][INFO] Iniciando transacción
[2025-05-26 15:08:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 15:08:48][INFO] Procesando repuesto: {"repuesto_id":84,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 15:08:48][INFO] Movimiento insertado correctamente para repuesto ID: 84
[2025-05-26 15:08:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 84
[2025-05-26 15:08:48][INFO] Transacción completada exitosamente
[2025-05-26 15:08:48][INFO] Conexión cerrada
[2025-05-26 15:12:09][INFO] Nueva solicitud recibida
[2025-05-26 15:12:09][INFO] Método HTTP: POST
[2025-05-26 15:12:09][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 15:12:09][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":85,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:12:09][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":85,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 15:12:09][INFO] Conexión a base de datos establecida
[2025-05-26 15:12:09][INFO] Iniciando transacción
[2025-05-26 15:12:09][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 15:12:09][INFO] Procesando repuesto: {"repuesto_id":85,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 15:12:09][INFO] Movimiento insertado correctamente para repuesto ID: 85
[2025-05-26 15:12:09][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 85
[2025-05-26 15:12:09][INFO] Transacción completada exitosamente
[2025-05-26 15:12:09][INFO] Conexión cerrada
[2025-05-26 15:14:22][INFO] Nueva solicitud recibida
[2025-05-26 15:14:22][INFO] Método HTTP: POST
[2025-05-26 15:14:22][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 15:14:22][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":86,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 15:14:22][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":86,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 15:14:22][INFO] Conexión a base de datos establecida
[2025-05-26 15:14:22][INFO] Iniciando transacción
[2025-05-26 15:14:22][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 15:14:22][INFO] Procesando repuesto: {"repuesto_id":86,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-26 15:14:22][INFO] Movimiento insertado correctamente para repuesto ID: 86
[2025-05-26 15:14:22][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 86
[2025-05-26 15:14:22][INFO] Transacción completada exitosamente
[2025-05-26 15:14:22][INFO] Conexión cerrada
[2025-05-26 18:24:01][INFO] Nueva solicitud recibida
[2025-05-26 18:24:01][INFO] Método HTTP: POST
[2025-05-26 18:24:01][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 18:24:01][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":87,"cantidad":6,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 18:24:01][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":87,"cantidad":6,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 18:24:01][INFO] Conexión a base de datos establecida
[2025-05-26 18:24:01][INFO] Iniciando transacción
[2025-05-26 18:24:01][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 18:24:01][INFO] Procesando repuesto: {"repuesto_id":87,"cantidad":6,"nombre":"Valvulas Admision 4U"}
[2025-05-26 18:24:01][INFO] Movimiento insertado correctamente para repuesto ID: 87
[2025-05-26 18:24:01][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 87
[2025-05-26 18:24:01][INFO] Transacción completada exitosamente
[2025-05-26 18:24:01][INFO] Conexión cerrada
[2025-05-26 18:25:57][INFO] Nueva solicitud recibida
[2025-05-26 18:25:57][INFO] Método HTTP: POST
[2025-05-26 18:25:57][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 18:25:57][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":88,"cantidad":6,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 18:25:57][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":88,"cantidad":6,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 18:25:57][INFO] Conexión a base de datos establecida
[2025-05-26 18:25:57][INFO] Iniciando transacción
[2025-05-26 18:25:57][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 18:25:57][INFO] Procesando repuesto: {"repuesto_id":88,"cantidad":6,"nombre":"Valvulas Escape 4U"}
[2025-05-26 18:25:57][INFO] Movimiento insertado correctamente para repuesto ID: 88
[2025-05-26 18:25:57][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 88
[2025-05-26 18:25:57][INFO] Transacción completada exitosamente
[2025-05-26 18:25:57][INFO] Conexión cerrada
[2025-05-26 18:47:03][INFO] Nueva solicitud recibida
[2025-05-26 18:47:03][INFO] Método HTTP: POST
[2025-05-26 18:47:03][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 18:47:03][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":89,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 18:47:03][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":89,"cantidad":1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 18:47:03][INFO] Conexión a base de datos establecida
[2025-05-26 18:47:03][INFO] Iniciando transacción
[2025-05-26 18:47:03][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 18:47:03][INFO] Procesando repuesto: {"repuesto_id":89,"cantidad":1,"nombre":"Valvulas Escape 4U"}
[2025-05-26 18:47:03][INFO] Movimiento insertado correctamente para repuesto ID: 89
[2025-05-26 18:47:03][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 89
[2025-05-26 18:47:03][INFO] Transacción completada exitosamente
[2025-05-26 18:47:03][INFO] Conexión cerrada
[2025-05-26 18:57:18][INFO] Nueva solicitud recibida
[2025-05-26 18:57:18][INFO] Método HTTP: POST
[2025-05-26 18:57:18][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 18:57:18][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":90,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 18:57:18][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":90,"cantidad":2,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 18:57:18][INFO] Conexión a base de datos establecida
[2025-05-26 18:57:18][INFO] Iniciando transacción
[2025-05-26 18:57:18][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 18:57:18][INFO] Procesando repuesto: {"repuesto_id":90,"cantidad":2,"nombre":"Valvulas Admision 4U"}
[2025-05-26 18:57:18][INFO] Movimiento insertado correctamente para repuesto ID: 90
[2025-05-26 18:57:18][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 90
[2025-05-26 18:57:18][INFO] Transacción completada exitosamente
[2025-05-26 18:57:18][INFO] Conexión cerrada
[2025-05-26 19:03:40][INFO] Nueva solicitud recibida
[2025-05-26 19:03:40][INFO] Método HTTP: POST
[2025-05-26 19:03:40][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 19:03:40][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":91,"cantidad":10,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 19:03:40][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":91,"cantidad":10,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 19:03:40][INFO] Conexión a base de datos establecida
[2025-05-26 19:03:40][INFO] Iniciando transacción
[2025-05-26 19:03:40][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 19:03:40][INFO] Procesando repuesto: {"repuesto_id":91,"cantidad":10,"nombre":"Valvulas Admision 4U"}
[2025-05-26 19:03:40][INFO] Movimiento insertado correctamente para repuesto ID: 91
[2025-05-26 19:03:40][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 91
[2025-05-26 19:03:40][INFO] Transacción completada exitosamente
[2025-05-26 19:03:40][INFO] Conexión cerrada
[2025-05-26 19:06:25][INFO] Nueva solicitud recibida
[2025-05-26 19:06:25][INFO] Método HTTP: POST
[2025-05-26 19:06:25][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 19:06:25][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":92,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 19:06:25][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":92,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-26 19:06:25][INFO] Conexión a base de datos establecida
[2025-05-26 19:06:25][INFO] Iniciando transacción
[2025-05-26 19:06:25][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 19:06:25][INFO] Procesando repuesto: {"repuesto_id":92,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-05-26 19:06:25][INFO] Movimiento insertado correctamente para repuesto ID: 92
[2025-05-26 19:06:25][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 92
[2025-05-26 19:06:25][INFO] Transacción completada exitosamente
[2025-05-26 19:06:25][INFO] Conexión cerrada
[2025-05-26 19:08:45][INFO] Nueva solicitud recibida
[2025-05-26 19:08:45][INFO] Método HTTP: POST
[2025-05-26 19:08:45][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=f9ppro54ciuncrckoodkm04i0t"}
[2025-05-26 19:08:45][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":93,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 19:08:45][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":93,"cantidad":2,"nombre":"Valvulas Escape 4U"}]}
[2025-05-26 19:08:45][INFO] Conexión a base de datos establecida
[2025-05-26 19:08:45][INFO] Iniciando transacción
[2025-05-26 19:08:45][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-26 19:08:45][INFO] Procesando repuesto: {"repuesto_id":93,"cantidad":2,"nombre":"Valvulas Escape 4U"}
[2025-05-26 19:08:45][INFO] Movimiento insertado correctamente para repuesto ID: 93
[2025-05-26 19:08:45][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 93
[2025-05-26 19:08:45][INFO] Transacción completada exitosamente
[2025-05-26 19:08:45][INFO] Conexión cerrada
[2025-05-28 14:24:31][INFO] Nueva solicitud recibida
[2025-05-28 14:24:31][INFO] Método HTTP: POST
[2025-05-28 14:24:31][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:24:31][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":95,"cantidad":2,"nombre":"Cilindro Frenos Traseros"}]}
[2025-05-28 14:24:31][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":95,"cantidad":2,"nombre":"Cilindro Frenos Traseros"}]}
[2025-05-28 14:24:31][INFO] Conexión a base de datos establecida
[2025-05-28 14:24:31][INFO] Iniciando transacción
[2025-05-28 14:24:31][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:24:31][INFO] Procesando repuesto: {"repuesto_id":95,"cantidad":2,"nombre":"Cilindro Frenos Traseros"}
[2025-05-28 14:24:31][INFO] Movimiento insertado correctamente para repuesto ID: 95
[2025-05-28 14:24:31][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 95
[2025-05-28 14:24:31][INFO] Transacción completada exitosamente
[2025-05-28 14:24:31][INFO] Conexión cerrada
[2025-05-28 14:28:57][INFO] Nueva solicitud recibida
[2025-05-28 14:28:57][INFO] Método HTTP: POST
[2025-05-28 14:28:57][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:28:57][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":2,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-05-28 14:28:57][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":2,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-05-28 14:28:57][INFO] Conexión a base de datos establecida
[2025-05-28 14:28:57][INFO] Iniciando transacción
[2025-05-28 14:28:57][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:28:57][INFO] Procesando repuesto: {"repuesto_id":96,"cantidad":2,"nombre":"Cilindro de Freno Trasero LH"}
[2025-05-28 14:28:57][INFO] Movimiento insertado correctamente para repuesto ID: 96
[2025-05-28 14:28:57][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 96
[2025-05-28 14:28:57][INFO] Transacción completada exitosamente
[2025-05-28 14:28:57][INFO] Conexión cerrada
[2025-05-28 14:33:50][INFO] Nueva solicitud recibida
[2025-05-28 14:33:50][INFO] Método HTTP: POST
[2025-05-28 14:33:50][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:33:50][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":97,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-05-28 14:33:50][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":97,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-05-28 14:33:50][INFO] Conexión a base de datos establecida
[2025-05-28 14:33:50][INFO] Iniciando transacción
[2025-05-28 14:33:50][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:33:50][INFO] Procesando repuesto: {"repuesto_id":97,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}
[2025-05-28 14:33:50][INFO] Movimiento insertado correctamente para repuesto ID: 97
[2025-05-28 14:33:50][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 97
[2025-05-28 14:33:50][INFO] Transacción completada exitosamente
[2025-05-28 14:33:50][INFO] Conexión cerrada
[2025-05-28 14:36:45][INFO] Nueva solicitud recibida
[2025-05-28 14:36:45][INFO] Método HTTP: POST
[2025-05-28 14:36:45][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:36:45][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":98,"cantidad":3,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-05-28 14:36:45][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":98,"cantidad":3,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-05-28 14:36:45][INFO] Conexión a base de datos establecida
[2025-05-28 14:36:45][INFO] Iniciando transacción
[2025-05-28 14:36:45][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:36:45][INFO] Procesando repuesto: {"repuesto_id":98,"cantidad":3,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-05-28 14:36:46][INFO] Movimiento insertado correctamente para repuesto ID: 98
[2025-05-28 14:36:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 98
[2025-05-28 14:36:46][INFO] Transacción completada exitosamente
[2025-05-28 14:36:46][INFO] Conexión cerrada
[2025-05-28 14:40:16][INFO] Nueva solicitud recibida
[2025-05-28 14:40:16][INFO] Método HTTP: POST
[2025-05-28 14:40:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:40:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":99,"cantidad":2,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-05-28 14:40:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":99,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-05-28 14:40:16][INFO] Conexión a base de datos establecida
[2025-05-28 14:40:16][INFO] Iniciando transacción
[2025-05-28 14:40:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:40:16][INFO] Procesando repuesto: {"repuesto_id":99,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-05-28 14:40:16][INFO] Movimiento insertado correctamente para repuesto ID: 99
[2025-05-28 14:40:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 99
[2025-05-28 14:40:16][INFO] Transacción completada exitosamente
[2025-05-28 14:40:16][INFO] Conexión cerrada
[2025-05-28 14:42:49][INFO] Nueva solicitud recibida
[2025-05-28 14:42:49][INFO] Método HTTP: POST
[2025-05-28 14:42:49][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:42:49][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":100,"cantidad":2,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-05-28 14:42:49][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":100,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-05-28 14:42:49][INFO] Conexión a base de datos establecida
[2025-05-28 14:42:49][INFO] Iniciando transacción
[2025-05-28 14:42:49][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:42:49][INFO] Procesando repuesto: {"repuesto_id":100,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-05-28 14:42:49][INFO] Movimiento insertado correctamente para repuesto ID: 100
[2025-05-28 14:42:49][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 100
[2025-05-28 14:42:49][INFO] Transacción completada exitosamente
[2025-05-28 14:42:49][INFO] Conexión cerrada
[2025-05-28 14:45:20][INFO] Nueva solicitud recibida
[2025-05-28 14:45:20][INFO] Método HTTP: POST
[2025-05-28 14:45:20][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:45:20][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":101,"cantidad":5,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-05-28 14:45:20][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":101,"cantidad":5,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-05-28 14:45:20][INFO] Conexión a base de datos establecida
[2025-05-28 14:45:20][INFO] Iniciando transacción
[2025-05-28 14:45:20][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:45:20][INFO] Procesando repuesto: {"repuesto_id":101,"cantidad":5,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-05-28 14:45:20][INFO] Movimiento insertado correctamente para repuesto ID: 101
[2025-05-28 14:45:20][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 101
[2025-05-28 14:45:20][INFO] Transacción completada exitosamente
[2025-05-28 14:45:20][INFO] Conexión cerrada
[2025-05-28 14:50:14][INFO] Nueva solicitud recibida
[2025-05-28 14:50:14][INFO] Método HTTP: POST
[2025-05-28 14:50:14][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 14:50:14][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":102,"cantidad":2,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-05-28 14:50:14][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":102,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-05-28 14:50:14][INFO] Conexión a base de datos establecida
[2025-05-28 14:50:14][INFO] Iniciando transacción
[2025-05-28 14:50:14][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 14:50:14][INFO] Procesando repuesto: {"repuesto_id":102,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-05-28 14:50:14][INFO] Movimiento insertado correctamente para repuesto ID: 102
[2025-05-28 14:50:14][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 102
[2025-05-28 14:50:14][INFO] Transacción completada exitosamente
[2025-05-28 14:50:14][INFO] Conexión cerrada
[2025-05-28 15:18:16][INFO] Nueva solicitud recibida
[2025-05-28 15:18:16][INFO] Método HTTP: POST
[2025-05-28 15:18:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"151","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=ar0evvi973t8isddut8mjkpbq5"}
[2025-05-28 15:18:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":103,"cantidad":1,"nombre":"Anillos STD"}]}
[2025-05-28 15:18:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":103,"cantidad":1,"nombre":"Anillos STD"}]}
[2025-05-28 15:18:16][INFO] Conexión a base de datos establecida
[2025-05-28 15:18:16][INFO] Iniciando transacción
[2025-05-28 15:18:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 15:18:16][INFO] Procesando repuesto: {"repuesto_id":103,"cantidad":1,"nombre":"Anillos STD"}
[2025-05-28 15:18:16][INFO] Movimiento insertado correctamente para repuesto ID: 103
[2025-05-28 15:18:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 103
[2025-05-28 15:18:16][INFO] Transacción completada exitosamente
[2025-05-28 15:18:16][INFO] Conexión cerrada
[2025-05-28 19:40:45][INFO] Nueva solicitud recibida
[2025-05-28 19:40:45][INFO] Método HTTP: POST
[2025-05-28 19:40:45][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"158","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-419,es;q=0.9","Cookie":"PHPSESSID=mhiev9lohnp6aundah52b1se4l"}
[2025-05-28 19:40:45][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":56,"cantidad":-1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-28 19:40:45][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":56,"cantidad":-1,"nombre":"Valvulas Escape 4U"}]}
[2025-05-28 19:40:45][INFO] Conexión a base de datos establecida
[2025-05-28 19:40:45][INFO] Iniciando transacción
[2025-05-28 19:40:45][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 19:40:45][INFO] Procesando repuesto: {"repuesto_id":56,"cantidad":-1,"nombre":"Valvulas Escape 4U"}
[2025-05-28 19:40:45][INFO] Movimiento insertado correctamente para repuesto ID: 56
[2025-05-28 19:40:45][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 56
[2025-05-28 19:40:45][INFO] Transacción completada exitosamente
[2025-05-28 19:40:45][INFO] Conexión cerrada
[2025-05-28 19:41:08][INFO] Nueva solicitud recibida
[2025-05-28 19:41:08][INFO] Método HTTP: POST
[2025-05-28 19:41:08][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-419,es;q=0.9","Cookie":"PHPSESSID=mhiev9lohnp6aundah52b1se4l"}
[2025-05-28 19:41:08][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":57,"cantidad":-1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-28 19:41:08][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":57,"cantidad":-1,"nombre":"Valvulas Admision 4U"}]}
[2025-05-28 19:41:08][INFO] Conexión a base de datos establecida
[2025-05-28 19:41:08][INFO] Iniciando transacción
[2025-05-28 19:41:08][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 19:41:08][INFO] Procesando repuesto: {"repuesto_id":57,"cantidad":-1,"nombre":"Valvulas Admision 4U"}
[2025-05-28 19:41:08][INFO] Movimiento insertado correctamente para repuesto ID: 57
[2025-05-28 19:41:08][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 57
[2025-05-28 19:41:08][INFO] Transacción completada exitosamente
[2025-05-28 19:41:08][INFO] Conexión cerrada
[2025-05-28 19:41:24][INFO] Nueva solicitud recibida
[2025-05-28 19:41:24][INFO] Método HTTP: POST
[2025-05-28 19:41:24][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"152","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-419,es;q=0.9","Cookie":"PHPSESSID=mhiev9lohnp6aundah52b1se4l"}
[2025-05-28 19:41:24][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":103,"cantidad":-1,"nombre":"Anillos STD"}]}
[2025-05-28 19:41:24][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":103,"cantidad":-1,"nombre":"Anillos STD"}]}
[2025-05-28 19:41:24][INFO] Conexión a base de datos establecida
[2025-05-28 19:41:24][INFO] Iniciando transacción
[2025-05-28 19:41:24][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-28 19:41:24][INFO] Procesando repuesto: {"repuesto_id":103,"cantidad":-1,"nombre":"Anillos STD"}
[2025-05-28 19:41:24][INFO] Movimiento insertado correctamente para repuesto ID: 103
[2025-05-28 19:41:24][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 103
[2025-05-28 19:41:24][INFO] Transacción completada exitosamente
[2025-05-28 19:41:24][INFO] Conexión cerrada
[2025-05-29 19:41:41][INFO] Nueva solicitud recibida
[2025-05-29 19:41:41][INFO] Método HTTP: POST
[2025-05-29 19:41:41][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"164","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=u6rsv47rkm77rdjoe4ojrjoo12"}
[2025-05-29 19:41:41][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":95,"cantidad":-1,"nombre":"Cilindro Frenos Traseros"}]}
[2025-05-29 19:41:41][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":95,"cantidad":-1,"nombre":"Cilindro Frenos Traseros"}]}
[2025-05-29 19:41:41][INFO] Conexión a base de datos establecida
[2025-05-29 19:41:41][INFO] Iniciando transacción
[2025-05-29 19:41:41][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-05-29 19:41:41][INFO] Procesando repuesto: {"repuesto_id":95,"cantidad":-1,"nombre":"Cilindro Frenos Traseros"}
[2025-05-29 19:41:41][INFO] Movimiento insertado correctamente para repuesto ID: 95
[2025-05-29 19:41:41][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 95
[2025-05-29 19:41:41][INFO] Transacción completada exitosamente
[2025-05-29 19:41:41][INFO] Conexión cerrada
[2025-06-02 01:30:40][INFO] Nueva solicitud recibida
[2025-06-02 01:30:40][INFO] Método HTTP: POST
[2025-06-02 01:30:40][INFO] Headers recibidos: {"Host":"www.tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Origin":"http:\/\/www.tatarepuestos.cl","Referer":"http:\/\/www.tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=i02lqk1c7garjjvl3trqt5v5s9"}
[2025-06-02 01:30:40][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-0001","notas":"NotasTest01","repuestos":[{"repuesto_id":104,"cantidad":1000,"nombre":"Rep0123"}]}
[2025-06-02 01:30:40][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-0001","notas":"NotasTest01","repuestos":[{"repuesto_id":104,"cantidad":1000,"nombre":"Rep0123"}]}
[2025-06-02 01:30:40][INFO] Conexión a base de datos establecida
[2025-06-02 01:30:40][INFO] Iniciando transacción
[2025-06-02 01:30:40][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-02 01:30:40][INFO] Procesando repuesto: {"repuesto_id":104,"cantidad":1000,"nombre":"Rep0123"}
[2025-06-02 01:30:40][INFO] Movimiento insertado correctamente para repuesto ID: 104
[2025-06-02 01:30:40][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 104
[2025-06-02 01:30:40][INFO] Transacción completada exitosamente
[2025-06-02 01:30:40][INFO] Conexión cerrada
[2025-06-02 01:45:37][INFO] Nueva solicitud recibida
[2025-06-02 01:45:37][INFO] Método HTTP: POST
[2025-06-02 01:45:37][INFO] Headers recibidos: {"Host":"www.tatarepuestos.cl","Connection":"keep-alive","Content-Length":"230","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Sec-GPC":"1","Accept-Language":"en-US,en;q=0.6","Origin":"http:\/\/www.tatarepuestos.cl","Referer":"http:\/\/www.tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=i02lqk1c7garjjvl3trqt5v5s9"}
[2025-06-02 01:45:37][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-00023","notas":"Testo entradas","repuestos":[{"repuesto_id":105,"cantidad":17,"nombre":"RepuestoTest02"},{"repuesto_id":104,"cantidad":77,"nombre":"Rep0123"}]}
[2025-06-02 01:45:37][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"RF-00023","notas":"Testo entradas","repuestos":[{"repuesto_id":105,"cantidad":17,"nombre":"RepuestoTest02"},{"repuesto_id":104,"cantidad":77,"nombre":"Rep0123"}]}
[2025-06-02 01:45:37][INFO] Conexión a base de datos establecida
[2025-06-02 01:45:37][INFO] Iniciando transacción
[2025-06-02 01:45:37][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-02 01:45:37][INFO] Procesando repuesto: {"repuesto_id":105,"cantidad":17,"nombre":"RepuestoTest02"}
[2025-06-02 01:45:37][INFO] Movimiento insertado correctamente para repuesto ID: 105
[2025-06-02 01:45:37][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 105
[2025-06-02 01:45:37][INFO] Procesando repuesto: {"repuesto_id":104,"cantidad":77,"nombre":"Rep0123"}
[2025-06-02 01:45:37][INFO] Movimiento insertado correctamente para repuesto ID: 104
[2025-06-02 01:45:37][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 104
[2025-06-02 01:45:37][INFO] Transacción completada exitosamente
[2025-06-02 01:45:37][INFO] Conexión cerrada
[2025-06-06 14:56:49][INFO] Nueva solicitud recibida
[2025-06-06 14:56:49][INFO] Método HTTP: POST
[2025-06-06 14:56:49][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 14:56:49][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":98,"cantidad":-1,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-06-06 14:56:49][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":98,"cantidad":-1,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-06-06 14:56:49][INFO] Conexión a base de datos establecida
[2025-06-06 14:56:49][INFO] Iniciando transacción
[2025-06-06 14:56:49][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 14:56:49][INFO] Procesando repuesto: {"repuesto_id":98,"cantidad":-1,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-06-06 14:56:49][INFO] Movimiento insertado correctamente para repuesto ID: 98
[2025-06-06 14:56:49][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 98
[2025-06-06 14:56:49][INFO] Transacción completada exitosamente
[2025-06-06 14:56:49][INFO] Conexión cerrada
[2025-06-06 15:00:48][INFO] Nueva solicitud recibida
[2025-06-06 15:00:48][INFO] Método HTTP: POST
[2025-06-06 15:00:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:00:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":106,"cantidad":1,"nombre":"Cilindro Freno Delantero LH"}]}
[2025-06-06 15:00:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":106,"cantidad":1,"nombre":"Cilindro Freno Delantero LH"}]}
[2025-06-06 15:00:48][INFO] Conexión a base de datos establecida
[2025-06-06 15:00:48][INFO] Iniciando transacción
[2025-06-06 15:00:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:00:48][INFO] Procesando repuesto: {"repuesto_id":106,"cantidad":1,"nombre":"Cilindro Freno Delantero LH"}
[2025-06-06 15:00:48][INFO] Movimiento insertado correctamente para repuesto ID: 106
[2025-06-06 15:00:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 106
[2025-06-06 15:00:48][INFO] Transacción completada exitosamente
[2025-06-06 15:00:48][INFO] Conexión cerrada
[2025-06-06 15:04:46][INFO] Nueva solicitud recibida
[2025-06-06 15:04:46][INFO] Método HTTP: POST
[2025-06-06 15:04:46][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:04:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":107,"cantidad":1,"nombre":"CILINDRO FRENO TRASERO LH/RH"}]}
[2025-06-06 15:04:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":107,"cantidad":1,"nombre":"CILINDRO FRENO TRASERO LH\/RH"}]}
[2025-06-06 15:04:46][INFO] Conexión a base de datos establecida
[2025-06-06 15:04:46][INFO] Iniciando transacción
[2025-06-06 15:04:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:04:46][INFO] Procesando repuesto: {"repuesto_id":107,"cantidad":1,"nombre":"CILINDRO FRENO TRASERO LH\/RH"}
[2025-06-06 15:04:46][INFO] Movimiento insertado correctamente para repuesto ID: 107
[2025-06-06 15:04:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 107
[2025-06-06 15:04:46][INFO] Transacción completada exitosamente
[2025-06-06 15:04:46][INFO] Conexión cerrada
[2025-06-06 15:07:09][INFO] Nueva solicitud recibida
[2025-06-06 15:07:09][INFO] Método HTTP: POST
[2025-06-06 15:07:09][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:07:09][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":108,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-06-06 15:07:09][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":108,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-06-06 15:07:09][INFO] Conexión a base de datos establecida
[2025-06-06 15:07:09][INFO] Iniciando transacción
[2025-06-06 15:07:09][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:07:09][INFO] Procesando repuesto: {"repuesto_id":108,"cantidad":1,"nombre":"Cilindro de Freno Trasero RH"}
[2025-06-06 15:07:09][INFO] Movimiento insertado correctamente para repuesto ID: 108
[2025-06-06 15:07:09][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 108
[2025-06-06 15:07:09][INFO] Transacción completada exitosamente
[2025-06-06 15:07:09][INFO] Conexión cerrada
[2025-06-06 15:23:54][INFO] Nueva solicitud recibida
[2025-06-06 15:23:54][INFO] Método HTTP: POST
[2025-06-06 15:23:54][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:23:54][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":109,"cantidad":1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-06-06 15:23:54][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":109,"cantidad":1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-06-06 15:23:54][INFO] Conexión a base de datos establecida
[2025-06-06 15:23:54][INFO] Iniciando transacción
[2025-06-06 15:23:54][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:23:54][INFO] Procesando repuesto: {"repuesto_id":109,"cantidad":1,"nombre":"Cilindro de Freno Trasero LH"}
[2025-06-06 15:23:54][INFO] Movimiento insertado correctamente para repuesto ID: 109
[2025-06-06 15:23:54][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 109
[2025-06-06 15:23:54][INFO] Transacción completada exitosamente
[2025-06-06 15:23:54][INFO] Conexión cerrada
[2025-06-06 15:30:20][INFO] Nueva solicitud recibida
[2025-06-06 15:30:20][INFO] Método HTTP: POST
[2025-06-06 15:30:20][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:30:20][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":110,"cantidad":2,"nombre":"Cilindro Freno Trasero RH"}]}
[2025-06-06 15:30:20][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":110,"cantidad":2,"nombre":"Cilindro Freno Trasero RH"}]}
[2025-06-06 15:30:20][INFO] Conexión a base de datos establecida
[2025-06-06 15:30:20][INFO] Iniciando transacción
[2025-06-06 15:30:20][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:30:20][INFO] Procesando repuesto: {"repuesto_id":110,"cantidad":2,"nombre":"Cilindro Freno Trasero RH"}
[2025-06-06 15:30:20][INFO] Movimiento insertado correctamente para repuesto ID: 110
[2025-06-06 15:30:20][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 110
[2025-06-06 15:30:20][INFO] Transacción completada exitosamente
[2025-06-06 15:30:20][INFO] Conexión cerrada
[2025-06-06 15:33:10][INFO] Nueva solicitud recibida
[2025-06-06 15:33:10][INFO] Método HTTP: POST
[2025-06-06 15:33:10][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:33:10][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":111,"cantidad":4,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-06-06 15:33:10][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":111,"cantidad":4,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-06-06 15:33:10][INFO] Conexión a base de datos establecida
[2025-06-06 15:33:10][INFO] Iniciando transacción
[2025-06-06 15:33:10][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:33:10][INFO] Procesando repuesto: {"repuesto_id":111,"cantidad":4,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-06-06 15:33:10][INFO] Movimiento insertado correctamente para repuesto ID: 111
[2025-06-06 15:33:10][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 111
[2025-06-06 15:33:10][INFO] Transacción completada exitosamente
[2025-06-06 15:33:10][INFO] Conexión cerrada
[2025-06-06 15:36:43][INFO] Nueva solicitud recibida
[2025-06-06 15:36:43][INFO] Método HTTP: POST
[2025-06-06 15:36:43][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:36:43][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":112,"cantidad":1,"nombre":"Cilindro Freno Delantero RH"}]}
[2025-06-06 15:36:43][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":112,"cantidad":1,"nombre":"Cilindro Freno Delantero RH"}]}
[2025-06-06 15:36:43][INFO] Conexión a base de datos establecida
[2025-06-06 15:36:43][INFO] Iniciando transacción
[2025-06-06 15:36:43][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:36:43][INFO] Procesando repuesto: {"repuesto_id":112,"cantidad":1,"nombre":"Cilindro Freno Delantero RH"}
[2025-06-06 15:36:43][INFO] Movimiento insertado correctamente para repuesto ID: 112
[2025-06-06 15:36:43][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 112
[2025-06-06 15:36:43][INFO] Transacción completada exitosamente
[2025-06-06 15:36:43][INFO] Conexión cerrada
[2025-06-06 15:40:18][INFO] Nueva solicitud recibida
[2025-06-06 15:40:18][INFO] Método HTTP: POST
[2025-06-06 15:40:18][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:40:18][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":113,"cantidad":1,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-06-06 15:40:18][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":113,"cantidad":1,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-06-06 15:40:18][INFO] Conexión a base de datos establecida
[2025-06-06 15:40:18][INFO] Iniciando transacción
[2025-06-06 15:40:18][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:40:18][INFO] Procesando repuesto: {"repuesto_id":113,"cantidad":1,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-06-06 15:40:18][INFO] Movimiento insertado correctamente para repuesto ID: 113
[2025-06-06 15:40:18][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 113
[2025-06-06 15:40:18][INFO] Transacción completada exitosamente
[2025-06-06 15:40:18][INFO] Conexión cerrada
[2025-06-06 15:42:57][INFO] Nueva solicitud recibida
[2025-06-06 15:42:57][INFO] Método HTTP: POST
[2025-06-06 15:42:57][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 15:42:57][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":114,"cantidad":1,"nombre":"Cilindro Freno Trasero LH"}]}
[2025-06-06 15:42:57][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":114,"cantidad":1,"nombre":"Cilindro Freno Trasero LH"}]}
[2025-06-06 15:42:57][INFO] Conexión a base de datos establecida
[2025-06-06 15:42:57][INFO] Iniciando transacción
[2025-06-06 15:42:57][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 15:42:57][INFO] Procesando repuesto: {"repuesto_id":114,"cantidad":1,"nombre":"Cilindro Freno Trasero LH"}
[2025-06-06 15:42:57][INFO] Movimiento insertado correctamente para repuesto ID: 114
[2025-06-06 15:42:57][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 114
[2025-06-06 15:42:57][INFO] Transacción completada exitosamente
[2025-06-06 15:42:57][INFO] Conexión cerrada
[2025-06-06 17:24:19][INFO] Nueva solicitud recibida
[2025-06-06 17:24:19][INFO] Método HTTP: POST
[2025-06-06 17:24:19][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 17:24:19][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":115,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 17:24:19][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":115,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 17:24:19][INFO] Conexión a base de datos establecida
[2025-06-06 17:24:19][INFO] Iniciando transacción
[2025-06-06 17:24:19][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 17:24:19][INFO] Procesando repuesto: {"repuesto_id":115,"cantidad":1,"nombre":"Termostato con Carcasa"}
[2025-06-06 17:24:19][INFO] Movimiento insertado correctamente para repuesto ID: 115
[2025-06-06 17:24:19][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 115
[2025-06-06 17:24:19][INFO] Transacción completada exitosamente
[2025-06-06 17:24:19][INFO] Conexión cerrada
[2025-06-06 17:28:29][INFO] Nueva solicitud recibida
[2025-06-06 17:28:29][INFO] Método HTTP: POST
[2025-06-06 17:28:29][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 17:28:29][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":116,"cantidad":2,"nombre":"Termostato Completo"}]}
[2025-06-06 17:28:29][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":116,"cantidad":2,"nombre":"Termostato Completo"}]}
[2025-06-06 17:28:29][INFO] Conexión a base de datos establecida
[2025-06-06 17:28:29][INFO] Iniciando transacción
[2025-06-06 17:28:29][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 17:28:29][INFO] Procesando repuesto: {"repuesto_id":116,"cantidad":2,"nombre":"Termostato Completo"}
[2025-06-06 17:28:29][INFO] Movimiento insertado correctamente para repuesto ID: 116
[2025-06-06 17:28:29][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 116
[2025-06-06 17:28:29][INFO] Transacción completada exitosamente
[2025-06-06 17:28:29][INFO] Conexión cerrada
[2025-06-06 18:20:13][INFO] Nueva solicitud recibida
[2025-06-06 18:20:13][INFO] Método HTTP: POST
[2025-06-06 18:20:13][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 18:20:13][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":117,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:20:13][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":117,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:20:13][INFO] Conexión a base de datos establecida
[2025-06-06 18:20:13][INFO] Iniciando transacción
[2025-06-06 18:20:13][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 18:20:13][INFO] Procesando repuesto: {"repuesto_id":117,"cantidad":1,"nombre":"Termostato con Carcasa"}
[2025-06-06 18:20:13][INFO] Movimiento insertado correctamente para repuesto ID: 117
[2025-06-06 18:20:13][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 117
[2025-06-06 18:20:13][INFO] Transacción completada exitosamente
[2025-06-06 18:20:13][INFO] Conexión cerrada
[2025-06-06 18:23:57][INFO] Nueva solicitud recibida
[2025-06-06 18:23:57][INFO] Método HTTP: POST
[2025-06-06 18:23:57][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 18:23:57][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-06 18:23:57][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-06 18:23:57][INFO] Conexión a base de datos establecida
[2025-06-06 18:23:57][INFO] Iniciando transacción
[2025-06-06 18:23:57][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 18:23:57][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}
[2025-06-06 18:23:57][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-06 18:23:57][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-06 18:23:57][INFO] Transacción completada exitosamente
[2025-06-06 18:23:57][INFO] Conexión cerrada
[2025-06-06 18:26:44][INFO] Nueva solicitud recibida
[2025-06-06 18:26:44][INFO] Método HTTP: POST
[2025-06-06 18:26:44][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 18:26:44][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:26:44][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:26:44][INFO] Conexión a base de datos establecida
[2025-06-06 18:26:44][INFO] Iniciando transacción
[2025-06-06 18:26:44][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 18:26:44][INFO] Procesando repuesto: {"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}
[2025-06-06 18:26:44][INFO] Movimiento insertado correctamente para repuesto ID: 119
[2025-06-06 18:26:44][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 119
[2025-06-06 18:26:44][INFO] Transacción completada exitosamente
[2025-06-06 18:26:44][INFO] Conexión cerrada
[2025-06-06 18:27:35][INFO] Nueva solicitud recibida
[2025-06-06 18:27:35][INFO] Método HTTP: POST
[2025-06-06 18:27:35][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 18:27:35][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:27:35][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}]}
[2025-06-06 18:27:35][INFO] Conexión a base de datos establecida
[2025-06-06 18:27:35][INFO] Iniciando transacción
[2025-06-06 18:27:35][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 18:27:35][INFO] Procesando repuesto: {"repuesto_id":119,"cantidad":1,"nombre":"Termostato con Carcasa"}
[2025-06-06 18:27:35][INFO] Movimiento insertado correctamente para repuesto ID: 119
[2025-06-06 18:27:35][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 119
[2025-06-06 18:27:35][INFO] Transacción completada exitosamente
[2025-06-06 18:27:35][INFO] Conexión cerrada
[2025-06-06 18:37:35][INFO] Nueva solicitud recibida
[2025-06-06 18:37:35][INFO] Método HTTP: POST
[2025-06-06 18:37:35][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 18:37:35][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-06 18:37:35][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-06 18:37:35][INFO] Conexión a base de datos establecida
[2025-06-06 18:37:35][INFO] Iniciando transacción
[2025-06-06 18:37:35][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 18:37:35][INFO] Procesando repuesto: {"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-06 18:37:35][INFO] Movimiento insertado correctamente para repuesto ID: 120
[2025-06-06 18:37:35][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 120
[2025-06-06 18:37:35][INFO] Transacción completada exitosamente
[2025-06-06 18:37:35][INFO] Conexión cerrada
[2025-06-06 20:57:21][INFO] Nueva solicitud recibida
[2025-06-06 20:57:21][INFO] Método HTTP: POST
[2025-06-06 20:57:21][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-06 20:57:21][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":121,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-06 20:57:21][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":121,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-06 20:57:21][INFO] Conexión a base de datos establecida
[2025-06-06 20:57:21][INFO] Iniciando transacción
[2025-06-06 20:57:21][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-06 20:57:21][INFO] Procesando repuesto: {"repuesto_id":121,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-06 20:57:21][INFO] Movimiento insertado correctamente para repuesto ID: 121
[2025-06-06 20:57:21][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 121
[2025-06-06 20:57:21][INFO] Transacción completada exitosamente
[2025-06-06 20:57:21][INFO] Conexión cerrada
[2025-06-07 14:34:28][INFO] Nueva solicitud recibida
[2025-06-07 14:34:28][INFO] Método HTTP: POST
[2025-06-07 14:34:28][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-07 14:34:28][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":122,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-07 14:34:28][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":122,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-07 14:34:28][INFO] Conexión a base de datos establecida
[2025-06-07 14:34:28][INFO] Iniciando transacción
[2025-06-07 14:34:28][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-07 14:34:28][INFO] Procesando repuesto: {"repuesto_id":122,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-07 14:34:28][INFO] Movimiento insertado correctamente para repuesto ID: 122
[2025-06-07 14:34:28][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 122
[2025-06-07 14:34:28][INFO] Transacción completada exitosamente
[2025-06-07 14:34:28][INFO] Conexión cerrada
[2025-06-07 14:39:00][INFO] Nueva solicitud recibida
[2025-06-07 14:39:00][INFO] Método HTTP: POST
[2025-06-07 14:39:00][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=g9bki6lsl1s5gf4mlik4tpqktl"}
[2025-06-07 14:39:00][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":123,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-07 14:39:00][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":123,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-07 14:39:00][INFO] Conexión a base de datos establecida
[2025-06-07 14:39:00][INFO] Iniciando transacción
[2025-06-07 14:39:00][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-07 14:39:00][INFO] Procesando repuesto: {"repuesto_id":123,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-07 14:39:00][INFO] Movimiento insertado correctamente para repuesto ID: 123
[2025-06-07 14:39:00][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 123
[2025-06-07 14:39:00][INFO] Transacción completada exitosamente
[2025-06-07 14:39:00][INFO] Conexión cerrada
[2025-06-09 18:33:02][INFO] Nueva solicitud recibida
[2025-06-09 18:33:02][INFO] Método HTTP: POST
[2025-06-09 18:33:02][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:33:02][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":87,"cantidad":-1,"nombre":"Valvulas Admision 4U"}]}
[2025-06-09 18:33:02][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":87,"cantidad":-1,"nombre":"Valvulas Admision 4U"}]}
[2025-06-09 18:33:02][INFO] Conexión a base de datos establecida
[2025-06-09 18:33:02][INFO] Iniciando transacción
[2025-06-09 18:33:02][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:33:02][INFO] Procesando repuesto: {"repuesto_id":87,"cantidad":-1,"nombre":"Valvulas Admision 4U"}
[2025-06-09 18:33:02][INFO] Movimiento insertado correctamente para repuesto ID: 87
[2025-06-09 18:33:02][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 87
[2025-06-09 18:33:02][INFO] Transacción completada exitosamente
[2025-06-09 18:33:02][INFO] Conexión cerrada
[2025-06-09 18:36:15][INFO] Nueva solicitud recibida
[2025-06-09 18:36:15][INFO] Método HTTP: POST
[2025-06-09 18:36:15][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:36:15][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":124,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-06-09 18:36:15][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":124,"cantidad":1,"nombre":"Valvulas Admision 4U"}]}
[2025-06-09 18:36:15][INFO] Conexión a base de datos establecida
[2025-06-09 18:36:15][INFO] Iniciando transacción
[2025-06-09 18:36:15][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:36:15][INFO] Procesando repuesto: {"repuesto_id":124,"cantidad":1,"nombre":"Valvulas Admision 4U"}
[2025-06-09 18:36:15][INFO] Movimiento insertado correctamente para repuesto ID: 124
[2025-06-09 18:36:15][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 124
[2025-06-09 18:36:15][INFO] Transacción completada exitosamente
[2025-06-09 18:36:15][INFO] Conexión cerrada
[2025-06-09 18:40:16][INFO] Nueva solicitud recibida
[2025-06-09 18:40:16][INFO] Método HTTP: POST
[2025-06-09 18:40:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:40:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":125,"cantidad":1,"nombre":"Tapa Termostato"}]}
[2025-06-09 18:40:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":125,"cantidad":1,"nombre":"Tapa Termostato"}]}
[2025-06-09 18:40:16][INFO] Conexión a base de datos establecida
[2025-06-09 18:40:16][INFO] Iniciando transacción
[2025-06-09 18:40:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:40:16][INFO] Procesando repuesto: {"repuesto_id":125,"cantidad":1,"nombre":"Tapa Termostato"}
[2025-06-09 18:40:16][INFO] Movimiento insertado correctamente para repuesto ID: 125
[2025-06-09 18:40:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 125
[2025-06-09 18:40:16][INFO] Transacción completada exitosamente
[2025-06-09 18:40:16][INFO] Conexión cerrada
[2025-06-09 18:43:19][INFO] Nueva solicitud recibida
[2025-06-09 18:43:19][INFO] Método HTTP: POST
[2025-06-09 18:43:19][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:43:19][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":126,"cantidad":1,"nombre":"Base Termostato"}]}
[2025-06-09 18:43:19][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":126,"cantidad":1,"nombre":"Base Termostato"}]}
[2025-06-09 18:43:19][INFO] Conexión a base de datos establecida
[2025-06-09 18:43:19][INFO] Iniciando transacción
[2025-06-09 18:43:19][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:43:19][INFO] Procesando repuesto: {"repuesto_id":126,"cantidad":1,"nombre":"Base Termostato"}
[2025-06-09 18:43:19][INFO] Movimiento insertado correctamente para repuesto ID: 126
[2025-06-09 18:43:19][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 126
[2025-06-09 18:43:19][INFO] Transacción completada exitosamente
[2025-06-09 18:43:19][INFO] Conexión cerrada
[2025-06-09 18:47:15][INFO] Nueva solicitud recibida
[2025-06-09 18:47:15][INFO] Método HTTP: POST
[2025-06-09 18:47:15][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:47:15][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":127,"cantidad":1,"nombre":"Termostato con Base"}]}
[2025-06-09 18:47:15][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":127,"cantidad":1,"nombre":"Termostato con Base"}]}
[2025-06-09 18:47:15][INFO] Conexión a base de datos establecida
[2025-06-09 18:47:15][INFO] Iniciando transacción
[2025-06-09 18:47:15][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:47:15][INFO] Procesando repuesto: {"repuesto_id":127,"cantidad":1,"nombre":"Termostato con Base"}
[2025-06-09 18:47:15][INFO] Movimiento insertado correctamente para repuesto ID: 127
[2025-06-09 18:47:15][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 127
[2025-06-09 18:47:15][INFO] Transacción completada exitosamente
[2025-06-09 18:47:15][INFO] Conexión cerrada
[2025-06-09 18:47:57][INFO] Nueva solicitud recibida
[2025-06-09 18:47:57][INFO] Método HTTP: POST
[2025-06-09 18:47:57][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:47:57][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-09 18:47:57][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-09 18:47:57][INFO] Conexión a base de datos establecida
[2025-06-09 18:47:57][INFO] Iniciando transacción
[2025-06-09 18:47:57][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:47:57][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}
[2025-06-09 18:47:57][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-09 18:47:57][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-09 18:47:57][INFO] Transacción completada exitosamente
[2025-06-09 18:47:57][INFO] Conexión cerrada
[2025-06-09 18:51:14][INFO] Nueva solicitud recibida
[2025-06-09 18:51:14][INFO] Método HTTP: POST
[2025-06-09 18:51:14][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:51:14][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":128,"cantidad":2,"nombre":"Termostato con Carcasa"}]}
[2025-06-09 18:51:14][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":128,"cantidad":2,"nombre":"Termostato con Carcasa"}]}
[2025-06-09 18:51:14][INFO] Conexión a base de datos establecida
[2025-06-09 18:51:14][INFO] Iniciando transacción
[2025-06-09 18:51:14][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:51:14][INFO] Procesando repuesto: {"repuesto_id":128,"cantidad":2,"nombre":"Termostato con Carcasa"}
[2025-06-09 18:51:14][INFO] Movimiento insertado correctamente para repuesto ID: 128
[2025-06-09 18:51:14][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 128
[2025-06-09 18:51:14][INFO] Transacción completada exitosamente
[2025-06-09 18:51:14][INFO] Conexión cerrada
[2025-06-09 18:58:38][INFO] Nueva solicitud recibida
[2025-06-09 18:58:38][INFO] Método HTTP: POST
[2025-06-09 18:58:38][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 18:58:38][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":129,"cantidad":2,"nombre":"Termostato con Base y Sensor "}]}
[2025-06-09 18:58:38][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":129,"cantidad":2,"nombre":"Termostato con Base y Sensor "}]}
[2025-06-09 18:58:38][INFO] Conexión a base de datos establecida
[2025-06-09 18:58:38][INFO] Iniciando transacción
[2025-06-09 18:58:38][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 18:58:38][INFO] Procesando repuesto: {"repuesto_id":129,"cantidad":2,"nombre":"Termostato con Base y Sensor "}
[2025-06-09 18:58:38][INFO] Movimiento insertado correctamente para repuesto ID: 129
[2025-06-09 18:58:38][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 129
[2025-06-09 18:58:38][INFO] Transacción completada exitosamente
[2025-06-09 18:58:38][INFO] Conexión cerrada
[2025-06-09 19:37:02][INFO] Nueva solicitud recibida
[2025-06-09 19:37:02][INFO] Método HTTP: POST
[2025-06-09 19:37:02][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 19:37:02][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":130,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-09 19:37:02][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":130,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-09 19:37:02][INFO] Conexión a base de datos establecida
[2025-06-09 19:37:02][INFO] Iniciando transacción
[2025-06-09 19:37:02][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 19:37:02][INFO] Procesando repuesto: {"repuesto_id":130,"cantidad":1,"nombre":"Caja Salida Agua"}
[2025-06-09 19:37:02][INFO] Movimiento insertado correctamente para repuesto ID: 130
[2025-06-09 19:37:02][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 130
[2025-06-09 19:37:02][INFO] Transacción completada exitosamente
[2025-06-09 19:37:02][INFO] Conexión cerrada
[2025-06-09 19:39:34][INFO] Nueva solicitud recibida
[2025-06-09 19:39:34][INFO] Método HTTP: POST
[2025-06-09 19:39:34][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"167","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 19:39:34][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":131,"cantidad":1,"nombre":"Caja Salida Agua con Sensor"}]}
[2025-06-09 19:39:34][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":131,"cantidad":1,"nombre":"Caja Salida Agua con Sensor"}]}
[2025-06-09 19:39:34][INFO] Conexión a base de datos establecida
[2025-06-09 19:39:34][INFO] Iniciando transacción
[2025-06-09 19:39:34][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 19:39:34][INFO] Procesando repuesto: {"repuesto_id":131,"cantidad":1,"nombre":"Caja Salida Agua con Sensor"}
[2025-06-09 19:39:34][INFO] Movimiento insertado correctamente para repuesto ID: 131
[2025-06-09 19:39:34][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 131
[2025-06-09 19:39:34][INFO] Transacción completada exitosamente
[2025-06-09 19:39:34][INFO] Conexión cerrada
[2025-06-09 19:43:43][INFO] Nueva solicitud recibida
[2025-06-09 19:43:43][INFO] Método HTTP: POST
[2025-06-09 19:43:43][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 19:43:43][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:43:43][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:43:43][INFO] Conexión a base de datos establecida
[2025-06-09 19:43:43][INFO] Iniciando transacción
[2025-06-09 19:43:43][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 19:43:43][INFO] Procesando repuesto: {"repuesto_id":120,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-09 19:43:43][INFO] Movimiento insertado correctamente para repuesto ID: 120
[2025-06-09 19:43:43][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 120
[2025-06-09 19:43:43][INFO] Transacción completada exitosamente
[2025-06-09 19:43:43][INFO] Conexión cerrada
[2025-06-09 19:52:46][INFO] Nueva solicitud recibida
[2025-06-09 19:52:46][INFO] Método HTTP: POST
[2025-06-09 19:52:46][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 19:52:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":133,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:52:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":133,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:52:46][INFO] Conexión a base de datos establecida
[2025-06-09 19:52:46][INFO] Iniciando transacción
[2025-06-09 19:52:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 19:52:46][INFO] Procesando repuesto: {"repuesto_id":133,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-09 19:52:46][INFO] Movimiento insertado correctamente para repuesto ID: 133
[2025-06-09 19:52:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 133
[2025-06-09 19:52:46][INFO] Transacción completada exitosamente
[2025-06-09 19:52:46][INFO] Conexión cerrada
[2025-06-09 19:57:29][INFO] Nueva solicitud recibida
[2025-06-09 19:57:29][INFO] Método HTTP: POST
[2025-06-09 19:57:29][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 19:57:29][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":134,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:57:29][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":134,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 19:57:29][INFO] Conexión a base de datos establecida
[2025-06-09 19:57:29][INFO] Iniciando transacción
[2025-06-09 19:57:29][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 19:57:29][INFO] Procesando repuesto: {"repuesto_id":134,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-09 19:57:29][INFO] Movimiento insertado correctamente para repuesto ID: 134
[2025-06-09 19:57:29][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 134
[2025-06-09 19:57:29][INFO] Transacción completada exitosamente
[2025-06-09 19:57:29][INFO] Conexión cerrada
[2025-06-09 20:01:25][INFO] Nueva solicitud recibida
[2025-06-09 20:01:25][INFO] Método HTTP: POST
[2025-06-09 20:01:25][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"159","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-09 20:01:25][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":135,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 20:01:25][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":135,"cantidad":1,"nombre":"Termostato Completo"}]}
[2025-06-09 20:01:25][INFO] Conexión a base de datos establecida
[2025-06-09 20:01:25][INFO] Iniciando transacción
[2025-06-09 20:01:25][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-09 20:01:25][INFO] Procesando repuesto: {"repuesto_id":135,"cantidad":1,"nombre":"Termostato Completo"}
[2025-06-09 20:01:25][INFO] Movimiento insertado correctamente para repuesto ID: 135
[2025-06-09 20:01:25][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 135
[2025-06-09 20:01:25][INFO] Transacción completada exitosamente
[2025-06-09 20:01:25][INFO] Conexión cerrada
[2025-06-10 16:40:34][INFO] Nueva solicitud recibida
[2025-06-10 16:40:34][INFO] Método HTTP: POST
[2025-06-10 16:40:34][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"161","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"es-ES,es;q=0.9","Cookie":"PHPSESSID=lhj2uiqgepfr0vnh5nfocuus1c"}
[2025-06-10 16:40:34][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-10 16:40:34][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-10 16:40:34][INFO] Conexión a base de datos establecida
[2025-06-10 16:40:34][INFO] Iniciando transacción
[2025-06-10 16:40:34][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-10 16:40:34][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}
[2025-06-10 16:40:34][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-10 16:40:34][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-10 16:40:34][INFO] Transacción completada exitosamente
[2025-06-10 16:40:34][INFO] Conexión cerrada
[2025-06-11 14:11:22][INFO] Nueva solicitud recibida
[2025-06-11 14:11:22][INFO] Método HTTP: POST
[2025-06-11 14:11:22][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:11:22][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":136,"cantidad":1,"nombre":"Caja Termostato"}]}
[2025-06-11 14:11:22][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":136,"cantidad":1,"nombre":"Caja Termostato"}]}
[2025-06-11 14:11:22][INFO] Conexión a base de datos establecida
[2025-06-11 14:11:22][INFO] Iniciando transacción
[2025-06-11 14:11:22][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:11:22][INFO] Procesando repuesto: {"repuesto_id":136,"cantidad":1,"nombre":"Caja Termostato"}
[2025-06-11 14:11:22][INFO] Movimiento insertado correctamente para repuesto ID: 136
[2025-06-11 14:11:22][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 136
[2025-06-11 14:11:22][INFO] Transacción completada exitosamente
[2025-06-11 14:11:22][INFO] Conexión cerrada
[2025-06-11 14:14:59][INFO] Nueva solicitud recibida
[2025-06-11 14:14:59][INFO] Método HTTP: POST
[2025-06-11 14:14:59][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"155","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:14:59][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":137,"cantidad":1,"nombre":"Caja Termostato"}]}
[2025-06-11 14:14:59][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":137,"cantidad":1,"nombre":"Caja Termostato"}]}
[2025-06-11 14:14:59][INFO] Conexión a base de datos establecida
[2025-06-11 14:14:59][INFO] Iniciando transacción
[2025-06-11 14:14:59][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:14:59][INFO] Procesando repuesto: {"repuesto_id":137,"cantidad":1,"nombre":"Caja Termostato"}
[2025-06-11 14:14:59][INFO] Movimiento insertado correctamente para repuesto ID: 137
[2025-06-11 14:14:59][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 137
[2025-06-11 14:14:59][INFO] Transacción completada exitosamente
[2025-06-11 14:14:59][INFO] Conexión cerrada
[2025-06-11 14:20:48][INFO] Nueva solicitud recibida
[2025-06-11 14:20:48][INFO] Método HTTP: POST
[2025-06-11 14:20:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:20:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":138,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-11 14:20:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":138,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-11 14:20:48][INFO] Conexión a base de datos establecida
[2025-06-11 14:20:48][INFO] Iniciando transacción
[2025-06-11 14:20:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:20:48][INFO] Procesando repuesto: {"repuesto_id":138,"cantidad":1,"nombre":"Caja Salida Agua"}
[2025-06-11 14:20:48][INFO] Movimiento insertado correctamente para repuesto ID: 138
[2025-06-11 14:20:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 138
[2025-06-11 14:20:48][INFO] Transacción completada exitosamente
[2025-06-11 14:20:48][INFO] Conexión cerrada
[2025-06-11 14:28:39][INFO] Nueva solicitud recibida
[2025-06-11 14:28:39][INFO] Método HTTP: POST
[2025-06-11 14:28:39][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:28:39][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":139,"cantidad":1,"nombre":"Deposito Auxiliar"}]}
[2025-06-11 14:28:39][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":139,"cantidad":1,"nombre":"Deposito Auxiliar"}]}
[2025-06-11 14:28:39][INFO] Conexión a base de datos establecida
[2025-06-11 14:28:39][INFO] Iniciando transacción
[2025-06-11 14:28:39][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:28:39][INFO] Procesando repuesto: {"repuesto_id":139,"cantidad":1,"nombre":"Deposito Auxiliar"}
[2025-06-11 14:28:39][INFO] Movimiento insertado correctamente para repuesto ID: 139
[2025-06-11 14:28:39][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 139
[2025-06-11 14:28:39][INFO] Transacción completada exitosamente
[2025-06-11 14:28:39][INFO] Conexión cerrada
[2025-06-11 14:45:41][INFO] Nueva solicitud recibida
[2025-06-11 14:45:41][INFO] Método HTTP: POST
[2025-06-11 14:45:41][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:45:41][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":140,"cantidad":1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-11 14:45:41][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":140,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-11 14:45:41][INFO] Conexión a base de datos establecida
[2025-06-11 14:45:41][INFO] Iniciando transacción
[2025-06-11 14:45:41][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:45:41][INFO] Procesando repuesto: {"repuesto_id":140,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-11 14:45:41][INFO] Movimiento insertado correctamente para repuesto ID: 140
[2025-06-11 14:45:41][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 140
[2025-06-11 14:45:41][INFO] Transacción completada exitosamente
[2025-06-11 14:45:41][INFO] Conexión cerrada
[2025-06-11 14:48:51][INFO] Nueva solicitud recibida
[2025-06-11 14:48:51][INFO] Método HTTP: POST
[2025-06-11 14:48:51][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 14:48:51][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":141,"cantidad":1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-11 14:48:51][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":141,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-11 14:48:51][INFO] Conexión a base de datos establecida
[2025-06-11 14:48:51][INFO] Iniciando transacción
[2025-06-11 14:48:51][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 14:48:51][INFO] Procesando repuesto: {"repuesto_id":141,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-11 14:48:51][INFO] Movimiento insertado correctamente para repuesto ID: 141
[2025-06-11 14:48:51][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 141
[2025-06-11 14:48:51][INFO] Transacción completada exitosamente
[2025-06-11 14:48:51][INFO] Conexión cerrada
[2025-06-11 15:58:12][INFO] Nueva solicitud recibida
[2025-06-11 15:58:12][INFO] Método HTTP: POST
[2025-06-11 15:58:12][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 15:58:12][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":142,"cantidad":1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-11 15:58:12][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":142,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-11 15:58:12][INFO] Conexión a base de datos establecida
[2025-06-11 15:58:12][INFO] Iniciando transacción
[2025-06-11 15:58:12][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 15:58:12][INFO] Procesando repuesto: {"repuesto_id":142,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-11 15:58:12][INFO] Movimiento insertado correctamente para repuesto ID: 142
[2025-06-11 15:58:12][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 142
[2025-06-11 15:58:12][INFO] Transacción completada exitosamente
[2025-06-11 15:58:12][INFO] Conexión cerrada
[2025-06-11 16:01:55][INFO] Nueva solicitud recibida
[2025-06-11 16:01:55][INFO] Método HTTP: POST
[2025-06-11 16:01:55][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:01:55][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":143,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:01:55][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":143,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:01:55][INFO] Conexión a base de datos establecida
[2025-06-11 16:01:55][INFO] Iniciando transacción
[2025-06-11 16:01:55][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:01:55][INFO] Procesando repuesto: {"repuesto_id":143,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 16:01:55][INFO] Movimiento insertado correctamente para repuesto ID: 143
[2025-06-11 16:01:55][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 143
[2025-06-11 16:01:55][INFO] Transacción completada exitosamente
[2025-06-11 16:01:55][INFO] Conexión cerrada
[2025-06-11 16:06:21][INFO] Nueva solicitud recibida
[2025-06-11 16:06:21][INFO] Método HTTP: POST
[2025-06-11 16:06:21][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:06:21][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":144,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:06:21][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":144,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:06:21][INFO] Conexión a base de datos establecida
[2025-06-11 16:06:21][INFO] Iniciando transacción
[2025-06-11 16:06:21][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:06:21][INFO] Procesando repuesto: {"repuesto_id":144,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 16:06:21][INFO] Movimiento insertado correctamente para repuesto ID: 144
[2025-06-11 16:06:21][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 144
[2025-06-11 16:06:21][INFO] Transacción completada exitosamente
[2025-06-11 16:06:21][INFO] Conexión cerrada
[2025-06-11 16:10:56][INFO] Nueva solicitud recibida
[2025-06-11 16:10:56][INFO] Método HTTP: POST
[2025-06-11 16:10:56][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:10:56][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":145,"cantidad":1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-11 16:10:56][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":145,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-11 16:10:56][INFO] Conexión a base de datos establecida
[2025-06-11 16:10:56][INFO] Iniciando transacción
[2025-06-11 16:10:56][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:10:56][INFO] Procesando repuesto: {"repuesto_id":145,"cantidad":1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-11 16:10:56][INFO] Movimiento insertado correctamente para repuesto ID: 145
[2025-06-11 16:10:56][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 145
[2025-06-11 16:10:56][INFO] Transacción completada exitosamente
[2025-06-11 16:10:56][INFO] Conexión cerrada
[2025-06-11 16:15:30][INFO] Nueva solicitud recibida
[2025-06-11 16:15:30][INFO] Método HTTP: POST
[2025-06-11 16:15:30][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:15:30][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":146,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:15:30][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":146,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:15:30][INFO] Conexión a base de datos establecida
[2025-06-11 16:15:30][INFO] Iniciando transacción
[2025-06-11 16:15:30][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:15:30][INFO] Procesando repuesto: {"repuesto_id":146,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 16:15:30][INFO] Movimiento insertado correctamente para repuesto ID: 146
[2025-06-11 16:15:30][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 146
[2025-06-11 16:15:30][INFO] Transacción completada exitosamente
[2025-06-11 16:15:30][INFO] Conexión cerrada
[2025-06-11 16:20:10][INFO] Nueva solicitud recibida
[2025-06-11 16:20:10][INFO] Método HTTP: POST
[2025-06-11 16:20:10][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:20:10][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":147,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:20:10][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":147,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:20:10][INFO] Conexión a base de datos establecida
[2025-06-11 16:20:10][INFO] Iniciando transacción
[2025-06-11 16:20:10][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:20:10][INFO] Procesando repuesto: {"repuesto_id":147,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 16:20:10][INFO] Movimiento insertado correctamente para repuesto ID: 147
[2025-06-11 16:20:10][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 147
[2025-06-11 16:20:10][INFO] Transacción completada exitosamente
[2025-06-11 16:20:10][INFO] Conexión cerrada
[2025-06-11 16:24:18][INFO] Nueva solicitud recibida
[2025-06-11 16:24:18][INFO] Método HTTP: POST
[2025-06-11 16:24:18][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:24:18][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":148,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:24:18][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":148,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 16:24:18][INFO] Conexión a base de datos establecida
[2025-06-11 16:24:18][INFO] Iniciando transacción
[2025-06-11 16:24:18][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:24:18][INFO] Procesando repuesto: {"repuesto_id":148,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 16:24:18][INFO] Movimiento insertado correctamente para repuesto ID: 148
[2025-06-11 16:24:18][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 148
[2025-06-11 16:24:18][INFO] Transacción completada exitosamente
[2025-06-11 16:24:18][INFO] Conexión cerrada
[2025-06-11 16:27:15][INFO] Nueva solicitud recibida
[2025-06-11 16:27:15][INFO] Método HTTP: POST
[2025-06-11 16:27:15][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 16:27:15][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":150,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-11 16:27:15][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":150,"cantidad":1,"nombre":"Caja Salida Agua"}]}
[2025-06-11 16:27:15][INFO] Conexión a base de datos establecida
[2025-06-11 16:27:15][INFO] Iniciando transacción
[2025-06-11 16:27:15][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 16:27:15][INFO] Procesando repuesto: {"repuesto_id":150,"cantidad":1,"nombre":"Caja Salida Agua"}
[2025-06-11 16:27:15][INFO] Movimiento insertado correctamente para repuesto ID: 150
[2025-06-11 16:27:15][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 150
[2025-06-11 16:27:15][INFO] Transacción completada exitosamente
[2025-06-11 16:27:15][INFO] Conexión cerrada
[2025-06-11 17:20:45][INFO] Nueva solicitud recibida
[2025-06-11 17:20:45][INFO] Método HTTP: POST
[2025-06-11 17:20:45][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 17:20:45][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":151,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:20:45][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":151,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:20:45][INFO] Conexión a base de datos establecida
[2025-06-11 17:20:45][INFO] Iniciando transacción
[2025-06-11 17:20:45][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 17:20:45][INFO] Procesando repuesto: {"repuesto_id":151,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 17:20:45][INFO] Movimiento insertado correctamente para repuesto ID: 151
[2025-06-11 17:20:45][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 151
[2025-06-11 17:20:45][INFO] Transacción completada exitosamente
[2025-06-11 17:20:45][INFO] Conexión cerrada
[2025-06-11 17:25:54][INFO] Nueva solicitud recibida
[2025-06-11 17:25:54][INFO] Método HTTP: POST
[2025-06-11 17:25:54][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 17:25:54][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:25:54][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:25:54][INFO] Conexión a base de datos establecida
[2025-06-11 17:25:54][INFO] Iniciando transacción
[2025-06-11 17:25:54][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 17:25:54][INFO] Procesando repuesto: {"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 17:25:54][INFO] Movimiento insertado correctamente para repuesto ID: 152
[2025-06-11 17:25:54][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 152
[2025-06-11 17:25:54][INFO] Transacción completada exitosamente
[2025-06-11 17:25:54][INFO] Conexión cerrada
[2025-06-11 17:29:29][INFO] Nueva solicitud recibida
[2025-06-11 17:29:29][INFO] Método HTTP: POST
[2025-06-11 17:29:29][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 17:29:29][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":153,"cantidad":2,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:29:29][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":153,"cantidad":2,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:29:29][INFO] Conexión a base de datos establecida
[2025-06-11 17:29:29][INFO] Iniciando transacción
[2025-06-11 17:29:29][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 17:29:29][INFO] Procesando repuesto: {"repuesto_id":153,"cantidad":2,"nombre":"Kit Distribucion"}
[2025-06-11 17:29:29][INFO] Movimiento insertado correctamente para repuesto ID: 153
[2025-06-11 17:29:29][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 153
[2025-06-11 17:29:29][INFO] Transacción completada exitosamente
[2025-06-11 17:29:29][INFO] Conexión cerrada
[2025-06-11 17:32:36][INFO] Nueva solicitud recibida
[2025-06-11 17:32:36][INFO] Método HTTP: POST
[2025-06-11 17:32:36][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 17:32:36][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:32:36][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 17:32:36][INFO] Conexión a base de datos establecida
[2025-06-11 17:32:36][INFO] Iniciando transacción
[2025-06-11 17:32:36][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 17:32:36][INFO] Procesando repuesto: {"repuesto_id":152,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 17:32:36][INFO] Movimiento insertado correctamente para repuesto ID: 152
[2025-06-11 17:32:36][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 152
[2025-06-11 17:32:36][INFO] Transacción completada exitosamente
[2025-06-11 17:32:36][INFO] Conexión cerrada
[2025-06-11 17:59:08][INFO] Nueva solicitud recibida
[2025-06-11 17:59:08][INFO] Método HTTP: POST
[2025-06-11 17:59:08][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 17:59:08][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":155,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-11 17:59:08][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":155,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-11 17:59:08][INFO] Conexión a base de datos establecida
[2025-06-11 17:59:08][INFO] Iniciando transacción
[2025-06-11 17:59:08][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 17:59:08][INFO] Procesando repuesto: {"repuesto_id":155,"cantidad":3,"nombre":"Tensor Correa Accesorio"}
[2025-06-11 17:59:08][INFO] Movimiento insertado correctamente para repuesto ID: 155
[2025-06-11 17:59:08][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 155
[2025-06-11 17:59:08][INFO] Transacción completada exitosamente
[2025-06-11 17:59:08][INFO] Conexión cerrada
[2025-06-11 18:03:28][INFO] Nueva solicitud recibida
[2025-06-11 18:03:28][INFO] Método HTTP: POST
[2025-06-11 18:03:28][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"162","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 18:03:28][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":156,"cantidad":1,"nombre":"Kit Accesorio Completo"}]}
[2025-06-11 18:03:28][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":156,"cantidad":1,"nombre":"Kit Accesorio Completo"}]}
[2025-06-11 18:03:28][INFO] Conexión a base de datos establecida
[2025-06-11 18:03:28][INFO] Iniciando transacción
[2025-06-11 18:03:28][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 18:03:28][INFO] Procesando repuesto: {"repuesto_id":156,"cantidad":1,"nombre":"Kit Accesorio Completo"}
[2025-06-11 18:03:28][INFO] Movimiento insertado correctamente para repuesto ID: 156
[2025-06-11 18:03:28][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 156
[2025-06-11 18:03:28][INFO] Transacción completada exitosamente
[2025-06-11 18:03:28][INFO] Conexión cerrada
[2025-06-11 18:07:58][INFO] Nueva solicitud recibida
[2025-06-11 18:07:58][INFO] Método HTTP: POST
[2025-06-11 18:07:58][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"161","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 18:07:58][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":157,"cantidad":1,"nombre":"Tensor Correa Alt A/C"}]}
[2025-06-11 18:07:58][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":157,"cantidad":1,"nombre":"Tensor Correa Alt A\/C"}]}
[2025-06-11 18:07:58][INFO] Conexión a base de datos establecida
[2025-06-11 18:07:58][INFO] Iniciando transacción
[2025-06-11 18:07:58][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 18:07:58][INFO] Procesando repuesto: {"repuesto_id":157,"cantidad":1,"nombre":"Tensor Correa Alt A\/C"}
[2025-06-11 18:07:58][INFO] Movimiento insertado correctamente para repuesto ID: 157
[2025-06-11 18:07:58][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 157
[2025-06-11 18:07:58][INFO] Transacción completada exitosamente
[2025-06-11 18:07:58][INFO] Conexión cerrada
[2025-06-11 18:20:39][INFO] Nueva solicitud recibida
[2025-06-11 18:20:39][INFO] Método HTTP: POST
[2025-06-11 18:20:39][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 18:20:39][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":158,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-11 18:20:39][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":158,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-11 18:20:39][INFO] Conexión a base de datos establecida
[2025-06-11 18:20:39][INFO] Iniciando transacción
[2025-06-11 18:20:39][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 18:20:39][INFO] Procesando repuesto: {"repuesto_id":158,"cantidad":3,"nombre":"Tensor Correa Accesorio"}
[2025-06-11 18:20:39][INFO] Movimiento insertado correctamente para repuesto ID: 158
[2025-06-11 18:20:39][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 158
[2025-06-11 18:20:39][INFO] Transacción completada exitosamente
[2025-06-11 18:20:39][INFO] Conexión cerrada
[2025-06-11 18:21:21][INFO] Nueva solicitud recibida
[2025-06-11 18:21:21][INFO] Método HTTP: POST
[2025-06-11 18:21:21][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"156","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 18:21:21][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":154,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 18:21:21][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":154,"cantidad":1,"nombre":"Kit Distribucion"}]}
[2025-06-11 18:21:21][INFO] Conexión a base de datos establecida
[2025-06-11 18:21:21][INFO] Iniciando transacción
[2025-06-11 18:21:21][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 18:21:21][INFO] Procesando repuesto: {"repuesto_id":154,"cantidad":1,"nombre":"Kit Distribucion"}
[2025-06-11 18:21:21][INFO] Movimiento insertado correctamente para repuesto ID: 154
[2025-06-11 18:21:21][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 154
[2025-06-11 18:21:21][INFO] Transacción completada exitosamente
[2025-06-11 18:21:21][INFO] Conexión cerrada
[2025-06-11 18:30:12][INFO] Nueva solicitud recibida
[2025-06-11 18:30:12][INFO] Método HTTP: POST
[2025-06-11 18:30:12][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"166","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 18:30:12][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":140,"cantidad":-1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-11 18:30:12][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":140,"cantidad":-1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-11 18:30:12][INFO] Conexión a base de datos establecida
[2025-06-11 18:30:12][INFO] Iniciando transacción
[2025-06-11 18:30:12][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 18:30:12][INFO] Procesando repuesto: {"repuesto_id":140,"cantidad":-1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-11 18:30:12][INFO] Movimiento insertado correctamente para repuesto ID: 140
[2025-06-11 18:30:12][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 140
[2025-06-11 18:30:12][INFO] Transacción completada exitosamente
[2025-06-11 18:30:12][INFO] Conexión cerrada
[2025-06-11 19:23:31][INFO] Nueva solicitud recibida
[2025-06-11 19:23:31][INFO] Método HTTP: POST
[2025-06-11 19:23:31][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 19:23:31][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":159,"cantidad":2,"nombre":"Tensor Correas Alternador"}]}
[2025-06-11 19:23:31][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":159,"cantidad":2,"nombre":"Tensor Correas Alternador"}]}
[2025-06-11 19:23:31][INFO] Conexión a base de datos establecida
[2025-06-11 19:23:31][INFO] Iniciando transacción
[2025-06-11 19:23:31][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 19:23:31][INFO] Procesando repuesto: {"repuesto_id":159,"cantidad":2,"nombre":"Tensor Correas Alternador"}
[2025-06-11 19:23:31][INFO] Movimiento insertado correctamente para repuesto ID: 159
[2025-06-11 19:23:31][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 159
[2025-06-11 19:23:31][INFO] Transacción completada exitosamente
[2025-06-11 19:23:31][INFO] Conexión cerrada
[2025-06-11 20:25:16][INFO] Nueva solicitud recibida
[2025-06-11 20:25:16][INFO] Método HTTP: POST
[2025-06-11 20:25:16][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"164","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 20:25:16][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":160,"cantidad":1,"nombre":"Tensor Correa Alternador"}]}
[2025-06-11 20:25:16][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":160,"cantidad":1,"nombre":"Tensor Correa Alternador"}]}
[2025-06-11 20:25:16][INFO] Conexión a base de datos establecida
[2025-06-11 20:25:16][INFO] Iniciando transacción
[2025-06-11 20:25:16][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 20:25:16][INFO] Procesando repuesto: {"repuesto_id":160,"cantidad":1,"nombre":"Tensor Correa Alternador"}
[2025-06-11 20:25:16][INFO] Movimiento insertado correctamente para repuesto ID: 160
[2025-06-11 20:25:16][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 160
[2025-06-11 20:25:16][INFO] Transacción completada exitosamente
[2025-06-11 20:25:16][INFO] Conexión cerrada
[2025-06-11 20:31:10][INFO] Nueva solicitud recibida
[2025-06-11 20:31:10][INFO] Método HTTP: POST
[2025-06-11 20:31:10][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=pv9bnfvahb49h6hs9p9vmaqv8k"}
[2025-06-11 20:31:10][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":161,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-11 20:31:10][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":161,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-11 20:31:10][INFO] Conexión a base de datos establecida
[2025-06-11 20:31:10][INFO] Iniciando transacción
[2025-06-11 20:31:10][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-11 20:31:10][INFO] Procesando repuesto: {"repuesto_id":161,"cantidad":1,"nombre":"Tensor Correas Alternador"}
[2025-06-11 20:31:10][INFO] Movimiento insertado correctamente para repuesto ID: 161
[2025-06-11 20:31:10][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 161
[2025-06-11 20:31:10][INFO] Transacción completada exitosamente
[2025-06-11 20:31:10][INFO] Conexión cerrada
[2025-06-12 15:00:37][INFO] Nueva solicitud recibida
[2025-06-12 15:00:37][INFO] Método HTTP: POST
[2025-06-12 15:00:37][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 15:00:37][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":162,"cantidad":1,"nombre":"Tensor Correa Alternador Int"}]}
[2025-06-12 15:00:37][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":162,"cantidad":1,"nombre":"Tensor Correa Alternador Int"}]}
[2025-06-12 15:00:37][INFO] Conexión a base de datos establecida
[2025-06-12 15:00:37][INFO] Iniciando transacción
[2025-06-12 15:00:37][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 15:00:37][INFO] Procesando repuesto: {"repuesto_id":162,"cantidad":1,"nombre":"Tensor Correa Alternador Int"}
[2025-06-12 15:00:37][INFO] Movimiento insertado correctamente para repuesto ID: 162
[2025-06-12 15:00:37][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 162
[2025-06-12 15:00:37][INFO] Transacción completada exitosamente
[2025-06-12 15:00:37][INFO] Conexión cerrada
[2025-06-12 15:17:42][INFO] Nueva solicitud recibida
[2025-06-12 15:17:42][INFO] Método HTTP: POST
[2025-06-12 15:17:42][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"177","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 15:17:42][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":163,"cantidad":1,"nombre":"Tensor Correas Alternador con Soporte"}]}
[2025-06-12 15:17:42][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":163,"cantidad":1,"nombre":"Tensor Correas Alternador con Soporte"}]}
[2025-06-12 15:17:42][INFO] Conexión a base de datos establecida
[2025-06-12 15:17:42][INFO] Iniciando transacción
[2025-06-12 15:17:42][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 15:17:42][INFO] Procesando repuesto: {"repuesto_id":163,"cantidad":1,"nombre":"Tensor Correas Alternador con Soporte"}
[2025-06-12 15:17:42][INFO] Movimiento insertado correctamente para repuesto ID: 163
[2025-06-12 15:17:42][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 163
[2025-06-12 15:17:42][INFO] Transacción completada exitosamente
[2025-06-12 15:17:42][INFO] Conexión cerrada
[2025-06-12 16:43:31][INFO] Nueva solicitud recibida
[2025-06-12 16:43:31][INFO] Método HTTP: POST
[2025-06-12 16:43:31][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"161","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 16:43:31][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-12 16:43:31][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-12 16:43:31][INFO] Conexión a base de datos establecida
[2025-06-12 16:43:31][INFO] Iniciando transacción
[2025-06-12 16:43:31][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 16:43:31][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}
[2025-06-12 16:43:31][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-12 16:43:31][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-12 16:43:31][INFO] Transacción completada exitosamente
[2025-06-12 16:43:31][INFO] Conexión cerrada
[2025-06-12 16:47:05][INFO] Nueva solicitud recibida
[2025-06-12 16:47:05][INFO] Método HTTP: POST
[2025-06-12 16:47:05][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 16:47:05][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":164,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 16:47:05][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":164,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 16:47:05][INFO] Conexión a base de datos establecida
[2025-06-12 16:47:05][INFO] Iniciando transacción
[2025-06-12 16:47:05][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 16:47:05][INFO] Procesando repuesto: {"repuesto_id":164,"cantidad":1,"nombre":"Tensor Correas Alternador"}
[2025-06-12 16:47:05][INFO] Movimiento insertado correctamente para repuesto ID: 164
[2025-06-12 16:47:05][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 164
[2025-06-12 16:47:05][INFO] Transacción completada exitosamente
[2025-06-12 16:47:05][INFO] Conexión cerrada
[2025-06-12 16:50:20][INFO] Nueva solicitud recibida
[2025-06-12 16:50:20][INFO] Método HTTP: POST
[2025-06-12 16:50:20][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"170","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 16:50:20][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":165,"cantidad":1,"nombre":"Tensor Correas Alternador Solo"}]}
[2025-06-12 16:50:20][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":165,"cantidad":1,"nombre":"Tensor Correas Alternador Solo"}]}
[2025-06-12 16:50:20][INFO] Conexión a base de datos establecida
[2025-06-12 16:50:20][INFO] Iniciando transacción
[2025-06-12 16:50:20][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 16:50:20][INFO] Procesando repuesto: {"repuesto_id":165,"cantidad":1,"nombre":"Tensor Correas Alternador Solo"}
[2025-06-12 16:50:20][INFO] Movimiento insertado correctamente para repuesto ID: 165
[2025-06-12 16:50:20][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 165
[2025-06-12 16:50:20][INFO] Transacción completada exitosamente
[2025-06-12 16:50:20][INFO] Conexión cerrada
[2025-06-12 17:09:48][INFO] Nueva solicitud recibida
[2025-06-12 17:09:48][INFO] Método HTTP: POST
[2025-06-12 17:09:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 17:09:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":166,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 17:09:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":166,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 17:09:48][INFO] Conexión a base de datos establecida
[2025-06-12 17:09:48][INFO] Iniciando transacción
[2025-06-12 17:09:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 17:09:48][INFO] Procesando repuesto: {"repuesto_id":166,"cantidad":1,"nombre":"Tensor Correas Alternador"}
[2025-06-12 17:09:48][INFO] Movimiento insertado correctamente para repuesto ID: 166
[2025-06-12 17:09:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 166
[2025-06-12 17:09:48][INFO] Transacción completada exitosamente
[2025-06-12 17:09:48][INFO] Conexión cerrada
[2025-06-12 17:15:39][INFO] Nueva solicitud recibida
[2025-06-12 17:15:39][INFO] Método HTTP: POST
[2025-06-12 17:15:39][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 17:15:39][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":167,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-12 17:15:39][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":167,"cantidad":3,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-12 17:15:39][INFO] Conexión a base de datos establecida
[2025-06-12 17:15:39][INFO] Iniciando transacción
[2025-06-12 17:15:39][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 17:15:39][INFO] Procesando repuesto: {"repuesto_id":167,"cantidad":3,"nombre":"Tensor Correa Accesorio"}
[2025-06-12 17:15:39][INFO] Movimiento insertado correctamente para repuesto ID: 167
[2025-06-12 17:15:39][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 167
[2025-06-12 17:15:39][INFO] Transacción completada exitosamente
[2025-06-12 17:15:39][INFO] Conexión cerrada
[2025-06-12 18:31:28][INFO] Nueva solicitud recibida
[2025-06-12 18:31:28][INFO] Método HTTP: POST
[2025-06-12 18:31:28][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 18:31:28][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 18:31:28][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 18:31:28][INFO] Conexión a base de datos establecida
[2025-06-12 18:31:28][INFO] Iniciando transacción
[2025-06-12 18:31:28][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 18:31:28][INFO] Procesando repuesto: {"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}
[2025-06-12 18:31:28][INFO] Movimiento insertado correctamente para repuesto ID: 168
[2025-06-12 18:31:28][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 168
[2025-06-12 18:31:28][INFO] Transacción completada exitosamente
[2025-06-12 18:31:28][INFO] Conexión cerrada
[2025-06-12 18:32:01][INFO] Nueva solicitud recibida
[2025-06-12 18:32:01][INFO] Método HTTP: POST
[2025-06-12 18:32:01][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"165","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 18:32:01][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 18:32:01][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}]}
[2025-06-12 18:32:01][INFO] Conexión a base de datos establecida
[2025-06-12 18:32:01][INFO] Iniciando transacción
[2025-06-12 18:32:01][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 18:32:01][INFO] Procesando repuesto: {"repuesto_id":168,"cantidad":1,"nombre":"Tensor Correas Alternador"}
[2025-06-12 18:32:01][INFO] Movimiento insertado correctamente para repuesto ID: 168
[2025-06-12 18:32:01][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 168
[2025-06-12 18:32:01][INFO] Transacción completada exitosamente
[2025-06-12 18:32:01][INFO] Conexión cerrada
[2025-06-12 18:36:06][INFO] Nueva solicitud recibida
[2025-06-12 18:36:06][INFO] Método HTTP: POST
[2025-06-12 18:36:06][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"169","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 18:36:06][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":169,"cantidad":1,"nombre":"Tensor Correa Alternador Solo"}]}
[2025-06-12 18:36:06][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":169,"cantidad":1,"nombre":"Tensor Correa Alternador Solo"}]}
[2025-06-12 18:36:06][INFO] Conexión a base de datos establecida
[2025-06-12 18:36:06][INFO] Iniciando transacción
[2025-06-12 18:36:06][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 18:36:06][INFO] Procesando repuesto: {"repuesto_id":169,"cantidad":1,"nombre":"Tensor Correa Alternador Solo"}
[2025-06-12 18:36:06][INFO] Movimiento insertado correctamente para repuesto ID: 169
[2025-06-12 18:36:06][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 169
[2025-06-12 18:36:06][INFO] Transacción completada exitosamente
[2025-06-12 18:36:06][INFO] Conexión cerrada
[2025-06-12 18:48:50][INFO] Nueva solicitud recibida
[2025-06-12 18:48:50][INFO] Método HTTP: POST
[2025-06-12 18:48:50][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"140","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 18:48:50][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":170,"cantidad":2,"nombre":""}]}
[2025-06-12 18:48:50][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":170,"cantidad":2,"nombre":""}]}
[2025-06-12 18:48:50][INFO] Conexión a base de datos establecida
[2025-06-12 18:48:50][INFO] Iniciando transacción
[2025-06-12 18:48:50][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 18:48:50][INFO] Procesando repuesto: {"repuesto_id":170,"cantidad":2,"nombre":""}
[2025-06-12 18:48:50][INFO] Movimiento insertado correctamente para repuesto ID: 170
[2025-06-12 18:48:50][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 170
[2025-06-12 18:48:50][INFO] Transacción completada exitosamente
[2025-06-12 18:48:50][INFO] Conexión cerrada
[2025-06-12 18:52:48][INFO] Nueva solicitud recibida
[2025-06-12 18:52:48][INFO] Método HTTP: POST
[2025-06-12 18:52:48][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=ivnfvqjoe9et7mda94etcm7sqi"}
[2025-06-12 18:52:48][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":171,"cantidad":1,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-12 18:52:48][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":171,"cantidad":1,"nombre":"Tensor Correa Accesorio"}]}
[2025-06-12 18:52:48][INFO] Conexión a base de datos establecida
[2025-06-12 18:52:48][INFO] Iniciando transacción
[2025-06-12 18:52:48][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-12 18:52:48][INFO] Procesando repuesto: {"repuesto_id":171,"cantidad":1,"nombre":"Tensor Correa Accesorio"}
[2025-06-12 18:52:48][INFO] Movimiento insertado correctamente para repuesto ID: 171
[2025-06-12 18:52:48][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 171
[2025-06-12 18:52:48][INFO] Transacción completada exitosamente
[2025-06-12 18:52:48][INFO] Conexión cerrada
[2025-06-16 00:55:27][INFO] Nueva solicitud recibida
[2025-06-16 00:55:27][INFO] Método HTTP: POST
[2025-06-16 00:55:27][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Accept":"*\/*","Content-Type":"application\/json","Origin":"http:\/\/tatarepuestos.cl","User-Agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/18.5 Safari\/605.1.15","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Cache-Control":"no-cache","Content-Length":"161","Accept-Language":"es-ES,es;q=0.9","Priority":"u=3, i","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=9vjfbk8jiqu7vq0u7o8164lrgp","Connection":"keep-alive"}
[2025-06-16 00:55:27][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-16 00:55:27][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}]}
[2025-06-16 00:55:27][INFO] Conexión a base de datos establecida
[2025-06-16 00:55:27][INFO] Iniciando transacción
[2025-06-16 00:55:27][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-16 00:55:27][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":-1,"nombre":"Termostato con Oring"}
[2025-06-16 00:55:27][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-16 00:55:27][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-16 00:55:27][INFO] Transacción completada exitosamente
[2025-06-16 00:55:27][INFO] Conexión cerrada
[2025-06-18 16:21:09][INFO] Nueva solicitud recibida
[2025-06-18 16:21:09][INFO] Método HTTP: POST
[2025-06-18 16:21:09][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"160","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=h2l2dfc05b0uvrdasoae3da81k"}
[2025-06-18 16:21:09][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-18 16:21:09][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}]}
[2025-06-18 16:21:09][INFO] Conexión a base de datos establecida
[2025-06-18 16:21:09][INFO] Iniciando transacción
[2025-06-18 16:21:09][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-18 16:21:09][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":2,"nombre":"Termostato con Oring"}
[2025-06-18 16:21:09][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-18 16:21:09][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-18 16:21:09][INFO] Transacción completada exitosamente
[2025-06-18 16:21:09][INFO] Conexión cerrada
[2025-06-18 18:18:53][INFO] Nueva solicitud recibida
[2025-06-18 18:18:53][INFO] Método HTTP: POST
[2025-06-18 18:18:53][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"166","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=h2l2dfc05b0uvrdasoae3da81k"}
[2025-06-18 18:18:53][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":142,"cantidad":-1,"nombre":"Kit Distribucion c/ Bomba"}]}
[2025-06-18 18:18:53][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":142,"cantidad":-1,"nombre":"Kit Distribucion c\/ Bomba"}]}
[2025-06-18 18:18:53][INFO] Conexión a base de datos establecida
[2025-06-18 18:18:53][INFO] Iniciando transacción
[2025-06-18 18:18:53][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-18 18:18:53][INFO] Procesando repuesto: {"repuesto_id":142,"cantidad":-1,"nombre":"Kit Distribucion c\/ Bomba"}
[2025-06-18 18:18:53][INFO] Movimiento insertado correctamente para repuesto ID: 142
[2025-06-18 18:18:53][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 142
[2025-06-18 18:18:53][INFO] Transacción completada exitosamente
[2025-06-18 18:18:53][INFO] Conexión cerrada
[2025-06-18 18:19:13][INFO] Nueva solicitud recibida
[2025-06-18 18:19:13][INFO] Método HTTP: POST
[2025-06-18 18:19:13][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"157","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=h2l2dfc05b0uvrdasoae3da81k"}
[2025-06-18 18:19:13][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":146,"cantidad":-1,"nombre":"Kit Distribucion"}]}
[2025-06-18 18:19:13][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":146,"cantidad":-1,"nombre":"Kit Distribucion"}]}
[2025-06-18 18:19:13][INFO] Conexión a base de datos establecida
[2025-06-18 18:19:13][INFO] Iniciando transacción
[2025-06-18 18:19:13][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-18 18:19:13][INFO] Procesando repuesto: {"repuesto_id":146,"cantidad":-1,"nombre":"Kit Distribucion"}
[2025-06-18 18:19:13][INFO] Movimiento insertado correctamente para repuesto ID: 146
[2025-06-18 18:19:13][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 146
[2025-06-18 18:19:13][INFO] Transacción completada exitosamente
[2025-06-18 18:19:13][INFO] Conexión cerrada
[2025-06-18 19:53:46][INFO] Nueva solicitud recibida
[2025-06-18 19:53:46][INFO] Método HTTP: POST
[2025-06-18 19:53:46][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=h2l2dfc05b0uvrdasoae3da81k"}
[2025-06-18 19:53:46][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":131,"cantidad":-1,"nombre":"Caja Salida Agua con Sensor"}]}
[2025-06-18 19:53:46][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":131,"cantidad":-1,"nombre":"Caja Salida Agua con Sensor"}]}
[2025-06-18 19:53:46][INFO] Conexión a base de datos establecida
[2025-06-18 19:53:46][INFO] Iniciando transacción
[2025-06-18 19:53:46][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-18 19:53:46][INFO] Procesando repuesto: {"repuesto_id":131,"cantidad":-1,"nombre":"Caja Salida Agua con Sensor"}
[2025-06-18 19:53:46][INFO] Movimiento insertado correctamente para repuesto ID: 131
[2025-06-18 19:53:46][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 131
[2025-06-18 19:53:46][INFO] Transacción completada exitosamente
[2025-06-18 19:53:46][INFO] Conexión cerrada
[2025-06-21 17:18:26][INFO] Nueva solicitud recibida
[2025-06-21 17:18:26][INFO] Método HTTP: POST
[2025-06-21 17:18:26][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=h2l2dfc05b0uvrdasoae3da81k"}
[2025-06-21 17:18:26][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":172,"cantidad":2,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-06-21 17:18:26][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":172,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-06-21 17:18:26][INFO] Conexión a base de datos establecida
[2025-06-21 17:18:26][INFO] Iniciando transacción
[2025-06-21 17:18:26][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-21 17:18:26][INFO] Procesando repuesto: {"repuesto_id":172,"cantidad":2,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-06-21 17:18:26][INFO] Movimiento insertado correctamente para repuesto ID: 172
[2025-06-21 17:18:26][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 172
[2025-06-21 17:18:26][INFO] Transacción completada exitosamente
[2025-06-21 17:18:26][INFO] Conexión cerrada
[2025-06-23 20:34:32][INFO] Nueva solicitud recibida
[2025-06-23 20:34:32][INFO] Método HTTP: POST
[2025-06-23 20:34:32][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"163","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=9pen7447ooe957a9c6ggptfvbb"}
[2025-06-23 20:34:32][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":117,"cantidad":-1,"nombre":"Termostato con Carcasa"}]}
[2025-06-23 20:34:32][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":117,"cantidad":-1,"nombre":"Termostato con Carcasa"}]}
[2025-06-23 20:34:32][INFO] Conexión a base de datos establecida
[2025-06-23 20:34:32][INFO] Iniciando transacción
[2025-06-23 20:34:32][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-23 20:34:32][INFO] Procesando repuesto: {"repuesto_id":117,"cantidad":-1,"nombre":"Termostato con Carcasa"}
[2025-06-23 20:34:32][INFO] Movimiento insertado correctamente para repuesto ID: 117
[2025-06-23 20:34:32][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 117
[2025-06-23 20:34:32][INFO] Transacción completada exitosamente
[2025-06-23 20:34:32][INFO] Conexión cerrada
[2025-06-24 02:28:27][INFO] Nueva solicitud recibida
[2025-06-24 02:28:27][INFO] Método HTTP: POST
[2025-06-24 02:28:27][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Accept":"*\/*","Content-Type":"application\/json","Origin":"http:\/\/tatarepuestos.cl","User-Agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/18.5 Safari\/605.1.15","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Cache-Control":"no-cache","Content-Length":"169","Accept-Language":"es-ES,es;q=0.9","Priority":"u=3, i","Accept-Encoding":"gzip, deflate","Cookie":"PHPSESSID=90jgaotortvq52ha5g8jko33lu","Connection":"keep-alive"}
[2025-06-24 02:28:27][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":172,"cantidad":-2,"nombre":"Cilindro Freno Trasero LH/RH"}]}
[2025-06-24 02:28:27][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":172,"cantidad":-2,"nombre":"Cilindro Freno Trasero LH\/RH"}]}
[2025-06-24 02:28:27][INFO] Conexión a base de datos establecida
[2025-06-24 02:28:27][INFO] Iniciando transacción
[2025-06-24 02:28:27][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-24 02:28:27][INFO] Procesando repuesto: {"repuesto_id":172,"cantidad":-2,"nombre":"Cilindro Freno Trasero LH\/RH"}
[2025-06-24 02:28:27][INFO] Movimiento insertado correctamente para repuesto ID: 172
[2025-06-24 02:28:27][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 172
[2025-06-24 02:28:27][INFO] Transacción completada exitosamente
[2025-06-24 02:28:27][INFO] Conexión cerrada
[2025-06-26 19:24:59][INFO] Nueva solicitud recibida
[2025-06-26 19:24:59][INFO] Método HTTP: POST
[2025-06-26 19:24:59][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"161","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=skk6em654rqa4q997tminvcd6g"}
[2025-06-26 19:24:59][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-2,"nombre":"Termostato con Oring"}]}
[2025-06-26 19:24:59][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":118,"cantidad":-2,"nombre":"Termostato con Oring"}]}
[2025-06-26 19:24:59][INFO] Conexión a base de datos establecida
[2025-06-26 19:24:59][INFO] Iniciando transacción
[2025-06-26 19:24:59][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-26 19:24:59][INFO] Procesando repuesto: {"repuesto_id":118,"cantidad":-2,"nombre":"Termostato con Oring"}
[2025-06-26 19:24:59][INFO] Movimiento insertado correctamente para repuesto ID: 118
[2025-06-26 19:24:59][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 118
[2025-06-26 19:24:59][INFO] Transacción completada exitosamente
[2025-06-26 19:24:59][INFO] Conexión cerrada
[2025-06-26 19:25:13][INFO] Nueva solicitud recibida
[2025-06-26 19:25:13][INFO] Método HTTP: POST
[2025-06-26 19:25:13][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=skk6em654rqa4q997tminvcd6g"}
[2025-06-26 19:25:13][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-06-26 19:25:13][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-06-26 19:25:13][INFO] Conexión a base de datos establecida
[2025-06-26 19:25:13][INFO] Iniciando transacción
[2025-06-26 19:25:13][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-26 19:25:13][INFO] Procesando repuesto: {"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}
[2025-06-26 19:25:13][INFO] Movimiento insertado correctamente para repuesto ID: 96
[2025-06-26 19:25:13][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 96
[2025-06-26 19:25:13][INFO] Transacción completada exitosamente
[2025-06-26 19:25:13][INFO] Conexión cerrada
[2025-06-26 19:25:26][INFO] Nueva solicitud recibida
[2025-06-26 19:25:26][INFO] Método HTTP: POST
[2025-06-26 19:25:26][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=skk6em654rqa4q997tminvcd6g"}
[2025-06-26 19:25:26][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":97,"cantidad":-1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-06-26 19:25:26][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":97,"cantidad":-1,"nombre":"Cilindro de Freno Trasero RH"}]}
[2025-06-26 19:25:26][INFO] Conexión a base de datos establecida
[2025-06-26 19:25:26][INFO] Iniciando transacción
[2025-06-26 19:25:26][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-06-26 19:25:26][INFO] Procesando repuesto: {"repuesto_id":97,"cantidad":-1,"nombre":"Cilindro de Freno Trasero RH"}
[2025-06-26 19:25:26][INFO] Movimiento insertado correctamente para repuesto ID: 97
[2025-06-26 19:25:26][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 97
[2025-06-26 19:25:26][INFO] Transacción completada exitosamente
[2025-06-26 19:25:26][INFO] Conexión cerrada
[2025-07-05 15:41:19][INFO] Nueva solicitud recibida
[2025-07-05 15:41:19][INFO] Método HTTP: POST
[2025-07-05 15:41:19][INFO] Headers recibidos: {"Host":"tatarepuestos.cl","Connection":"keep-alive","Content-Length":"168","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Content-Type":"application\/json","Cache-Control":"no-cache","Accept":"*\/*","Origin":"http:\/\/tatarepuestos.cl","Referer":"http:\/\/tatarepuestos.cl\/inventory.php","Accept-Encoding":"gzip, deflate","Accept-Language":"en-US,en;q=0.9","Cookie":"PHPSESSID=4ars5hrvpebpksdi9clfj7eq68"}
[2025-07-05 15:41:19][INFO] Datos recibidos: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-07-05 15:41:19][INFO] Datos decodificados: {"almacen_id":1,"tipo_movimiento":"entrada","referencia_documento":"","notas":"","repuestos":[{"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}]}
[2025-07-05 15:41:19][INFO] Conexión a base de datos establecida
[2025-07-05 15:41:19][INFO] Iniciando transacción
[2025-07-05 15:41:19][INFO] Query preparada: INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)
[2025-07-05 15:41:19][INFO] Procesando repuesto: {"repuesto_id":96,"cantidad":-1,"nombre":"Cilindro de Freno Trasero LH"}
[2025-07-05 15:41:19][INFO] Movimiento insertado correctamente para repuesto ID: 96
[2025-07-05 15:41:19][INFO] El trigger se encargará de actualizar el stock para repuesto ID: 96
[2025-07-05 15:41:19][INFO] Transacción completada exitosamente
[2025-07-05 15:41:19][INFO] Conexión cerrada
