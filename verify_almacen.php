<?php
// verify_almacen.php
// Script para verificar que la tabla almacen existe y funciona correctamente

require_once 'db_connection.php';

header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Verificar si la tabla almacen existe
    $stmt = $conn->query("SHOW TABLES LIKE 'almacen'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo json_encode([
            'status' => 'error',
            'message' => 'La tabla almacen no existe'
        ]);
        exit;
    }
    
    // Verificar si hay registros en la tabla
    $stmt = $conn->query("SELECT COUNT(*) as total FROM almacen");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($count['total'] == 0) {
        // Insertar un almacén por defecto
        $stmt = $conn->prepare("INSERT INTO almacen (nombre, activo) VALUES ('TEMUCO', 1)");
        $stmt->execute();
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Se ha creado un almacén por defecto',
            'almacen_id' => $conn->lastInsertId(),
            'almacen_nombre' => 'TEMUCO'
        ]);
        exit;
    }
    
    // Obtener los almacenes
    $stmt = $conn->query("SELECT id, nombre FROM almacen WHERE activo = 1 ORDER BY nombre");
    $almacenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'La tabla almacen existe y contiene registros',
        'almacenes' => $almacenes
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
