<?php
// Script de diagnóstico para verificar directorios y archivos XML
header('Content-Type: application/json');

// Incluir la conexión a la base de datos
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Directorio donde deberían estar los XML
    $baseDir = 'Documents';
    $xmlDir = 'Documents/xml';
    $sobreDir = 'Documents/sobreEnvio';
    
    // Comprobar directorios principales
    $dirDiagnostico = [];
    $directorios = [$baseDir, $xmlDir, $sobreDir];
    
    foreach ($directorios as $dir) {
        $exists = file_exists($dir);
        $dirDiagnostico[$dir] = [
            'existe' => $exists ? 'Sí' : 'No',
            'ruta_absoluta' => $exists ? realpath($dir) : 'N/A',
            'permisos' => $exists ? decoct(fileperms($dir)) : 'N/A',
            'es_directorio' => $exists && is_dir($dir) ? 'Sí' : 'No',
            'es_legible' => $exists && is_readable($dir) ? 'Sí' : 'No',
            'es_escribible' => $exists && is_writable($dir) ? 'Sí' : 'No'
        ];
        
        // Si el directorio existe, intentar listar archivos
        if ($exists && is_readable($dir)) {
            $files = scandir($dir);
            $dirDiagnostico[$dir]['archivos'] = array_diff($files, ['.', '..']);
            $dirDiagnostico[$dir]['total_archivos'] = count($dirDiagnostico[$dir]['archivos']);
        }
    }
    
    // Obtener los últimos 10 documentos pendientes de la base de datos
    $stmt = $conn->prepare("SELECT id, nombre_archivo, tipo_dte, estado_sobre FROM tb_facturas_dte WHERE estado_sobre = 0 ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $documentosPendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Verificar cada archivo XML de los documentos pendientes
    $xmlDiagnostico = [];
    foreach ($documentosPendientes as $doc) {
        $xmlPath = $doc['nombre_archivo'];
        $exists = file_exists($xmlPath);
        
        $xmlDiagnostico[] = [
            'id' => $doc['id'],
            'tipo_dte' => $doc['tipo_dte'],
            'archivo' => $xmlPath,
            'existe' => $exists ? 'Sí' : 'No',
            'ruta_absoluta' => $exists ? realpath($xmlPath) : 'N/A',
            'tamaño' => $exists ? filesize($xmlPath) . ' bytes' : 'N/A',
            'fecha_modificacion' => $exists ? date('Y-m-d H:i:s', filemtime($xmlPath)) : 'N/A',
            'directorio_padre_existe' => file_exists(dirname($xmlPath)) ? 'Sí' : 'No',
            'directorio_padre' => dirname($xmlPath),
            'es_legible' => $exists && is_readable($xmlPath) ? 'Sí' : 'No',
            'es_xml_valido' => false
        ];
        
        // Intentar validar el XML si existe y es legible
        if ($exists && is_readable($xmlPath)) {
            try {
                $xml = new SimpleXMLElement(file_get_contents($xmlPath));
                $xmlDiagnostico[count($xmlDiagnostico) - 1]['es_xml_valido'] = true;
                $xmlDiagnostico[count($xmlDiagnostico) - 1]['xml_root'] = $xml->getName();
            } catch (Exception $xmlEx) {
                $xmlDiagnostico[count($xmlDiagnostico) - 1]['error_xml'] = $xmlEx->getMessage();
            }
        }
    }
    
    // Obtener información del sistema para diagnostico
    $systemInfo = [
        'php_version' => PHP_VERSION,
        'sistema_operativo' => PHP_OS,
        'servidor_web' => $_SERVER['SERVER_SOFTWARE'] ?? 'No disponible',
        'directorio_actual' => getcwd(),
        'usuario_php' => function_exists('posix_getpwuid') ? (posix_getpwuid(posix_geteuid())['name'] ?? 'desconocido') : 'función posix no disponible',
        'memoria_limite' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time') . ' segundos',
        'post_max_size' => ini_get('post_max_size'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'display_errors' => ini_get('display_errors'),
        'fecha_hora' => date('Y-m-d H:i:s'),
        'zona_horaria' => date_default_timezone_get()
    ];
    
    // Comprobar si hay sobres pendientes
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM tb_sobre_envios WHERE estado_envio = 0");
    $stmt->execute();
    $sobresPendientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Retornar toda la información de diagnóstico
    echo json_encode([
        'success' => true,
        'directorios' => $dirDiagnostico,
        'xml_documentos' => $xmlDiagnostico,
        'system_info' => $systemInfo,
        'documentos_pendientes_total' => count($documentosPendientes),
        'sobres_pendientes' => $sobresPendientes,
        'tiempo_consulta' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Capturar cualquier error y devolverlo como respuesta
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'error_code' => $e->getCode(),
        'error_file' => $e->getFile(),
        'error_line' => $e->getLine(),
        'trace' => explode("\n", $e->getTraceAsString())
    ]);
}
