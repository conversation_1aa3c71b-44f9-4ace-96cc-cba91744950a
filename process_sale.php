
<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    // Obtener el cuerpo de la petición JSON
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        throw new Exception('Datos de venta inválidos');
    }

    $conn = getConnection();
    $conn->beginTransaction();

    // Insertar la venta principal
    $sql = "INSERT INTO venta (
        tipo_documento, 
        numero_documento,
        condiciones_pago,
        total,
        cliente_id,
        fecha_compromiso,
        fecha_emision,
        estado,
        abono,
        created_at,
        updated_at
    ) VALUES (
        :tipo_documento,
        :numero_documento,
        :condiciones_pago,
        :total,
        :cliente_id,
        :fecha_compromiso,
        NOW(),
        CASE 
            WHEN :fecha_compromiso IS NOT NULL AND :fecha_compromiso != '' 
            THEN 'PENDIENTE' 
            ELSE 'COMPLETADA' 
        END,
        :abono,
        NOW(),
        NOW()
    )";

    $stmt = $conn->prepare($sql);
    $stmt->execute([
        ':tipo_documento' => $data['tipo_documento'],
        ':numero_documento' => $data['numero_documento'],
        ':condiciones_pago' => $data['condiciones_pago'],
        ':total' => $data['total'],
        ':cliente_id' => $data['cliente_id'],
        ':fecha_compromiso' => $data['fecha_compromiso'],
        ':abono' => $data['abono']
    ]);

    $ventaId = $conn->lastInsertId();

    // Insertar los detalles de la venta
    $sqlDetalle = "INSERT INTO detalle_venta (
        venta_id,
        repuesto_id,
        cantidad,
        precio_unitario,
        subtotal,
        descuento
    ) VALUES (
        :venta_id,
        :repuesto_id,
        :cantidad,
        :precio_unitario,
        :subtotal,
        :descuento
    )";

    $stmtDetalle = $conn->prepare($sqlDetalle);

    foreach ($data['detalles'] as $detalle) {
        $stmtDetalle->execute([
            ':venta_id' => $ventaId,
            ':repuesto_id' => $detalle['repuesto_id'],
            ':cantidad' => $detalle['cantidad'],
            ':precio_unitario' => $detalle['precio_unitario'],
            ':subtotal' => $detalle['subtotal'],
            ':descuento' => $detalle['descuento']
        ]);
    }

    $conn->commit();

    echo json_encode([
        'status' => 'success',
        'message' => 'Venta procesada exitosamente',
        'venta_id' => $ventaId
    ]);

} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }

    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
