/**
 * Monitor de sesión para mantener la sesión activa y comprobar su estado
 * Este script debe incluirse en todas las páginas que requieran autenticación
 */

// Importar helper para peticiones fetch con credentials
// Asegúrate de que fetch-helper.js se carga antes que este script
if (typeof fetchWithCredentials !== 'function') {
    console.error('Error: fetch-helper.js debe cargarse antes que session-monitor.js');
}

class SessionMonitor {
    constructor(options = {}) {
        // Configuración por defecto
        this.config = {
            checkInterval: options.checkInterval || 5 * 60 * 1000, // 5 minutos por defecto
            keepAliveInterval: options.keepAliveInterval || 15 * 60 * 1000, // 15 minutos por defecto
            checkEndpoint: options.checkEndpoint || 'session_check.php',
            debug: options.debug || false,
            onExpired: options.onExpired || this.defaultExpiredHandler,
            onWarning: options.onWarning || this.defaultWarningHandler,
            warningThreshold: options.warningThreshold || 60 * 1000 // 1 minuto por defecto
        };

        // Estado interno
        this.isActive = true;
        this.checkTimer = null;
        this.keepAliveTimer = null;
        
        this.log('SessionMonitor inicializado');
    }

    /**
     * Inicia el monitoreo de la sesión
     */
    start() {
        this.log('Iniciando monitoreo de sesión');
        
        // Iniciar verificación periódica
        this.checkTimer = setInterval(() => this.checkSession(), this.config.checkInterval);
        
        // Iniciar keep-alive periódico
        this.keepAliveTimer = setInterval(() => this.keepAlive(), this.config.keepAliveInterval);
        
        // Realizar una verificación inicial
        this.checkSession();
        
        // Registrar eventos de actividad del usuario
        this.setupActivityListeners();
    }

    /**
     * Detiene el monitoreo de la sesión
     */
    stop() {
        this.log('Deteniendo monitoreo de sesión');
        
        // Limpiar timers
        if (this.checkTimer) clearInterval(this.checkTimer);
        if (this.keepAliveTimer) clearInterval(this.keepAliveTimer);
        
        // Limpiar estado
        this.isActive = false;
    }

    /**
     * Verifica si la sesión está activa
     */
    async checkSession() {
        try {
            this.log('Verificando estado de la sesión');
            
            const response = await getWithCredentials(this.config.checkEndpoint);
            
            if (!response.ok) {
                this.log('Error en la verificación de sesión:', response.status);
                this.handleSessionExpired();
                return;
            }
            
            const data = await response.json();
            this.log('Respuesta de verificación:', data);
            
            if (!data.logged_in) {
                this.handleSessionExpired();
            }
        } catch (error) {
            this.log('Error al verificar la sesión:', error);
            // No manejar como sesión expirada porque podría ser un error de red
        }
    }

    /**
     * Mantiene la sesión activa con una petición al servidor
     */
    async keepAlive() {
        try {
            this.log('Enviando petición keep-alive');
            
            const response = await getWithCredentials(this.config.checkEndpoint);
            
            if (!response.ok) {
                this.log('Error en keep-alive:', response.status);
                return;
            }
            
            const data = await response.json();
            this.log('Respuesta de keep-alive:', data);
            
            if (!data.logged_in) {
                this.handleSessionExpired();
            }
        } catch (error) {
            this.log('Error en keep-alive:', error);
            // No manejar como sesión expirada porque podría ser un error de red
        }
    }

    /**
     * Registra eventos de actividad del usuario para mantener la sesión activa
     */
    setupActivityListeners() {
        this.log('Configurando listeners de actividad del usuario');
        
        // Lista de eventos a monitorear
        const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];
        
        // Función debounced para no enviar demasiadas peticiones
        let activityTimeout = null;
        const activityHandler = () => {
            if (activityTimeout) clearTimeout(activityTimeout);
            activityTimeout = setTimeout(() => this.keepAlive(), 1000);
        };
        
        // Registrar listeners
        events.forEach(eventName => {
            document.addEventListener(eventName, activityHandler, { passive: true });
        });
    }

    /**
     * Maneja la expiración de la sesión
     */
    handleSessionExpired() {
        this.log('Sesión expirada');
        this.stop();
        this.config.onExpired();
    }

    /**
     * Manejador por defecto para sesión expirada
     */
    defaultExpiredHandler() {
        alert('Su sesión ha expirado. Por favor, inicie sesión nuevamente.');
        window.location.href = 'login.php';
    }

    /**
     * Manejador por defecto para advertencia de sesión por expirar
     */
    defaultWarningHandler(timeLeft) {
        alert(`Su sesión expirará en ${Math.floor(timeLeft/1000)} segundos. ¿Desea mantenerla activa?`);
    }

    /**
     * Función de logging
     */
    log(...args) {
        if (this.config.debug) {
            console.log('[SessionMonitor]', ...args);
        }
    }
}

// Crear instancia del monitor de sesión con configuración por defecto
const sessionMonitor = new SessionMonitor({
    debug: true // Establecer a false en producción
});

// Iniciar monitoreo cuando el documento esté listo
document.addEventListener('DOMContentLoaded', () => {
    sessionMonitor.start();
});