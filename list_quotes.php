<?php
require_once 'session_config.php';
session_start();
header('Content-Type: application/json; charset=utf-8');

date_default_timezone_set('America/Santiago');


error_log('--- DEBUG list_quotes.php ---');
error_log('Session ID: ' . session_id());
error_log('Contenido de $_SESSION: ' . print_r($_SESSION, true));
error_log('Valor de $_SESSION[\'logged_in\']: ' . (isset($_SESSION['logged_in']) ? (($_SESSION['logged_in'] === true) ? 'true' : 'false') : 'no set'));
error_log('Contenido de $_COOKIE: ' . print_r($_COOKIE, true));
error_log('-----------------------------');
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'No autorizado']);
    exit;
}

require_once 'db_connection.php';

try {
    $conn = getConnection();

    $stmt = $conn->query("SELECT id, numero, DATE_FORMAT(fecha, '%d-%m-%Y %H:%i') AS fecha, cliente_nombre, total FROM tb_cotizaciones ORDER BY fecha DESC");
    $quotes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['status' => 'success', 'quotes' => $quotes], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Error al obtener cotizaciones', 'error_details' => $e->getMessage()]);
}
?>
