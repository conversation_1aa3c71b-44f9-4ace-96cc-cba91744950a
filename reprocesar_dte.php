<?php
header('Content-Type: application/json');
require_once 'db_connection.php';

// Función helper para logging
function logDebug($message, $data = null) {
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        $logMessage .= " - Data: " . json_encode($data);
    }
    error_log($logMessage);
}

logDebug("=== Iniciando reprocesamiento de DTE ===");

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logDebug("Error: Método no permitido", $_SERVER['REQUEST_METHOD']);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener el ID del documento a reprocesar
$data = json_decode(file_get_contents('php://input'), true);
$id = $data['id'] ?? null;

logDebug("ID recibido para reprocesar", $id);

if (!$id) {
    logDebug("Error: ID no proporcionado");
    echo json_encode(['error' => 'ID no proporcionado']);
    exit;
}

try {
    logDebug("Conectando a la base de datos...");
    $conn = getConnection();

    // Obtener el JSON original y otros datos necesarios
    $stmt = $conn->prepare("SELECT json_enviado, tipo_dte FROM tb_facturas_dte WHERE id = ?");
    $stmt->execute([$id]);
    $documento = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$documento) {
        logDebug("Error: Documento no encontrado para ID", $id);
        echo json_encode(['error' => 'Documento no encontrado']);
        exit;
    }

    logDebug("Documento encontrado", ['tipo_dte' => $documento['tipo_dte']]);

    $jsonData = $documento['json_enviado'];
    $tipoDTE = $documento['tipo_dte'];

    // Verificar JSON válido
    $jsonObj = json_decode($jsonData, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        logDebug("Error: JSON inválido", ['error' => json_last_error_msg()]);
        echo json_encode(['error' => 'JSON inválido en la base de datos']);
        exit;
    }

    logDebug("JSON validado correctamente");

    // Rutas a los archivos necesarios
    $certificadoPath = 'Documents/17365958-K.pfx';
    logDebug("Verificando certificado", ['path' => $certificadoPath]);

    // Obtener la ruta del archivo de folios
    $stmt = $conn->prepare("SELECT ruta_archivo FROM folios_caf WHERE tipo_documento = ? AND activo = 1");
    $stmt->execute([$tipoDTE]);
    $folio = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$folio || empty($folio['ruta_archivo'])) {
        logDebug("Error: No se encontró archivo de folios", ['tipo_dte' => $tipoDTE]);
        echo json_encode(['error' => 'No se encontró un archivo de folios activo']);
        exit;
    }

    $foliosPath = $folio['ruta_archivo'];
    logDebug("Archivo de folios encontrado", ['path' => $foliosPath]);

    // Verificar archivos
    if (!file_exists($certificadoPath)) {
        logDebug("Error: Certificado no encontrado", ['path' => $certificadoPath]);
        echo json_encode(['error' => 'Archivo de certificado no encontrado']);
        exit;
    }

    if (!file_exists($foliosPath)) {
        logDebug("Error: Archivo de folios no encontrado", ['path' => $foliosPath]);
        echo json_encode(['error' => 'Archivo de folios no encontrado']);
        exit;
    }

    logDebug("Todos los archivos verificados correctamente");

    // Configurar cURL para SimpleAPI
    $ch = curl_init('https://api.simpleapi.cl/api/v1/dte/generar');
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;

    logDebug("Preparando solicitud a SimpleAPI");

    // Construir el cuerpo de la solicitud
    $postData = '';

    // Agregar JSON
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonData . "\r\n";

    // Agregar certificado
    $fileContents = file_get_contents($certificadoPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Agregar folios
    $fileContents = file_get_contents($foliosPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($foliosPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    $postData .= "--" . $delimiter . "--\r\n";

    logDebug("Datos preparados para envío", [
        'postDataLength' => strlen($postData),
        'boundary' => $boundary
    ]);

    // Configurar cURL
    $apiKey = '2037-N680-6391-2493-5987';
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => [
            "Authorization: $apiKey",
            "Content-Type: multipart/form-data; boundary=" . $delimiter,
            "Content-Length: " . strlen($postData)
        ]
    ]);

    // Capturar información detallada de cURL
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    logDebug("Enviando solicitud a SimpleAPI...");

    // Ejecutar la solicitud
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);

    curl_close($ch);

    if ($httpCode !== 200) {
        logDebug("Error en la respuesta de API", [
            'httpCode' => $httpCode,
            'response' => substr($response, 0, 500)
        ]);
        echo json_encode(['error' => 'Error en la API', 'codigo' => $httpCode, 'respuesta' => $response]);
        exit;
    }

    logDebug("Respuesta exitosa de SimpleAPI, procesando XML...");

    // Determinar directorio basado en el tipo de documento
    switch ($tipoDTE) {
        case '33': // Factura Electrónica (33)
        case '34': // Factura Exenta (34)
            $directorio = 'Documents/DTE/Facturas/';
            break;
        case '39': // Boleta (39)
            $directorio = 'Documents/DTE/Boletas/';
            break;
        case '61': // Nota de Crédito (61)
            $directorio = 'Documents/DTE/NotasCredito/';
            break;
        default:
            $directorio = 'Documents/DTE/Otros/';
            break;
    }

    // Crear directorio si no existe
    if (!file_exists($directorio)) {
        if (!mkdir($directorio, 0755, true)) {
            logDebug("Error al crear directorio", ['directorio' => $directorio]);
            echo json_encode(['error' => 'Error al crear el directorio: ' . $directorio]);
            exit;
        }
    }

    // Obtener el folio del JSON para el nombre del archivo
    $folio = $jsonObj['Documento']['Encabezado']['IdentificacionDTE']['Folio'] ?? 'sin_folio';

    // Generar nombre de archivo
    $fecha = date('Ymd_His');
    $nombreArchivo = "DTE_{$tipoDTE}_{$folio}_{$fecha}.xml";
    $rutaCompleta = $directorio . $nombreArchivo;

    // Guardar el XML
    $bytesWritten = file_put_contents($rutaCompleta, $response);

    if ($bytesWritten === false) {
        logDebug("Error al guardar archivo XML", ['ruta' => $rutaCompleta]);
        echo json_encode(['error' => 'Error al guardar el archivo XML']);
        exit;
    }

    // Actualizar el registro en la base de datos con la nueva ruta del archivo
    try {
        $stmt = $conn->prepare("UPDATE tb_facturas_dte SET nombre_archivo = ? WHERE id = ?");
        $stmt->execute([$rutaCompleta, $id]);

        echo json_encode([
            'success' => true,
            'mensaje' => 'DTE reprocesado exitosamente',
            'archivo' => $nombreArchivo,
            'ruta' => $rutaCompleta
        ]);
    } catch (PDOException $e) {
        logDebug("Error al actualizar la base de datos", ['error' => $e->getMessage()]);
        echo json_encode(['error' => 'Error al actualizar la base de datos: ' . $e->getMessage()]);
        exit;
    }

} catch (Exception $e) {
    logDebug("Error general", ['error' => $e->getMessage()]);
    echo json_encode(['error' => 'Error: ' . $e->getMessage()]);
    exit;
} catch (PDOException $e) {
    logDebug("Error en el proceso", [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    echo json_encode([
        'error' => 'Error en el servidor',
        'detalles' => $e->getMessage()
    ]);
}

logDebug("=== Fin del proceso de reprocesamiento ===");



