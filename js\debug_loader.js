/**
 * debug_loader.js
 * Script para verificar la carga correcta de los archivos JavaScript
 */

// Verificar si debug_viewer.js se ha cargado correctamente
document.addEventListener('DOMContentLoaded', function() {
    console.log('Verificando carga de scripts de depuración...');
    
    // Verificar si las funciones de debug_viewer.js están disponibles
    const debugFunctions = [
        'formatLogEntry',
        'addLogEntry',
        'clearLogs',
        'showMessage',
        'analyzeAndSummarizeLogs',
        'exportLogs',
        'filterLogs',
        'searchLogs'
    ];
    
    let missingFunctions = [];
    
    debugFunctions.forEach(function(funcName) {
        if (typeof window[funcName] !== 'function') {
            missingFunctions.push(funcName);
            console.error(`Función ${funcName} no está disponible en el ámbito global`);
        }
    });
    
    if (missingFunctions.length > 0) {
        console.error('Algunas funciones de debug_viewer.js no están disponibles:', missingFunctions);
        
        // Verificar si el archivo debug_viewer.js existe
        const scriptElements = document.querySelectorAll('script');
        let debugViewerFound = false;
        
        scriptElements.forEach(function(script) {
            if (script.src && script.src.includes('debug_viewer.js')) {
                debugViewerFound = true;
                console.log('Script debug_viewer.js encontrado en el DOM:', script.src);
                
                // Verificar si el script se cargó correctamente
                if (script.readyState) {
                    console.log('Estado de carga:', script.readyState);
                }
            }
        });
        
        if (!debugViewerFound) {
            console.error('Script debug_viewer.js no encontrado en el DOM');
        }
        
        // Intentar cargar el script de nuevo
        console.log('Intentando cargar debug_viewer.js de nuevo...');
        
        const script = document.createElement('script');
        script.src = 'js/debug_viewer.js?v=' + new Date().getTime(); // Añadir timestamp para evitar caché
        script.onload = function() {
            console.log('debug_viewer.js cargado correctamente');
            
            // Verificar de nuevo las funciones
            let stillMissing = [];
            debugFunctions.forEach(function(funcName) {
                if (typeof window[funcName] !== 'function') {
                    stillMissing.push(funcName);
                }
            });
            
            if (stillMissing.length > 0) {
                console.error('Algunas funciones siguen sin estar disponibles después de recargar:', stillMissing);
            } else {
                console.log('Todas las funciones están disponibles ahora');
            }
        };
        
        script.onerror = function() {
            console.error('Error al cargar debug_viewer.js');
        };
        
        document.head.appendChild(script);
    } else {
        console.log('Todas las funciones de debug_viewer.js están disponibles');
    }
});
