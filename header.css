
.icon-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.5rem;
    color: white;
}

.cart-icon i {
    position: relative;
    z-index: 1;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex; 
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.user-icon {
    position: relative;
    cursor: pointer;
    font-size: 1.5rem;
    color: white;
    transition: color 0.3s ease;
}

.user-icon:hover {
    color: var(--hover-color);
}

.user-dropdown {
    display: none;
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    z-index: 1000;
    min-width: 160px;
    animation: dropdownFade 0.2s ease-out;
}

@keyframes dropdownFade {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.icon-container:hover .user-dropdown {
    display: block;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--primary-color);
    text-decoration: none;
    transition: background-color 0.2s ease;
    gap: 8px;
}

.user-dropdown a:hover {
    background-color: #f5f5f5;
    color: var(--hover-color);
}

.user-dropdown i {
    margin-right: 8px;
    font-size: 1.1rem;
}



@keyframes dropdownFade {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.icon-container:hover .user-dropdown {
    display: block;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--primary-color);
    text-decoration: none;
    transition: background-color 0.2s ease;
    gap: 8px;
}

.user-dropdown a:hover {
    background-color: #f5f5f5;
    color: var(--hover-color);
}

.user-dropdown i {
    margin-right: 8px;
    font-size: 1.1rem;
}