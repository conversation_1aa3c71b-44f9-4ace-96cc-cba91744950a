
<?php
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_connection.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        throw new Exception('Método no permitido. Use GET para obtener datos del producto.');
    }

    if (!isset($_GET['id'])) {
        http_response_code(400);
        throw new Exception('ID del producto no proporcionado');
    }

    $conn = getConnection();
    $stmt = $conn->prepare("
        SELECT
            id,
            nombre,
            descripcion,
            categoria_id,
            id_subcategoria,
            precio_compra,
            precio_venta,
            stock_minimo,
            stock_maximo,
            unidad_medida,
            ubicacion_almacen,
            es_original,
            fabricante,
            pais_origen,
            codigo_fabricante,
            activo
        FROM repuesto
        WHERE id = ? AND activo = 1
    ");

    $stmt->execute([$_GET['id']]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($product) {
        http_response_code(200);
        echo json_encode([
            'status' => 'success',
            'product' => $product
        ]);
    } else {
        http_response_code(404);
        throw new Exception('Producto no encontrado');
    }

} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error de base de datos: ' . $e->getMessage()
    ]);
} catch(Exception $e) {
    if (http_response_code() === 200) {
        http_response_code(400);
    }
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
