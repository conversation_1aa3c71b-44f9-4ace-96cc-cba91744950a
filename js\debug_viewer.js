/**
 * debug_viewer.js
 * Funciones para visualizar y analizar logs de depuración
 */

// Colores para los diferentes niveles de log
const LOG_COLORS = {
    debug: '#9C27B0',    // Morado
    info: '#2196F3',     // Azul
    warning: '#FF9800',  // Naranja
    error: '#F44336',    // Rojo
    critical: '#B71C1C', // Rojo oscuro
    success: '#4CAF50'   // Verde
};

// Función para formatear una entrada de log en HTML
function formatLogEntry(entry) {
    const level = entry.level || 'info';
    const message = entry.message || 'Sin mensaje';
    const data = entry.data || null;
    const timestamp = entry.timestamp || new Date().toISOString();
    
    const color = LOG_COLORS[level] || '#757575';
    const levelUpper = level.toUpperCase();
    
    let html = `<div class="log-entry ${level}" style="margin: 5px 0; padding: 10px; border-left: 4px solid ${color}; background: #f8f9fa; border-radius: 4px;">`;
    html += `<div style="display: flex; justify-content: space-between;">`;
    html += `<span style="color: #666;">[${timestamp}]</span>`;
    html += `<span style="color: ${color}; font-weight: bold;">${levelUpper}</span>`;
    html += `</div>`;
    
    html += `<div style="margin: 5px 0; font-weight: 500;">${message}</div>`;
    
    if (data !== null) {
        html += `<div class="log-data" style="margin-top: 5px; padding: 8px; background: #f1f1f1; border-radius: 3px; font-family: monospace; font-size: 13px;">`;
        
        if (typeof data === 'object') {
            html += `<details>`;
            html += `<summary style="cursor: pointer; color: #0066cc;">Ver detalles</summary>`;
            html += `<pre style="margin: 5px 0; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>`;
            html += `</details>`;
        } else {
            html += `<pre style="margin: 0; overflow-x: auto;">${data}</pre>`;
        }
        
        html += `</div>`;
    }
    
    html += `</div>`;
    
    return html;
}

// Función para agregar una entrada de log al contenedor
function addLogEntry(level, message, data = null) {
    const entry = {
        level: level,
        message: message,
        data: data,
        timestamp: new Date().toISOString()
    };
    
    const logContainer = document.getElementById('detailedLogsContainer');
    if (logContainer) {
        const logContent = document.getElementById('detailedLogs');
        if (logContent) {
            const logHtml = formatLogEntry(entry);
            logContent.innerHTML += logHtml;
            
            // Hacer scroll hasta el final
            logContent.scrollTop = logContent.scrollHeight;
        }
    }
}

// Función para limpiar todos los logs
function clearLogs() {
    const logContent = document.getElementById('detailedLogs');
    if (logContent) {
        logContent.innerHTML = '';
    }
}

// Función para mostrar un mensaje de respuesta
function showMessage(type, message) {
    const messageContainer = document.getElementById('responseMessage');
    if (messageContainer) {
        messageContainer.className = `alert alert-${type}`;
        messageContainer.innerHTML = message;
        messageContainer.style.display = 'block';
        
        // Hacer scroll hasta el mensaje
        messageContainer.scrollIntoView({ behavior: 'smooth' });
    }
}

// Función para analizar logs y mostrar un resumen
function analyzeAndSummarizeLogs() {
    const logContent = document.getElementById('detailedLogs');
    if (!logContent) return;
    
    const logEntries = logContent.querySelectorAll('.log-entry');
    if (logEntries.length === 0) return;
    
    const summary = {
        total: logEntries.length,
        byLevel: {
            debug: 0,
            info: 0,
            warning: 0,
            error: 0,
            critical: 0,
            success: 0
        },
        hasErrors: false,
        executionTime: null
    };
    
    // Contar entradas por nivel
    logEntries.forEach(entry => {
        const level = entry.classList[1];
        if (summary.byLevel.hasOwnProperty(level)) {
            summary.byLevel[level]++;
        }
        
        if (level === 'error' || level === 'critical') {
            summary.hasErrors = true;
        }
        
        // Buscar tiempo de ejecución
        if (entry.textContent.includes('Tiempo de ejecución:')) {
            const match = entry.textContent.match(/Tiempo de ejecución: ([\d.]+) segundos/);
            if (match && match[1]) {
                summary.executionTime = parseFloat(match[1]);
            }
        }
    });
    
    // Crear resumen HTML
    let summaryHtml = `<div class="log-summary" style="margin: 10px 0; padding: 15px; background: #e9ecef; border-radius: 4px;">`;
    summaryHtml += `<h4 style="margin-top: 0;">Resumen de Logs</h4>`;
    summaryHtml += `<div style="display: flex; flex-wrap: wrap; gap: 10px;">`;
    
    // Mostrar conteo por nivel
    for (const level in summary.byLevel) {
        if (summary.byLevel[level] > 0) {
            summaryHtml += `<div style="padding: 5px 10px; background: ${LOG_COLORS[level]}; color: white; border-radius: 4px;">`;
            summaryHtml += `${level.toUpperCase()}: ${summary.byLevel[level]}`;
            summaryHtml += `</div>`;
        }
    }
    
    summaryHtml += `</div>`;
    
    // Mostrar tiempo de ejecución si está disponible
    if (summary.executionTime !== null) {
        summaryHtml += `<div style="margin-top: 10px;">Tiempo total: ${summary.executionTime} segundos</div>`;
    }
    
    // Mostrar estado general
    const statusColor = summary.hasErrors ? '#F44336' : '#4CAF50';
    const statusText = summary.hasErrors ? 'Se encontraron errores' : 'Proceso completado sin errores';
    
    summaryHtml += `<div style="margin-top: 10px; font-weight: bold; color: ${statusColor};">${statusText}</div>`;
    summaryHtml += `</div>`;
    
    // Insertar el resumen al principio del contenedor
    logContent.insertAdjacentHTML('afterbegin', summaryHtml);
}

// Función para exportar logs a un archivo
function exportLogs(format = 'json') {
    const logContent = document.getElementById('detailedLogs');
    if (!logContent) return;
    
    const logEntries = logContent.querySelectorAll('.log-entry');
    if (logEntries.length === 0) return;
    
    const logs = [];
    
    // Extraer información de cada entrada
    logEntries.forEach(entry => {
        const level = entry.classList[1];
        const timestamp = entry.querySelector('span:first-child').textContent.replace('[', '').replace(']', '');
        const message = entry.querySelector('div:nth-child(2)').textContent;
        
        const dataElement = entry.querySelector('.log-data pre');
        let data = null;
        
        if (dataElement) {
            try {
                data = JSON.parse(dataElement.textContent);
            } catch (e) {
                data = dataElement.textContent;
            }
        }
        
        logs.push({
            timestamp,
            level,
            message,
            data
        });
    });
    
    // Crear el contenido del archivo según el formato
    let content = '';
    let filename = `logs_${new Date().toISOString().replace(/:/g, '-')}.`;
    
    if (format === 'json') {
        content = JSON.stringify(logs, null, 2);
        filename += 'json';
        mimeType = 'application/json';
    } else if (format === 'csv') {
        // Cabecera CSV
        content = 'Timestamp,Level,Message\n';
        
        // Agregar cada entrada
        logs.forEach(log => {
            content += `"${log.timestamp}","${log.level}","${log.message.replace(/"/g, '""')}"\n`;
        });
        
        filename += 'csv';
        mimeType = 'text/csv';
    }
    
    // Crear y descargar el archivo
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    
    // Liberar el objeto URL
    setTimeout(() => URL.revokeObjectURL(url), 100);
}

// Función para filtrar logs por nivel
function filterLogs(level) {
    const logContent = document.getElementById('detailedLogs');
    if (!logContent) return;
    
    const logEntries = logContent.querySelectorAll('.log-entry');
    if (logEntries.length === 0) return;
    
    // Si level es 'all', mostrar todas las entradas
    if (level === 'all') {
        logEntries.forEach(entry => {
            entry.style.display = 'block';
        });
        return;
    }
    
    // Ocultar todas las entradas que no coincidan con el nivel
    logEntries.forEach(entry => {
        if (entry.classList.contains(level)) {
            entry.style.display = 'block';
        } else {
            entry.style.display = 'none';
        }
    });
}

// Función para buscar en los logs
function searchLogs(query) {
    if (!query) {
        filterLogs('all');
        return;
    }
    
    const logContent = document.getElementById('detailedLogs');
    if (!logContent) return;
    
    const logEntries = logContent.querySelectorAll('.log-entry');
    if (logEntries.length === 0) return;
    
    const lowerQuery = query.toLowerCase();
    
    // Mostrar solo las entradas que contienen la consulta
    logEntries.forEach(entry => {
        if (entry.textContent.toLowerCase().includes(lowerQuery)) {
            entry.style.display = 'block';
        } else {
            entry.style.display = 'none';
        }
    });
}
