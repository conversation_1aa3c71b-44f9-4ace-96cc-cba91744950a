/* Variables */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #e74c3c;
  --accent-color: #3498db;
  --hover-color: #e67e22;
}

/* Reset y estilos base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Layout */
.inventory-container {
  max-width: 1800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.products-table {
  max-width: 1700px;
  margin: 2rem auto;
  padding: 0 1rem;
  display: block;
}

.search-container {
  max-width: 1400px;
  margin: 1rem auto;
  padding: 0 1rem;
}

/* Modales */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  overflow-y: auto;
}

.modal-content {
  background: white;
  margin: 2rem auto;
  padding: 2rem;
  width: 90%;
  max-width: 944px;
  border-radius: 8px;
  position: relative;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.modal-header {
  background: var(--primary-color);
  padding: 0.8rem 1.2rem;
  border-bottom: none;
  display: flex;
  align-items: center;
  min-height: 48px;
  flex-direction: column;
  align-items: flex-start;
}

.modal-title {
  color: white;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.modal-title i {
  font-size: 0.9rem;
}

.close {
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 24px;
  cursor: pointer;
}

.btn-close {
  padding: 0.5rem;
  background-color: transparent;
  border: none;
  opacity: 0.8;
  filter: brightness(0) invert(1);
  transform: scale(0.8);
}

.btn-close:hover {
  opacity: 1;
  transform: scale(0.85);
}

/* Formularios */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Botones */
.action-buttons {
  background: rgba(44, 62, 80, 0.95);
  padding: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.button-container {
  display: flex;
  gap: 0.8rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0.8rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.action-button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.action-button:hover {
  background: var(--hover-color);
}

.action-button i {
  font-size: 1.1rem;
}

.action-btn {
  padding: 0.8rem 1.5rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: var(--hover-color);
}

.save-btn,
.cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn {
  background-color: var(--accent-color);
  color: white;
}

.cancel-btn {
  background-color: #ccc;
  color: black;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
}

.submit-btn:hover {
  background: var(--hover-color);
}

#search-button {
  padding: 0.5rem 1rem;
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Botones de acción en tablas */
.action-container {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.icon-btn {
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  color: #007bff;
}

.edit-btn:hover {
  color: #0056b3;
}

.assign-btn {
  background-color: transparent;
  color: #28a745;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.assign-btn:hover {
  color: #218838;
}

.delete-btn {
  background-color: transparent;
  color: #dc3545;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.delete-btn:hover {
  color: #c82333;
}

.button-group {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

/* Campos de búsqueda */
.search-input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-input-wrapper {
  margin: 1rem;
  display: flex;
  gap: 1rem;
  position: relative;
  flex-grow: 1;
}

.search-input-wrapper input {
  width: 100%;
  padding-right: 40px;
}

#search-input {
  flex-grow: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Estilos para ordenamiento de tabla */
.sortable {
  cursor: pointer !important;
  position: relative;
  transition: background-color 0.2s;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.sortable:hover {
  background-color: #f5f5f5;
}

.sort-icon {
  margin-left: 5px;
  font-size: 0.8em;
  opacity: 0.5;
  transition: opacity 0.2s;
  display: inline-block;
}

.sortable:hover .sort-icon {
  opacity: 1;
}

.sort-asc .sort-icon:before {
  content: "\f0de" !important;
  opacity: 1;
  color: #007bff;
}

.sort-desc .sort-icon:before {
  content: "\f0dd" !important;
  opacity: 1;
  color: #007bff;
}

.filter-row th {
  padding: 4px;
  background-color: #f8f9fa;
}

.column-search {
  width: 90%;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 12px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.column-search:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.column-search::placeholder {
  color: #aaa;
  font-style: italic;
}

/* Tablas de compatibilidad */
.compatibility-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.compatibility-table th,
.compatibility-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.compatibility-table thead {
  background-color: #f5f5f5;
}

/* Estilos específicos para modales */
#assignModal .modal-content {
  max-width: 900px;
  width: 90%;
  margin: 20px auto;
}

/* Estilos para filas de tabla */
#tabla_repuestos tbody tr[style*="display: none"] {
  display: none !important;
}

.no-results td {
  text-align: center !important;
  padding: 20px !important;
  background-color: #f8f9fa !important;
}

/* Media queries */
@media (max-width: 768px) {
  body {
    padding-top: 70px;
    padding-bottom: 70px;
  }

  .actions-bar {
    flex-direction: column;
  }

  #tabla_repuestos tbody tr {
    display: table-row !important;
  }

  .search-bar input {
    padding: 0.8rem;
    border-radius: 6px;
    border: none;
    font-size: 1rem;
    background: white;
  }
}

@media (min-width: 768px) and (max-width: 1150px) {
  body {
    width: 150%;
  }
}


