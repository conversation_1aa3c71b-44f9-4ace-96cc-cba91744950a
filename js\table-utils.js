// Clase para manejar el ordenamiento de tablas
class TableSorter {
    constructor(tableId) {
        this.table = document.getElementById(tableId);
        if (!this.table) {
            console.error(`Tabla con ID ${tableId} no encontrada`);
            return;
        }
        console.log(`TableSorter inicializado para tabla: ${tableId}`);
        this.init();
    }

    init() {
        const headers = this.table.querySelectorAll('th.sortable');
        console.log(`Encontrados ${headers.length} encabezados ordenables`);
        
        headers.forEach((header, index) => {
            console.log(`Configurando encabezado: ${header.textContent.trim()}`);
            header.addEventListener('click', () => {
                console.log(`Clic en encabezado: ${header.textContent.trim()}`);
                this.sortColumn(header);
            });
        });
    }

    sortColumn(header) {
        const columnIndex = parseInt(header.getAttribute('data-column'));
        console.log(`Ordenando columna ${columnIndex}: ${header.textContent.trim()}`);
        
        const isAsc = !header.classList.contains('sort-asc');
        
        // Remover clases de ordenamiento previas
        this.table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Agregar clase de ordenamiento actual
        header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');
        
        // Ordenar filas
        const tbody = this.table.querySelector('tbody');
        if (!tbody) {
            console.error('No se encontró el cuerpo de la tabla');
            return;
        }
        
        const rows = Array.from(tbody.querySelectorAll('tr:not(.no-results)'));
        console.log(`Ordenando ${rows.length} filas`);
        
        const sortedRows = rows.sort((a, b) => {
            if (!a.cells[columnIndex] || !b.cells[columnIndex]) {
                console.warn('Índice de celda fuera de rango');
                return 0;
            }
            
            const aValue = this.getCellValue(a, columnIndex);
            const bValue = this.getCellValue(b, columnIndex);
            
            return isAsc ? this.compare(aValue, bValue) : this.compare(bValue, aValue);
        });
        
        // Limpiar y reinserar filas ordenadas
        while (tbody.firstChild) {
            tbody.removeChild(tbody.firstChild);
        }
        
        sortedRows.forEach(row => tbody.appendChild(row));
        console.log('Ordenamiento completado');
    }

    getCellValue(row, index) {
        const cell = row.cells[index];
        
        // Manejar casos especiales
        if (cell.querySelector('.category-tag')) {
            // Para celdas con etiquetas de categoría
            return cell.querySelector('.category-tag').textContent.trim();
        } else if (cell.textContent.includes('$')) {
            // Para celdas con precios
            return parseFloat(cell.textContent.replace(/[^0-9.-]+/g, '')) || 0;
        } else {
            // Caso general
            return cell.textContent.trim();
        }
    }

    compare(a, b) {
        // Detectar si son números
        if (!isNaN(a) && !isNaN(b)) {
            return a - b;
        }
        
        // Detectar si son fechas
        const dateA = new Date(a);
        const dateB = new Date(b);
        if (!isNaN(dateA) && !isNaN(dateB)) {
            return dateA - dateB;
        }
        
        // Comparación de cadenas por defecto
        return String(a).localeCompare(String(b), undefined, { numeric: true, sensitivity: 'base' });
    }
}

// Clase para manejar el filtrado de tablas
class TableFilter {
    constructor(tableId, options = {}) {
        this.table = document.getElementById(tableId);
        if (!this.table) {
            console.error(`Tabla con ID ${tableId} no encontrada`);
            return;
        }
        
        this.options = {
            inputSelector: '.column-search',
            ...options
        };
        
        console.log(`TableFilter inicializado para tabla: ${tableId}`);
        this.init();
    }

    init() {
        const inputs = this.table.querySelectorAll(this.options.inputSelector);
        console.log(`Encontrados ${inputs.length} campos de filtrado`);
        
        inputs.forEach((input, index) => {
            input.addEventListener('input', () => {
                console.log(`Filtrado en columna ${index}: ${input.value}`);
                this.filterTable();
            });
            
            // También filtrar al presionar Enter
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.filterTable();
                }
            });
        });
    }

    filterTable() {
        const tbody = this.table.querySelector('tbody');
        if (!tbody) {
            console.error('No se encontró el cuerpo de la tabla');
            return;
        }
        
        const rows = Array.from(tbody.querySelectorAll('tr:not(.no-results)'));
        console.log(`Filtrando ${rows.length} filas`);
        
        const inputs = this.table.querySelectorAll(this.options.inputSelector);
        
        // Recopilar los valores de filtro
        const filters = [];
        inputs.forEach((input, index) => {
            const value = input.value.trim().toLowerCase();
            if (value) {
                filters.push({ index, value });
                console.log(`Filtro activo en columna ${index}: "${value}"`);
            }
        });
        
        console.log(`Total de filtros activos: ${filters.length}`);
        
        // Aplicar filtros a las filas
        let visibleCount = 0;
        
        rows.forEach(row => {
            let showRow = true;
            
            filters.forEach(filter => {
                // Verificar que el índice esté dentro del rango de celdas
                if (filter.index >= row.cells.length) {
                    console.warn(`Índice de filtro ${filter.index} fuera de rango para la fila`);
                    return;
                }
                
                const cell = row.cells[filter.index];
                let cellValue = '';
                
                // Manejar casos especiales
                if (cell.querySelector('.category-tag')) {
                    cellValue = cell.querySelector('.category-tag').textContent.trim().toLowerCase();
                } else {
                    cellValue = cell.textContent.trim().toLowerCase();
                }
                
                if (!cellValue.includes(filter.value)) {
                    showRow = false;
                }
            });
            
            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });
        
        console.log(`Filas visibles después del filtrado: ${visibleCount}`);
        
        // Mostrar mensaje si no hay resultados
        this.updateNoResultsMessage(visibleCount, filters.length > 0);
    }

    updateNoResultsMessage(visibleCount, hasFilters) {
        const tbody = this.table.querySelector('tbody');
        const noResultsRow = tbody.querySelector('.no-results');
        
        if (visibleCount === 0 && hasFilters) {
            console.log('No se encontraron resultados, mostrando mensaje');
            if (!noResultsRow) {
                const tr = document.createElement('tr');
                tr.className = 'no-results';
                const td = document.createElement('td');
                td.colSpan = this.table.querySelector('thead tr').children.length;
                td.textContent = 'No se encontraron resultados';
                td.style.textAlign = 'center';
                td.style.padding = '20px';
                td.style.backgroundColor = '#f8f9fa';
                tr.appendChild(td);
                tbody.appendChild(tr);
            }
        } else if (noResultsRow) {
            console.log('Eliminando mensaje de no resultados');
            tbody.removeChild(noResultsRow);
        }
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando utilidades de tabla');
    
    // Inicializar ordenamiento para todas las tablas con clase 'sortable-table'
    const sortableTables = document.querySelectorAll('.sortable-table');
    sortableTables.forEach(table => {
        new TableSorter(table.id);
    });
    
    // Inicializar filtrado para todas las tablas con clase 'filterable-table'
    const filterableTables = document.querySelectorAll('.filterable-table');
    filterableTables.forEach(table => {
        new TableFilter(table.id);
    });
});
