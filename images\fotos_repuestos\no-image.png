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
