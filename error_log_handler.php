<?php
/**
 * Manejador de logs de error dedicado
 * Este archivo proporciona funciones para registrar errores en múltiples lugares
 */

class ErrorLogHandler {
    private static $logFile = 'sobres_error.log';
    private static $initialized = false;

    /**
     * Inicializa el manejador de logs
     */
    public static function init() {
        if (!self::$initialized) {
            // Registrar manejador de errores personalizado
            set_error_handler([__CLASS__, 'errorHandler']);

            // Registrar manejador de excepciones no capturadas
            set_exception_handler([__CLASS__, 'exceptionHandler']);

            // Registrar función de cierre para capturar errores fatales
            register_shutdown_function([__CLASS__, 'shutdownHandler']);

            self::$initialized = true;
        }
    }

    /**
     * Registra un error en múltiples lugares
     */
    public static function logError($message, $details = [], $type = 'error') {
        $timestamp = date('Y-m-d H:i:s');
        $detailsJson = json_encode($details, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        // 1. Registrar en archivo de log dedicado
        $logMessage = "[$timestamp][$type] $message\n";
        if (!empty($details)) {
            $logMessage .= "Detalles: $detailsJson\n";
        }
        $logMessage .= "---------------------------------------------\n";

        // Verificar si el directorio existe, si no, intentar crearlo
        $logDir = dirname(self::$logFile);
        if (!empty($logDir) && $logDir != '.' && !file_exists($logDir)) {
            @mkdir($logDir, 0777, true);
        }

        // Intentar crear el archivo si no existe
        if (!file_exists(self::$logFile)) {
            @touch(self::$logFile);
            @chmod(self::$logFile, 0777);
        }

        // Guardar el mensaje en el archivo
        $result = @file_put_contents(self::$logFile, $logMessage, FILE_APPEND);

        // Si falla, intentar guardar en un archivo alternativo
        if ($result === false) {
            $alternativeLog = 'error_' . date('Ymd') . '.log';
            @file_put_contents($alternativeLog, $logMessage, FILE_APPEND);
        }

        // 2. Registrar en error_log de PHP
        error_log("[$type] $message - " . substr($detailsJson, 0, 500));

        // 3. Intentar guardar en la base de datos
        self::saveToDatabase($message, $detailsJson, $type);

        // 4. Guardar en un archivo de texto plano en el directorio actual como último recurso
        $emergencyLog = 'emergency_error_log.txt';
        @file_put_contents($emergencyLog, $logMessage, FILE_APPEND);

        return true;
    }

    /**
     * Guarda el error en la base de datos
     */
    private static function saveToDatabase($message, $details, $type) {
        try {
            require_once 'db_connection.php';
            $conn = getConnection();

            // Verificar si la tabla existe
            $stmt = $conn->query("SHOW TABLES LIKE 'tb_error_logs'");
            if ($stmt->rowCount() == 0) {
                // Crear la tabla si no existe
                $conn->exec("
                    CREATE TABLE IF NOT EXISTS tb_error_logs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tipo VARCHAR(50) NOT NULL,
                        mensaje TEXT NOT NULL,
                        detalles TEXT,
                        fecha DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ");
            }

            // Insertar el error
            $stmt = $conn->prepare("
                INSERT INTO tb_error_logs (tipo, mensaje, detalles, fecha)
                VALUES (:tipo, :mensaje, :detalles, NOW())
            ");

            $stmt->bindParam(':tipo', $type);
            $stmt->bindParam(':mensaje', $message);
            $stmt->bindParam(':detalles', $details);
            $stmt->execute();

            return true;
        } catch (Exception $e) {
            // No podemos hacer mucho si falla el guardado en la base de datos
            error_log("Error al guardar error en la base de datos: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Manejador de errores personalizado
     */
    public static function errorHandler($errno, $errstr, $errfile, $errline) {
        $type = 'error';
        switch ($errno) {
            case E_WARNING:
            case E_USER_WARNING:
                $type = 'warning';
                break;
            case E_NOTICE:
            case E_USER_NOTICE:
                $type = 'notice';
                break;
        }

        self::logError($errstr, [
            'file' => $errfile,
            'line' => $errline,
            'code' => $errno
        ], $type);

        // Devolver false para permitir que el manejador de errores estándar de PHP también se ejecute
        return false;
    }

    /**
     * Manejador de excepciones no capturadas
     */
    public static function exceptionHandler($exception) {
        self::logError($exception->getMessage(), [
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $exception->getTraceAsString()
        ], 'exception');
    }

    /**
     * Manejador de cierre para capturar errores fatales
     */
    public static function shutdownHandler() {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::logError($error['message'], [
                'file' => $error['file'],
                'line' => $error['line'],
                'code' => $error['type']
            ], 'fatal');
        }
    }

    /**
     * Obtiene los últimos errores registrados
     */
    public static function getLastErrors($limit = 10) {
        $errors = [];

        // Leer del archivo de log
        if (file_exists(self::$logFile)) {
            $content = file_get_contents(self::$logFile);
            $entries = explode("---------------------------------------------", $content);
            $entries = array_filter($entries);
            $entries = array_slice($entries, -$limit);

            foreach ($entries as $entry) {
                $errors[] = trim($entry);
            }
        }

        return $errors;
    }
}

// Inicializar el manejador de logs
ErrorLogHandler::init();
?>
