// Datos de ejemplo de productos
let products = [];

// Función para comprobar el estado de la conexión
// Función para comprobar el estado de la conexión

// Cargar productos desde la base de datos


// Función para renderizar productos
function renderProducts(view = "grid", productsToRender = products) {
    const container = document.getElementById("products-container");

    if (view === "grid") {
        container.className = "products-grid";
        const productsHTML = productsToRender
            .map(
                (product) => `
            <div class="product-card" data-id="${product.id}">
                <img 
                    src="${product.image}" 
                    alt="${product.brand} ${product.model}" 
                    class="product-image"
                    onerror="this.onerror=null; this.src='https://via.placeholder.com/300x200?text=Imagen+no+disponible';"
                >
                <div class="product-info">
                    <div class="product-details">
                        <div class="detail-row">
                            <span class="detail-label">SKU:</span>
                            <span class="product-sku">${product.sku || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Marca:</span>
                            <span class="product-brand">${product.brand}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Modelo:</span>
                            <span class="product-model">${product.model}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Año:</span>
                            <span class="product-year">${product.year || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Motor:</span>
                            <span class="product-engine">${product.motor || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Precio:</span>
                            <span class="product-price">$${parseFloat(product.price).toLocaleString()}</span>
                        </div>
                    </div>
                    <div class="controls-container">
                        <div class="quantity-controls">
                            <button class="quantity-btn minus">-</button>
                            <span class="quantity-display">0</span>
                            <button class="quantity-btn plus">+</button>
                        </div>
                        <button class="add-to-cart-btn">Agregar</button>
                    </div>
                </div>
            </div>
        `,
            )
            .join("");
        container.innerHTML = productsHTML;
    } else {
        container.className = "products-table";
        const tableHTML = `
            <table>
                <thead>
                    <tr>
                        <th data-col="imagen">Imagen</th>
                        <th data-col="marca">Marca</th>
                        <th data-col="modelo">Modelo</th>
                        <th data-col="año">Año</th>
                        <th data-col="motor">Motor</th>
                        <th data-col="precio">Precio</th>
                        <th data-col="cantidad">Cantidad</th>
                        <th data-col="accion">Acción</th>
                    </tr>
                </thead>
                <tbody>
                    ${productsToRender
                        .map(
                            (product) => `
                        <tr>
                            <td><img src="${product.image}" alt="${product.brand}" class="table-image" data-id="${product.id}"></td>
                            <td>${product.brand}</td>
                            <td>${product.model}</td>
                            <td>${product.year}</td>
                            <td>${product.motor}</td>
                            <td>$${parseFloat(product.price).toLocaleString()}</td>
                            <td>
                                <div class="quantity-controls">
                                    <button class="quantity-btn minus">-</button>
                                    <span class="quantity-display">0</span>
                                    <button class="quantity-btn plus">+</button>
                                </div>
                            </td>
                            <td>
                                <button class="add-to-cart-btn">Agregar</button>
                            </td>
                        </tr>
                    `,
                        )
                        .join("")}
                </tbody>
            </table>
        `;
        container.innerHTML = tableHTML;
    }
}

// Carrito de compras
let cart = [];

function updateCartCount() {
    const cartCount = document.querySelector(".cart-count");
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
}

function addToCart(productId, quantity) {
    debugger; // Agrega este breakpoint para pausar la ejecución aquí
    const product = products.find((p) => p.id === productId);
    const existingItem = cart.find((item) => item.id === productId);

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            ...product,
            quantity,
        });
    }

    updateCartCount();
    renderCart();
}

function renderCart() {
    const cartItems = document.querySelector(".cart-items");
    const cartHTML = cart
        .map(
            (item) => `
        <div class="cart-item" data-id="${item.id}">
            <button class="delete-item-btn">&times;</button>
            <img src="${item.image}" alt="${item.brand}" class="cart-item-image">
            <div class="cart-item-details">
                <h4>${item.brand} ${item.model}</h4>
                <div class="cart-quantity-controls">
                    <button class="cart-quantity-btn minus">-</button>
                    <span class="quantity-display">${item.quantity}</span>
                    <button class="cart-quantity-btn plus">+</button>
                </div>
                <p>Precio unitario: $${item.price.toFixed(2)}</p>
                <p>Subtotal: $${(item.price * item.quantity).toFixed(2)}</p>
            </div>
        </div>
    `,
        )
        .join("");

    cartItems.innerHTML = cartHTML;

    const total = cart.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0,
    );
    document.querySelector(".cart-total").innerHTML = `
        <h3>Total: $${total.toFixed(2)}</h3>
    `;
}

// Populate years in the select
function populateYears() {
    const yearSelect = document.getElementById("año");
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= 1990; year--) {
        const option = document.createElement("option");
        option.value = year;
        option.textContent = year;
        yearSelect.appendChild(option);
    }
}

// Event Listeners
// Función para cargar el footer
function loadFooter() {
    fetch("footer.html")
        .then((response) => response.text())
        .then((data) => {
            document.body.insertAdjacentHTML("beforeend", data);
        });
}

document.addEventListener("DOMContentLoaded", () => {
    checkDatabaseStatus();
    loadFooter();
    // Filter toggle functionality
    const filterToggle = document.querySelector(".filter-toggle");
    const searchBar = document.querySelector(".search-bar");

    filterToggle.addEventListener("click", () => {
        searchBar.classList.toggle("collapsed");
    });
    renderProducts("grid");
    populateYears();

    // View toggle functionality
    document.querySelectorAll(".view-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
            document.querySelectorAll(".view-btn")
                   .forEach((b) => b.classList.remove("active"));
            btn.classList.add("active");
            
            const container = document.getElementById("products-container");
            const gridView = container.querySelector(".products-grid");
            const tableView = container.querySelector(".products-table");
            
            if (btn.dataset.view === "grid") {
                gridView.style.display = "grid";
                tableView.style.display = "none";
            } else {
                gridView.style.display = "none";
                tableView.style.display = "block";
            }
        });
    });

    // Manejo de cantidades
    document
        .getElementById("products-container")
        .addEventListener("click", (e) => {
            const card = e.target.closest(".product-card");
            const row = e.target.closest("tr");
            const element = card || row;
            if (!element) return;

            const quantityDisplay = element.querySelector(".quantity-display");
            let quantity = parseInt(quantityDisplay.textContent);

            if (e.target.classList.contains("minus")) {
                quantity = Math.max(0, quantity - 1);
                quantityDisplay.textContent = quantity;
            }

            if (e.target.classList.contains("plus")) {
                quantity = Math.min(99, quantity + 1);
                quantityDisplay.textContent = quantity;
            }

            if (e.target.classList.contains("add-to-cart-btn")) {
                const productId = card
                    ? parseInt(card.dataset.id)
                    : parseInt(
                          row
                              .querySelector("td:first-child img")
                              .getAttribute("data-id"),
                      );
                if (quantity > 0) {
                    addToCart(productId, quantity);
                    quantityDisplay.textContent = "0";
                }
            }
        });

    // Toggle carrito
    const cartIcon = document.querySelector(".cart-icon");
    const cartModal = document.querySelector(".cart-modal");
    const closeCart = document.querySelector(".close-cart");

    // Search functionality
    const searchIcon = document.querySelector(".search-icon");
    const searchInput = document.querySelector(".search-input-wrapper input");

    searchIcon.addEventListener("click", () => {
        const searchTerm = searchInput.value.toLowerCase();
        const marcaFilter = document
            .getElementById("marca")
            .value.toLowerCase();
        const modeloFilter = document
            .getElementById("modelo")
            .value.toLowerCase();
        const yearFilter = document.getElementById("año").value;
        const motorFilter = document.getElementById("motor").value;

        const filteredProducts = products.filter((product) => {
            const matchesSearch =
                searchTerm === "" ||
                product.brand.toLowerCase().includes(searchTerm) ||
                product.model.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm);

            const matchesMarca =
                marcaFilter === "" ||
                product.brand.toLowerCase() === marcaFilter;
            const matchesModelo =
                modeloFilter === "" ||
                product.model.toLowerCase() === modeloFilter;
            const matchesYear =
                yearFilter === "" || product.year.toString() === yearFilter;
            const matchesMotor =
                motorFilter === "" || product.motor === motorFilter;

            return (
                matchesSearch &&
                matchesMarca &&
                matchesModelo &&
                matchesYear &&
                matchesMotor
            );
        });

        renderFilteredProducts(filteredProducts);
    });

    cartIcon.addEventListener("click", () => {
        cartModal.classList.add("active");
    });

    closeCart.addEventListener("click", () => {
        cartModal.classList.remove("active");
    });
});
function updateCartItemQuantity(productId, change) {
    const item = cart.find((item) => item.id === productId);
    if (item) {
        const newQuantity = item.quantity + change;
        if (newQuantity > 0) {
            item.quantity = newQuantity;
        } else {
            removeFromCart(productId);
        }
        updateCartCount();
        renderCart();
    }
}

function removeFromCart(productId) {
    cart = cart.filter((item) => item.id !== productId);
    updateCartCount();
    renderCart();
}
document.querySelector(".cart-items").addEventListener("click", (e) => {
    const cartItem = e.target.closest(".cart-item");
    if (!cartItem) return;

    const productId = parseInt(cartItem.dataset.id);

    if (e.target.classList.contains("delete-item-btn")) {
        removeFromCart(productId);
    }

    if (e.target.classList.contains("plus")) {
        updateCartItemQuantity(productId, 1);
    }

    if (e.target.classList.contains("minus")) {
        updateCartItemQuantity(productId, -1);
    }
});
function renderFilteredProducts(filteredProducts) {
    const container = document.getElementById("products-container");
    const currentView = container.className;

    if (filteredProducts.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 2rem; width: 100%;">
                <i class="fas fa-search" style="font-size: 3rem; color: #ccc;"></i>
                <h3 style="margin-top: 1rem; color: #666;">No se encontraron productos que coincidan con tu búsqueda</h3>
            </div>`;
        return;
    }

    if (currentView === "products-grid") {
        renderProducts("grid", filteredProducts);
    } else {
        renderProducts("table", filteredProducts);
    }
}

function navigateRow(rowIndex, direction) {
    const row = document.querySelector(
        `.row-container[data-row="${rowIndex}"] .product-row`,
    );
    const cards = row.querySelectorAll(".product-card");
    const cardWidth = cards[0].offsetWidth;
    const currentScroll = row.scrollLeft;

    if (direction === "left") {
        row.scrollTo({
            left: currentScroll - cardWidth,
            behavior: "smooth",
        });
    } else {
        row.scrollTo({
            left: currentScroll + cardWidth,
            behavior: "smooth",
        });
    }
}

function populateModels() {
    const marcaSelect = document.getElementById("marca");
    const modeloSelect = document.getElementById("modelo");

    // Limpiar modelos anteriores
    modeloSelect.innerHTML = `<option value="">Modelo</option>`;

    const models = {
        peugeot: [
            "108",
            "208",
            "308",
            "408",
            "508",
            "2008",
            "3008",
            "5008",
            "Partner",
            "205",
            "307",
            "RCZ",
        ],
        renault: [
            "Twingo",
            "Clio",
            "Mégane",
            "Talisman",
            "Captur",
            "Kadjar",
            "Koleos",
            "Arkana",
            "Duster",
            "Kangoo",
            "Trafic",
            "Espace",
            "Oroch",
            "A110",
        ],
        citroen: [
            "C1",
            "C3",
            "C4",
            "C-Elysée",
            "C3 Aircross",
            "C5 Aircross",
            "Berlingo",
            "2CV",
            "C4 Picasso",
            "Grand C4 Picasso",
        ],
    };

    const selectedMarca = marcaSelect.value;
    if (selectedMarca && models[selectedMarca]) {
        models[selectedMarca].forEach((model) => {
            const option = document.createElement("option");
            option.value = model;
            option.textContent = model;
            modeloSelect.appendChild(option);
        });
    }
}
// Función para comprobar el estado de la conexión
function checkDatabaseStatus() {
    const indicator = document.getElementById("db-status");
    indicator.style.backgroundColor = "yellow";

    fetch("http://0.0.0.0:3000/api/status", {
        method: "GET",
        headers: {
            Accept: "application/json",
            "Cache-Control": "no-cache",
        },
        cache: "no-cache",
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error("Error en la respuesta del servidor");
            }
            return response.json();
        })
        .then((data) => {
            if (data.status === "success") {
                indicator.style.backgroundColor = "green";
                console.log("Conexión exitosa:", data.message);
            } else {
                indicator.style.backgroundColor = "red";
                console.error("Error de conexión:", data.message);
            }
        })
        .catch((error) => {
            indicator.style.backgroundColor = "red";
            console.error("Error al verificar estado:", error.message);
        });
}

// Llamar a la función cuando se carga la página
document.addEventListener("DOMContentLoaded", () => {
    checkDatabaseStatus();
    // Verificar estado cada 30 segundos
    setInterval(checkDatabaseStatus, 30000);
});

