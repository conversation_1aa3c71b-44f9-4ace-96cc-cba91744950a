
<?php
require_once 'db_connection.php';

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['difference']) || !isset($data['id'])) {
        throw new Exception('Datos incompletos');
    }

    $conn = getConnection();
    $stmt = $conn->prepare("UPDATE venta SET 
        monto_pago_diferencia = :monto,
        fecha_pago_diferencia = NOW(),
        estado = 'COMPLETADA',
        updated_at = NOW()
        WHERE id = :id");
        
    $stmt->execute([
        ':monto' => $data['difference'],
        ':id' => $data['id']
    ]);
    
    echo json_encode(['status' => 'success', 'message' => 'Pago procesado correctamente']);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
