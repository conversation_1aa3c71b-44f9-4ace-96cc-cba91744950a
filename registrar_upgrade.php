<?php
// Iniciar buffer de salida
ob_start();

// Incluir archivos necesarios
require_once 'db_connection.php';

// Configurar manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Array para almacenar mensajes de depuración
$debugMessages = [];

// Función para registrar mensajes de depuración
function debugLog($message, $type = 'info') {
    global $debugMessages;

    // Registrar en el log del servidor
    error_log($message);

    // Guardar para mostrar en la respuesta JSON
    $timestamp = date('H:i:s');
    $debugMessages[] = [
        'time' => $timestamp,
        'message' => $message,
        'type' => $type
    ];

    return $message;
}

// Establecer encabezados para evitar el caché y especificar el tipo de contenido
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

try {
    // Verificar método de solicitud
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    // Registrar los datos recibidos para depuración
    debugLog('Datos POST recibidos: ' . print_r($_POST, true));
    debugLog('Archivos recibidos: ' . print_r($_FILES, true));

    // Validar campos requeridos
    if (empty($_POST['pagina_modificada']) || empty($_POST['descripcion'])) {
        throw new Exception('Todos los campos son obligatorios');
    }

    $paginaModificada = $_POST['pagina_modificada'];

    // Simplemente tomar la descripción tal como viene, sin procesar los saltos de línea
    $descripcion = $_POST['descripcion'];

    $rutaArchivo = null;
    $tipoArchivo = 'ninguno';

    // Aumentar los límites de tiempo y memoria para archivos grandes
    ini_set('max_execution_time', 600); // 10 minutos
    ini_set('memory_limit', '512M');     // 512 MB

    // Configurar límites de subida de archivos
    $maxFileSize = 100 * 1024 * 1024; // 100 MB
    ini_set('upload_max_filesize', '100M');
    ini_set('post_max_size', '100M');

    // Registrar límites actuales para depuración
    debugLog('Límites de PHP: max_execution_time=' . ini_get('max_execution_time') .
             ', memory_limit=' . ini_get('memory_limit') .
             ', upload_max_filesize=' . ini_get('upload_max_filesize') .
             ', post_max_size=' . ini_get('post_max_size'));

    // Procesar el archivo si fue enviado
    debugLog('Verificando archivo subido...');
    if (isset($_FILES['archivo']) && !empty($_FILES['archivo']['name'])) {
        // Verificar si hay errores en la subida
        if ($_FILES['archivo']['error'] !== UPLOAD_ERR_OK) {
            $errorMessage = 'Error al subir el archivo: ';
            switch ($_FILES['archivo']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                    $errorMessage .= 'El archivo excede el tamaño máximo permitido por PHP (upload_max_filesize)';
                    break;
                case UPLOAD_ERR_FORM_SIZE:
                    $errorMessage .= 'El archivo excede el tamaño máximo permitido por el formulario';
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $errorMessage .= 'El archivo se subió parcialmente';
                    break;
                case UPLOAD_ERR_NO_FILE:
                    // No mostrar error si no se subió ningún archivo, ya que es opcional
                    debugLog('No se subió ningún archivo, continuando con el registro sin archivo', 'info');
                    break;
                case UPLOAD_ERR_NO_TMP_DIR:
                    $errorMessage .= 'No se encuentra el directorio temporal';
                    break;
                case UPLOAD_ERR_CANT_WRITE:
                    $errorMessage .= 'Error al escribir el archivo en el disco';
                    break;
                case UPLOAD_ERR_EXTENSION:
                    $errorMessage .= 'Una extensión de PHP detuvo la subida del archivo';
                    break;
                default:
                    $errorMessage .= 'Error desconocido (código: ' . $_FILES['archivo']['error'] . ')';
            }

            // Solo lanzar excepción si no es UPLOAD_ERR_NO_FILE (que es aceptable)
            if ($_FILES['archivo']['error'] !== UPLOAD_ERR_NO_FILE) {
                debugLog($errorMessage, 'error');
                throw new Exception($errorMessage);
            }
        }

        // Si no hay errores, procesar el archivo
        if ($_FILES['archivo']['error'] === UPLOAD_ERR_OK) {
        debugLog('Archivo encontrado: ' . print_r($_FILES['archivo'], true));

        // Registrar tamaño del archivo para depuración
        $fileSizeMB = $_FILES['archivo']['size'] / 1024 / 1024;
        debugLog('Tamaño del archivo: ' . round($fileSizeMB, 2) . ' MB');

        $fileName = $_FILES['archivo']['name'];
        $fileType = $_FILES['archivo']['type'];
        $fileSize = $_FILES['archivo']['size'];
        $fileTmpName = $_FILES['archivo']['tmp_name'];
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Verificar si el archivo es una imagen o un video
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

        // Determinar el tipo de archivo
        if (in_array($fileExtension, $imageExtensions)) {
            debugLog('Archivo identificado como imagen', 'success');
            $tipoArchivo = 'imagen';
        } elseif (in_array($fileExtension, $videoExtensions)) {
            debugLog('Archivo identificado como video', 'success');
            $tipoArchivo = 'video';
        } else {
            throw new Exception('Tipo de archivo no soportado. Solo se permiten imágenes y videos.');
        }

        // Directorio para guardar archivos
        $uploadDir = 'images/upgrades/';
        $absoluteUploadDir = __DIR__ . '/' . $uploadDir;

        // Verificar y crear directorio si no existe
        if (!file_exists($absoluteUploadDir)) {
            if (!mkdir($absoluteUploadDir, 0777, true)) {
                throw new Exception('No se pudo crear el directorio para guardar el archivo');
            }
        }

        // Generar nombre único para el archivo
        $uniqueId = uniqid();
        $timestamp = time();
        $safeFilename = preg_replace('/[^\w\-\.]/', '_', pathinfo($fileName, PATHINFO_FILENAME));
        $newFileName = 'upgrade_' . $safeFilename . '_' . $uniqueId . '_' . $timestamp . '.' . $fileExtension;
        $filePath = $absoluteUploadDir . $newFileName;

        debugLog('Ruta del archivo a guardar: ' . $filePath);

        // Mover el archivo subido con manejo de errores mejorado
        debugLog('Intentando mover el archivo a: ' . $filePath);

        // Verificar permisos del directorio
        if (!is_writable($absoluteUploadDir)) {
            debugLog('El directorio no tiene permisos de escritura: ' . $absoluteUploadDir, 'error');
            throw new Exception('El directorio de destino no tiene permisos de escritura');
        }

        // Verificar espacio en disco
        $freeSpace = disk_free_space(__DIR__);
        $freeSpaceMB = $freeSpace / 1024 / 1024;
        debugLog('Espacio libre en disco: ' . round($freeSpaceMB, 2) . ' MB');

        if ($freeSpace < $fileSize * 1.5) {
            debugLog('Espacio insuficiente en disco', 'error');
            throw new Exception('No hay suficiente espacio en disco para guardar el archivo');
        }

        // Intentar mover el archivo
        if (!move_uploaded_file($fileTmpName, $filePath)) {
            $errorMessage = 'Error al guardar el archivo';

            // Obtener más información sobre el error
            if (!file_exists($fileTmpName)) {
                $errorMessage .= ': El archivo temporal no existe';
            } elseif (!is_readable($fileTmpName)) {
                $errorMessage .= ': El archivo temporal no se puede leer';
            }

            debugLog($errorMessage, 'error');
            throw new Exception($errorMessage);
        }

        // Verificar que el archivo se haya guardado correctamente
        if (!file_exists($filePath)) {
            debugLog('El archivo no se guardó correctamente', 'error');
            throw new Exception('El archivo no se guardó correctamente');
        }

        debugLog('Archivo guardado correctamente en: ' . $filePath, 'success');

        // Asignar la ruta relativa para guardar en la BD
        $rutaArchivo = $uploadDir . $newFileName;
        debugLog('Ruta relativa guardada: ' . $rutaArchivo);
        } // Cierre del if para UPLOAD_ERR_OK
    } // Cierre del if para isset($_FILES['archivo'])

    // Conectar a la base de datos
    try {
        $conn = getConnection();

        // Verificar si la tabla tb_upgrades existe
        $checkTableSql = "SHOW TABLES LIKE 'tb_upgrades'";
        $checkTableStmt = $conn->query($checkTableSql);
        $tableExists = $checkTableStmt->rowCount() > 0;

        // Crear tabla si no existe
        if (!$tableExists) {
            $createTableSql = "
                CREATE TABLE tb_upgrades (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    pagina_modificada VARCHAR(100) NOT NULL,
                    descripcion TEXT NOT NULL,
                    ruta_archivo VARCHAR(255),
                    tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno',
                    usuario VARCHAR(100) DEFAULT 'admin'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $conn->exec($createTableSql);
            debugLog('Tabla tb_upgrades creada');
        }

        // Preparar la consulta SQL
        $sql = "INSERT INTO tb_upgrades (pagina_modificada, descripcion, ruta_archivo, tipo_archivo)
                VALUES (:pagina_modificada, :descripcion, :ruta_archivo, :tipo_archivo)";

        $stmt = $conn->prepare($sql);

        // Vincular parámetros
        $stmt->bindParam(':pagina_modificada', $paginaModificada);
        $stmt->bindParam(':descripcion', $descripcion);
        $stmt->bindParam(':ruta_archivo', $rutaArchivo);
        $stmt->bindParam(':tipo_archivo', $tipoArchivo);

        // Ejecutar la consulta
        if (!$stmt->execute()) {
            $errorInfo = $stmt->errorInfo();
            throw new Exception('Error al guardar en la base de datos: ' . $errorInfo[2]);
        }

        // Obtener el ID del registro insertado
        $lastId = $conn->lastInsertId();
        debugLog('Registro insertado con ID: ' . $lastId);

        // Preparar respuesta
        $response = [
            'status' => 'success',
            'message' => 'Registro de upgrade guardado correctamente',
            'id' => $lastId,
            'data' => [
                'pagina_modificada' => $paginaModificada,
                'descripcion' => $descripcion,
                'ruta_archivo' => $rutaArchivo,
                'tipo_archivo' => $tipoArchivo
            ],
            'debug_messages' => $debugMessages
        ];

        echo json_encode($response);

    } catch (PDOException $e) {
        throw new Exception('Error de base de datos: ' . $e->getMessage());
    }

    // Cerrar la conexión
    if (isset($conn)) {
        $conn = null;
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug' => [
            'error_type' => get_class($e),
            'error_line' => $e->getLine(),
            'error_file' => $e->getFile()
        ],
        'debug_messages' => $debugMessages
    ]);
}

// Limpiar y cerrar el buffer de salida
ob_end_flush();
