<?php
require_once 'session_config.php';
session_start();

// Verificar autenticación
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    http_response_code(401);
    die('<!DOCTYPE html><html><head><title>Error</title></head><body><h1>Error 401: No autorizado</h1><p>Debe iniciar sesión para acceder a esta funcionalidad.</p></body></html>');
}

// Configurar zona horaria para Santiago de Chile
date_default_timezone_set('America/Santiago');

// Recibir datos de la cotización
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['client']) || !isset($input['items'])) {
    http_response_code(400);
    die('<!DOCTYPE html><html><head><title>Error</title></head><body><h1>Error 400: Datos inválidos</h1><p>Los datos de la cotización no son válidos.</p></body></html>');
}

// Calcular totales (sin IVA)
$subtotal = 0;
foreach ($input['items'] as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}
$iva = 0; // IVA eliminado
$total = $subtotal; // Total igual al subtotal

// Generar HTML para impresión
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cotización - TATA REPUESTOS</title>
    <style>
        @page {
            size: 80mm 297mm;
            margin: 0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.3;
            width: 80mm;
            padding: 5mm;
            margin: 0 auto;
            font-weight: bold;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .company-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .brand-logo {
            width: 45px;
            height: auto;
            max-height: 35px;
            object-fit: contain;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 10px;
            color: #000;
            font-weight: bold;
        }
        
        .divider {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        
        .quote-info {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .quote-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-size: 12px;
        }
        
        .client-info {
            margin-bottom: 10px;
        }
        
        .client-info div {
            margin-bottom: 2px;
        }
        
        .products-table {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .product-item {
            margin-bottom: 8px;
        }
        
        .product-name {
            font-weight: bold;
        }
        
        .product-details {
            display: flex;
            justify-content: space-between;
            margin-top: 2px;
        }
        
        .product-description {
            font-size: 9px;
            color: #333;
            margin-left: 10px;
            margin-top: 2px;
            font-weight: bold;
        }
        
        .totals {
            text-align: right;
            margin-bottom: 10px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .total-row.final {
            font-weight: bold;
            font-size: 13px;
            margin-top: 5px;
        }
        
        .notes {
            margin-top: 10px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
            color: #000;
            font-weight: bold;
        }
        
        @media print {
            body {
                width: 80mm;
                margin: 0;
            }
            
            .no-print {
                display: none;
            }
            
            .company-logos {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;
            }
            
            .brand-logo {
                width: 40px;
                height: auto;
                max-height: 30px;
                object-fit: contain;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-logos">
            <?php if (file_exists('images/citroen.png')): ?>
            <img src="images/citroen.png" alt="Citroën" class="brand-logo">
            <?php endif; ?>
            <?php if (file_exists('images/peugeot.jpg')): ?>
            <img src="images/peugeot.jpg" alt="Peugeot" class="brand-logo">
            <?php endif; ?>
            <?php if (file_exists('images/renault.png')): ?>
            <img src="images/renault.png" alt="Renault" class="brand-logo">
            <?php endif; ?>
        </div>
        <div class="company-name">TATA REPUESTOS</div>
        <div class="company-info">            
            Manuel montt 1286, Local 2<br>
            Tel: +56989370572
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="quote-info">
        <div class="quote-title">COTIZACIÓN</div>
        <div>N° <?php echo date('Ymd-His'); ?></div>
        <div>Fecha: <?php echo date('d/m/Y H:i'); ?></div>
    </div>
    
    <div class="divider"></div>
    
    <div class="client-info">
        <div class="section-title">Datos del Cliente</div>
        <div>Nombre: <?php echo htmlspecialchars($input['client']['name']); ?></div>
        <?php if (!empty($input['client']['rut'])): ?>
        <div>RUT: <?php echo htmlspecialchars($input['client']['rut']); ?></div>
        <?php endif; ?>
        <?php if (!empty($input['client']['email'])): ?>
        <div>Email: <?php echo htmlspecialchars($input['client']['email']); ?></div>
        <?php endif; ?>
        <?php if (!empty($input['client']['phone'])): ?>
        <div>Tel: <?php echo htmlspecialchars($input['client']['phone']); ?></div>
        <?php endif; ?>
    </div>
    
    <div class="divider"></div>
    
    <div class="products">
        <div class="section-title">Productos</div>
        <?php foreach ($input['items'] as $item): 
            $itemTotal = $item['price'] * $item['quantity'];
        ?>
        <div class="product-item">
            <div class="product-name"><?php echo htmlspecialchars($item['name']); ?></div>
            <div class="product-details">
                <span><?php echo $item['quantity']; ?> x $<?php echo number_format($item['price'], 0, ',', '.'); ?></span>
                <span>$<?php echo number_format($itemTotal, 0, ',', '.'); ?></span>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <div class="divider"></div>
    
    <div class="totals">
        <div class="total-row">
            <span>Subtotal:</span>
            <span>$<?php echo number_format($subtotal, 0, ',', '.'); ?></span>
        </div>
        <div class="total-row final">
            <span>TOTAL:</span>
            <span>$<?php echo number_format($total, 0, ',', '.'); ?></span>
        </div>
    </div>
    
    <?php if (!empty($input['notes'])): ?>
    <div class="divider"></div>
    <div class="notes">
        <div class="section-title">Notas</div>
        <?php echo nl2br(htmlspecialchars($input['notes'])); ?>
    </div>
    <?php endif; ?>
    
    <div class="footer">
        <div class="divider"></div>
        Gracias por su preferencia<br>
        Cotización válida por 30 días
    </div>
    
    <div class="no-print" style="margin-top: 30px; text-align: center;">
        <button onclick="window.print();" style="padding: 10px 20px; font-size: 14px; cursor: pointer;">
            Imprimir Cotización
        </button>
        <button onclick="window.close();" style="padding: 10px 20px; font-size: 14px; cursor: pointer; margin-left: 10px;">
            Cerrar
        </button>
    </div>
    
    <script>
        // Auto imprimir al cargar
        window.onload = function() {
            // window.print();
        };
    </script>
</body>
</html>