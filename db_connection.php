<?php
function getConnection() {
    try {
        // Añadimos timeout para la conexión
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_PERSISTENT => false,  // Asegura conexiones no persistentes
            PDO::ATTR_TIMEOUT => 5,         // Timeout de 5 segundos
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
            PDO::ATTR_EMULATE_PREPARES => false,
        ];

        // Registrar intento de conexión
        error_log("Intentando conectar a la base de datos: host=***************, dbname=Tata_Repuestos, user=ncornejo");

        $startTime = microtime(true);
        $conn = new PDO(
            "mysql:host=***************;port=3306;dbname=Tata_Repuestos;charset=utf8mb4",
            "ncornejo",
            "N1c0l7as17",
            $options
        );
        $endTime = microtime(true);
        $connectionTime = round(($endTime - $startTime) * 1000, 2); // en milisegundos

        error_log("Conexión establecida en $connectionTime ms");

        // Verificar la conexión con una consulta simple
        $testStmt = $conn->query('SELECT 1');
        if (!$testStmt) {
            throw new Exception("Conexión establecida pero no funcional");
        }

        error_log("Prueba de consulta exitosa");
        return $conn;
    } catch(PDOException $e) {
        error_log("Error PDO al conectar a la base de datos: " . $e->getMessage());
        // Registrar información adicional para diagnóstico
        error_log("Detalles de conexión: host=***************, port=3306, dbname=Tata_Repuestos");
        error_log("Código de error PDO: " . $e->getCode());

        // Verificar si es un error de timeout
        if (strpos($e->getMessage(), 'timeout') !== false || $e->getCode() == 2002) {
            throw new Exception("Error de conexión: Timeout al conectar con el servidor de base de datos. Verifique la conectividad de red.");
        }

        throw new Exception("Error de conexión a la base de datos: " . $e->getMessage());
    } catch(Exception $e) {
        error_log("Error general al conectar a la base de datos: " . $e->getMessage());
        throw $e;
    }
}

function closeConnection($conn) {
    if ($conn) {
        $conn = null; // Esto cerrará explícitamente la conexión
    }
}