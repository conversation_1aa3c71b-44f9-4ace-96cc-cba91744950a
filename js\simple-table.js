// Funciones simples para ordenar y filtrar tablas
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando funciones de tabla simple');

    // 1. Configurar ordenamiento
    setupSorting();

    // 2. Configurar filtrado
    setupFiltering();

    // Mensaje de inicialización
    console.log('Inicialización de tabla completada');
});

// Función para configurar el ordenamiento
function setupSorting() {
    const table = document.getElementById('tabla_repuestos');
    if (!table) {
        console.error('Tabla no encontrada');
        return;
    }

    const headers = table.querySelectorAll('th.sortable');
    console.log(`Encontrados ${headers.length} encabezados ordenables`);

    headers.forEach(header => {
        // Asegurarse de que el encabezado tenga un cursor pointer
        header.style.cursor = 'pointer';

        header.addEventListener('click', function() {
            // Obtener el índice de columna, con fallback a la posición del encabezado
            let columnIndex;
            if (this.hasAttribute('data-column')) {
                columnIndex = parseInt(this.getAttribute('data-column'));
            } else {
                // Calcular el índice basado en la posición del encabezado
                const headerRow = this.parentElement;
                columnIndex = Array.from(headerRow.children).indexOf(this);
            }

            const sortType = this.getAttribute('data-sort') || 'string';

            console.log(`Ordenando columna ${columnIndex} (${sortType})`);

            // Determinar dirección
            let sortDirection = 'asc';
            if (this.classList.contains('sort-asc')) {
                sortDirection = 'desc';
                this.classList.remove('sort-asc');
                this.classList.add('sort-desc');
            } else {
                // Limpiar todas las clases de ordenamiento
                table.querySelectorAll('th').forEach(th => {
                    th.classList.remove('sort-asc', 'sort-desc');
                });
                this.classList.add('sort-asc');
            }

            // Ordenar la tabla
            sortTableByColumn(table, columnIndex, sortDirection, sortType);
        });
    });
}

// Función para ordenar la tabla por columna
function sortTableByColumn(table, columnIndex, direction, sortType) {
    const tbody = table.querySelector('tbody');
    if (!tbody) {
        console.error('No se encontró el cuerpo de la tabla');
        return;
    }

    // Verificar si hay múltiples elementos tbody (puede ocurrir en algunas tablas)
    let allRows = [];
    if (table.querySelectorAll('tbody').length > 1) {
        table.querySelectorAll('tbody').forEach(tb => {
            allRows = [...allRows, ...Array.from(tb.querySelectorAll('tr:not(.no-results)'))];
        });
    } else {
        allRows = Array.from(tbody.querySelectorAll('tr:not(.no-results)'));
    }

    console.log(`Ordenando ${allRows.length} filas`);

    // Función para obtener el valor de una celda
    const getCellValue = (row, index) => {
        if (!row.cells || index >= row.cells.length) {
            console.warn('Celda no encontrada en fila:', row, 'indice:', index);
            return '';
        }

        const cell = row.cells[index];
        let value = cell.textContent.trim();

        // Manejar casos especiales
        if (cell.querySelector('.category-tag')) {
            value = cell.querySelector('.category-tag').textContent.trim();
        } else if (value.includes('$')) {
            // Extraer valor numérico de precios
            value = parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
        }

        return value;
    };

    // Función para comparar valores
    const compareValues = (a, b) => {
        if (sortType === 'number') {
            // Convertir a números para comparación numérica
            const aNum = parseFloat(a) || 0;
            const bNum = parseFloat(b) || 0;
            return aNum - bNum;
        } else if (sortType === 'date') {
            // Convertir a fechas para comparación de fechas
            const aDate = new Date(a);
            const bDate = new Date(b);
            return aDate - bDate;
        } else {
            // Comparación de cadenas por defecto
            return String(a).localeCompare(String(b), undefined, { numeric: true });
        }
    };

    // Ordenar filas
    const sortedRows = allRows.sort((rowA, rowB) => {
        const aValue = getCellValue(rowA, columnIndex);
        const bValue = getCellValue(rowB, columnIndex);

        return direction === 'asc'
            ? compareValues(aValue, bValue)
            : compareValues(bValue, aValue);
    });

    // Limpiar y reconstruir la tabla
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    sortedRows.forEach(row => {
        tbody.appendChild(row);
    });

    console.log('Ordenamiento completado');
}

// Función para configurar el filtrado
function setupFiltering() {
    const table = document.getElementById('tabla_repuestos');
    if (!table) {
        console.error('Tabla no encontrada para filtrado');
        return;
    }

    const filterInputs = table.querySelectorAll('.column-search');
    console.log(`Encontrados ${filterInputs.length} campos de filtrado`);

    if (filterInputs.length === 0) {
        console.warn('No se encontraron campos de filtrado. Verificando si hay una fila de filtros para agregar.');

        // Intentar agregar una fila de filtros si no existe
        const thead = table.querySelector('thead');
        if (thead && thead.querySelector('tr')) {
            const headerRow = thead.querySelector('tr');
            const headerCells = headerRow.querySelectorAll('th');

            // Crear una nueva fila para filtros
            const filterRow = document.createElement('tr');
            filterRow.className = 'filter-row';

            headerCells.forEach((cell, index) => {
                const th = document.createElement('th');

                // No agregar input en la columna de acciones (generalmente la última)
                if (index === headerCells.length - 1 && cell.textContent.trim().toLowerCase().includes('acciones')) {
                    th.innerHTML = '';
                } else {
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'form-control form-control-sm column-search';
                    input.setAttribute('data-column', index);
                    input.placeholder = 'Filtrar...';
                    input.style.width = '90%';
                    input.style.padding = '6px 8px';
                    input.style.borderRadius = '4px';
                    input.style.border = '1px solid #ddd';
                    input.style.margin = '4px 0';

                    th.appendChild(input);
                }

                filterRow.appendChild(th);
            });

            // Insertar la fila de filtros después de la fila de encabezados
            headerRow.after(filterRow);

            // Volver a buscar los inputs de filtro
            const newFilterInputs = table.querySelectorAll('.column-search');
            console.log(`Se agregaron ${newFilterInputs.length} campos de filtrado`);

            // Configurar eventos para los nuevos inputs
            newFilterInputs.forEach(input => {
                input.addEventListener('input', function() {
                    filterTable();
                });

                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        filterTable();
                    }
                });
            });
        }
    } else {
        // Configurar eventos para los inputs existentes
        filterInputs.forEach(input => {
            input.addEventListener('input', function() {
                filterTable();
            });

            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    filterTable();
                }
            });
        });
    }
}

// Función para filtrar la tabla
function filterTable() {
    const table = document.getElementById('tabla_repuestos');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    if (!tbody) return;

    // Verificar si hay múltiples elementos tbody
    let allRows = [];
    if (table.querySelectorAll('tbody').length > 1) {
        table.querySelectorAll('tbody').forEach(tb => {
            allRows = [...allRows, ...Array.from(tb.querySelectorAll('tr:not(.no-results)'))];
        });
    } else {
        allRows = Array.from(tbody.querySelectorAll('tr:not(.no-results)'));
    }

    const filterInputs = table.querySelectorAll('.column-search');

    // Recopilar filtros activos
    const filters = [];
    filterInputs.forEach(input => {
        const value = input.value.trim().toLowerCase();
        if (value) {
            // Obtener el índice de columna del atributo data-column o calcular basado en la posición
            let columnIndex;
            if (input.hasAttribute('data-column')) {
                columnIndex = parseInt(input.getAttribute('data-column'));
            } else {
                // Buscar el índice basado en la posición del input en la fila de filtros
                const filterCell = input.closest('th');
                const filterRow = filterCell.parentElement;
                columnIndex = Array.from(filterRow.children).indexOf(filterCell);
            }

            filters.push({ index: columnIndex, value });
        }
    });

    console.log(`Aplicando ${filters.length} filtros`);

    // Aplicar filtros
    let visibleCount = 0;

    allRows.forEach(row => {
        let showRow = true;

        filters.forEach(filter => {
            if (!row.cells || filter.index >= row.cells.length) return;

            const cell = row.cells[filter.index];
            let cellText = cell.textContent.trim().toLowerCase();

            // Manejar casos especiales
            if (cell.querySelector('.category-tag')) {
                cellText = cell.querySelector('.category-tag').textContent.trim().toLowerCase();
            }

            if (!cellText.includes(filter.value)) {
                showRow = false;
            }
        });

        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });

    // Mostrar mensaje si no hay resultados
    updateNoResultsMessage(tbody, visibleCount, filters.length > 0);

    console.log(`Filas visibles después del filtrado: ${visibleCount}`);
}

// Función para actualizar el mensaje de "No se encontraron resultados"
function updateNoResultsMessage(tbody, visibleCount, hasFilters) {
    const table = document.getElementById('tabla_repuestos');
    if (!table || !tbody) return;

    // Buscar mensaje existente en cualquier tbody
    let noResultsRow = null;
    table.querySelectorAll('tbody .no-results').forEach(row => {
        noResultsRow = row;
    });

    if (visibleCount === 0 && hasFilters) {
        if (!noResultsRow) {
            const tr = document.createElement('tr');
            tr.className = 'no-results';
            const td = document.createElement('td');

            // Obtener el número de columnas de la primera fila de encabezado
            const headerRow = table.querySelector('thead tr');
            td.colSpan = headerRow ? headerRow.children.length : 5; // Valor por defecto si no hay encabezado

            td.textContent = 'No se encontraron resultados';
            td.style.textAlign = 'center';
            td.style.padding = '20px';
            td.style.backgroundColor = '#f8f9fa';
            td.style.fontStyle = 'italic';
            tr.appendChild(td);
            tbody.appendChild(tr);

            console.log('Mensaje de "No se encontraron resultados" agregado');
        }
    } else if (noResultsRow) {
        noResultsRow.parentNode.removeChild(noResultsRow);
        console.log('Mensaje de "No se encontraron resultados" eliminado');
    }
}
