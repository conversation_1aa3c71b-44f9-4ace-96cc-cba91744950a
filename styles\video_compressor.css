/* Estilos para el compresor de video */
.compression-container {
    margin-top: 15px;
    display: none;
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px;
    border: 1px solid #ddd;
}

.compression-status {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.compression-progress-container {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.compression-progress {
    height: 100%;
    background-color: #17a2b8;
    color: white;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    transition: width 0.3s ease;
    width: 0%;
}

.compression-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.compression-info {
    font-size: 12px;
    color: #666;
}

.compression-cancel {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 12px;
}

.compression-cancel:hover {
    background-color: #c82333;
}

/* Estilos para el botón de compresión */
.compress-video-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 10px;
    display: none;
}

.compress-video-btn:hover {
    background-color: #138496;
}

/* Estilos para el contenedor de video */
.video-preview-container {
    margin-top: 10px;
    display: none;
}

.video-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 5px;
}

/* Estilos para el mensaje de tamaño */
.file-size-warning {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}
