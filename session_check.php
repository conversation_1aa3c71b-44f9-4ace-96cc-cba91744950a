<?php
/**
 * Endpoint para verificar si la sesión está activa
 * Se usa para comprobar si la sesión permanece activa sin requerir interacción del usuario
 */

// Incluir configuración de sesiones para prevenir cierre por inactividad
require_once 'session_config.php';
session_start();

// Cabecera JSON para la respuesta
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');

// Registrar información para depuración
error_log('--- DEBUG session_check.php ---');
error_log('Session ID: ' . session_id());
error_log('Contenido de $_SESSION: ' . print_r($_SESSION, true));
error_log('Hora actual: ' . date('Y-m-d H:i:s', time()));
error_log('Hora última actividad: ' . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'No establecida'));
error_log('Diferencia en segundos: ' . (isset($_SESSION['login_time']) ? (time() - $_SESSION['login_time']) : 'N/A'));

// Verificar si el usuario está logueado
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Sesión no activa',
        'logged_in' => false
    ]);
    exit;
}

// Actualizar la hora de la última actividad
$_SESSION['login_time'] = time();

// Regenerar ID de sesión periódicamente para mayor seguridad
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 3600) {
    // Regenerar ID cada hora de uso activo
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
    error_log('Regenerando ID de sesión: ' . session_id());
}

// Responder con información de la sesión
echo json_encode([
    'status' => 'success',
    'message' => 'Sesión activa',
    'logged_in' => true,
    'user' => isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 'Unknown',
    'session_started' => isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'Unknown',
    'session_active_for' => isset($_SESSION['login_time']) ? secondsToHumanReadable(time() - $_SESSION['login_time']) : 'Unknown',
    'session_id' => session_id() // Solo para depuración, no exponer en producción
]);

/**
 * Convierte segundos a formato legible para humanos (días, horas, minutos, segundos)
 * @param int $seconds Número de segundos
 * @return string Tiempo en formato legible
 */
function secondsToHumanReadable($seconds) {
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    $result = '';
    if ($days > 0) $result .= "$days días, ";
    if ($hours > 0) $result .= "$hours horas, ";
    if ($minutes > 0) $result .= "$minutes minutos, ";
    $result .= "$secs segundos";
    
    return $result;
}
?>