<?php
header('Content-Type: application/json');
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Validate required fields
    $required_fields = ['repuesto_id', 'marca', 'modelo', 'cilindrada', 'anio_inicio', 'anio_fin', 'combustible'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("Campo requerido: $field");
        }
    }

    // Insert into vehiculo_compatible table
    $stmt = $conn->prepare("
        INSERT INTO vehiculo_compatible 
        (repuesto_id, marca_id, modelo_id, cilindrada, anio, combustible) 
        VALUES 
        (:repuesto_id, :marca_id, :modelo_id, :cilindrada, :anio, :combustible)
    ");

    // For each year in the range, create a compatibility record
    for ($year = $_POST['anio_inicio']; $year <= $_POST['anio_fin']; $year++) {
        $params = [
            ':repuesto_id' => $_POST['repuesto_id'],
            ':marca_id' => $_POST['marca'],
            ':modelo_id' => $_POST['modelo'],
            ':cilindrada' => $_POST['cilindrada'],
            ':anio' => $year,
            ':combustible' => $_POST['combustible']
        ];
        
        $stmt->execute($params);
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Compatibilidad registrada correctamente'
    ]);

} catch(Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
