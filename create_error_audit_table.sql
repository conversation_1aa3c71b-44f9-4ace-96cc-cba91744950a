-- Crear tabla para auditoría de errores
CREATE TABLE IF NOT EXISTS tb_error_audit (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_type VARCHAR(50) NOT NULL COMMENT 'Tipo de error (file, permission, api, etc)',
    error_code VARCHAR(50) NULL COMMENT 'Código de error, si está disponible',
    error_message TEXT NOT NULL COMMENT 'Mensaje de error',
    error_details LONGTEXT NULL COMMENT 'Detalles técnicos en formato JSON',
    source_file VARCHAR(255) NULL COMMENT 'Archivo PHP que generó el error',
    source_line INT NULL COMMENT 'Línea del archivo que generó el error',
    request_data LONGTEXT NULL COMMENT 'Datos de la solicitud en formato JSON',
    session_data LONGTEXT NULL COMMENT 'Datos de la sesión en formato JSON',
    user_id INT NULL COMMENT 'ID del usuario si está disponible',
    ip_address VARCHAR(45) NULL COMMENT 'Dirección IP del cliente',
    resolution_status ENUM('pending', 'in_progress', 'resolved', 'ignored') DEFAULT 'pending',
    resolution_notes TEXT NULL COMMENT 'Notas sobre la resolución',
    resolved_by VARCHAR(50) NULL COMMENT 'Usuario que resolvió el problema',
    resolved_at DATETIME NULL COMMENT 'Fecha y hora de resolución',
    INDEX idx_error_type (error_type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_resolution_status (resolution_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Tabla de auditoría de errores para diagnóstico';

-- Crear tabla para relacionar errores con documentos específicos
CREATE TABLE IF NOT EXISTS tb_error_documentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    error_id INT NOT NULL COMMENT 'Referencia a tb_error_logs o tb_error_audit',
    documento_id INT NOT NULL COMMENT 'ID del documento involucrado en el error',
    fecha DATETIME DEFAULT CURRENT_TIMESTAMP,
    detalles TEXT NULL COMMENT 'Información adicional específica del documento',
    INDEX idx_error_id (error_id),
    INDEX idx_documento_id (documento_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Relación entre errores y documentos';

-- Índices adicionales para tb_error_logs existente para mejorar rendimiento de consultas
CREATE INDEX IF NOT EXISTS idx_error_logs_tipo ON tb_error_logs (tipo);
CREATE INDEX IF NOT EXISTS idx_error_logs_fecha ON tb_error_logs (fecha);

-- Índices adicionales para tb_facturas_dte para mejorar rendimiento
CREATE INDEX IF NOT EXISTS idx_facturas_estado_sobre ON tb_facturas_dte (estado_sobre);
CREATE INDEX IF NOT EXISTS idx_facturas_tipo_dte ON tb_facturas_dte (tipo_dte);

-- Actualizar tb_error_audit con registros existentes de tb_error_logs
-- Ejecutar después de crear la tabla para migrar datos existentes
INSERT INTO tb_error_audit (
    timestamp,
    error_type,
    error_message,
    error_details,
    source_file,
    resolution_status
)
SELECT 
    fecha,
    tipo,
    mensaje,
    detalles,
    NULL,
    'pending'
FROM tb_error_logs;
