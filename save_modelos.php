<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    $sql = "INSERT INTO vehiculo_compatible (
        modelo_id, anio_inicio, anio_fin, 
        cilindrada, version, tipo_motor, combustible
    ) VALUES (
        :modelo_id, :anio_inicio, :anio_fin,
        :cilindrada, :version, :tipo_motor, :combustible
    )";
    
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([
        ':modelo_id' => $_POST['modelo_id'],
        ':anio_inicio' => $_POST['anio_inicio'],
        ':anio_fin' => $_POST['anio_fin'],
        ':cilindrada' => $_POST['cilindrada'],
        ':version' => $_POST['version'],
        ':tipo_motor' => $_POST['tipo_motor'],
        ':combustible' => $_POST['combustible']
    ]);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Vehículo compatible guardado exitosamente'
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
