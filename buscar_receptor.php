<?php
header('Content-Type: application/json');

// Verificar que sea una solicitud GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

// Obtener el RUT del parámetro de la solicitud
$rut = $_GET['rut'] ?? '';

if (empty($rut)) {
    echo json_encode(['success' => false, 'message' => 'RUT no proporcionado']);
    exit;
}

try {
    // Conectar a la base de datos
    require_once 'db_connection.php';
    $conn = getConnection();
    
    // Sanitizar el RUT (eliminar caracteres no permitidos)
    $rut = preg_replace('/[^0-9kK\-]/', '', $rut);
    
    // Para depuración
    error_log("Buscando receptor con RUT: " . $rut);
    
    // Preparar y ejecutar la consulta
    $stmt = $conn->prepare("SELECT * FROM tb_receptores WHERE rut = ?");
    $stmt->execute([$rut]);
    
    // Obtener el resultado
    $receptor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($receptor) {
        // Si se encontró un receptor, devolver sus datos
        error_log("Receptor encontrado: " . json_encode($receptor));
        echo json_encode([
            'success' => true,
            'data' => $receptor
        ]);
    } else {
        // Si no se encontró un receptor, devolver un mensaje
        error_log("No se encontró receptor con RUT: " . $rut);
        echo json_encode([
            'success' => false,
            'message' => 'No se encontró ningún receptor con ese RUT'
        ]);
    }
} catch (PDOException $e) {
    // En caso de error en la base de datos
    error_log("Error de base de datos: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Error en la base de datos',
        'message' => $e->getMessage()
    ]);
}
?>
