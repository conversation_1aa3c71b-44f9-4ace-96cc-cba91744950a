<?php
// debug_almacen.php
// Script para depurar problemas con la tabla almacen

require_once 'db_connection.php';

header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Verificar la conexión
    echo "Conexión a la base de datos establecida correctamente.<br>";
    
    // Listar todas las tablas
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Tablas en la base de datos:<br>";
    echo "<pre>" . print_r($tables, true) . "</pre>";
    
    // Verificar si la tabla almacen existe
    $tableExists = in_array('almacen', $tables);
    
    if ($tableExists) {
        echo "La tabla 'almacen' existe.<br>";
        
        // Mostrar la estructura de la tabla
        $stmt = $conn->query("DESCRIBE almacen");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Estructura de la tabla 'almacen':<br>";
        echo "<pre>" . print_r($columns, true) . "</pre>";
        
        // Contar registros
        $stmt = $conn->query("SELECT COUNT(*) as total FROM almacen");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "La tabla 'almacen' contiene " . $count['total'] . " registros.<br>";
        
        // Mostrar los registros
        if ($count['total'] > 0) {
            $stmt = $conn->query("SELECT * FROM almacen");
            $almacenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "Registros en la tabla 'almacen':<br>";
            echo "<pre>" . print_r($almacenes, true) . "</pre>";
        }
    } else {
        echo "La tabla 'almacen' NO existe.<br>";
        
        // Crear la tabla
        echo "Creando la tabla 'almacen'...<br>";
        
        $createTableSql = "
            CREATE TABLE almacen (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nombre VARCHAR(100) NOT NULL,
                descripcion TEXT,
                activo TINYINT(1) DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $conn->exec($createTableSql);
        
        echo "Tabla 'almacen' creada exitosamente.<br>";
        
        // Insertar un almacén por defecto
        $insertSql = "INSERT INTO almacen (nombre, descripcion, activo) VALUES ('TEMUCO', 'Almacén principal', 1)";
        $conn->exec($insertSql);
        
        echo "Almacén por defecto 'TEMUCO' insertado exitosamente.<br>";
    }
    
    // Verificar si la tabla almacenes (en plural) existe
    $tableExistsPlural = in_array('almacenes', $tables);
    
    if ($tableExistsPlural) {
        echo "La tabla 'almacenes' (en plural) también existe.<br>";
        
        // Mostrar la estructura de la tabla
        $stmt = $conn->query("DESCRIBE almacenes");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Estructura de la tabla 'almacenes':<br>";
        echo "<pre>" . print_r($columns, true) . "</pre>";
        
        // Contar registros
        $stmt = $conn->query("SELECT COUNT(*) as total FROM almacenes");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "La tabla 'almacenes' contiene " . $count['total'] . " registros.<br>";
        
        // Mostrar los registros
        if ($count['total'] > 0) {
            $stmt = $conn->query("SELECT * FROM almacenes");
            $almacenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "Registros en la tabla 'almacenes':<br>";
            echo "<pre>" . print_r($almacenes, true) . "</pre>";
            
            // Migrar datos si es necesario
            echo "Migrando datos de 'almacenes' a 'almacen'...<br>";
            
            foreach ($almacenes as $almacen) {
                // Verificar si ya existe en la tabla almacen
                $stmt = $conn->prepare("SELECT id FROM almacen WHERE nombre = ?");
                $stmt->execute([$almacen['nombre']]);
                $exists = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$exists) {
                    // Insertar en la tabla almacen
                    $stmt = $conn->prepare("INSERT INTO almacen (nombre, descripcion, activo) VALUES (?, ?, ?)");
                    $stmt->execute([
                        $almacen['nombre'],
                        $almacen['descripcion'] ?? null,
                        $almacen['activo'] ?? 1
                    ]);
                    
                    echo "Almacén '" . $almacen['nombre'] . "' migrado exitosamente.<br>";
                } else {
                    echo "Almacén '" . $almacen['nombre'] . "' ya existe en la tabla 'almacen'.<br>";
                }
            }
        }
    } else {
        echo "La tabla 'almacenes' (en plural) NO existe.<br>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
