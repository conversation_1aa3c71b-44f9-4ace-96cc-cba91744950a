<?php
require_once 'db_connection.php';
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');

try {
    $conn = getConnection();
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $marca = isset($_GET['marca']) ? trim($_GET['marca']) : '';
    $modelo = isset($_GET['modelo']) ? trim($_GET['modelo']) : '';
    $anio = isset($_GET['anio']) ? trim($_GET['anio']) : '';
    $combustible = isset($_GET['combustible']) ? trim($_GET['combustible']) : '';
    $cilindrada = isset($_GET['cilindrada']) ? trim($_GET['cilindrada']) : '';

    $sql = "SELECT 
            r.id, r.sku, r.nombre, r.descripcion, r.precio_venta,
            COALESCE(vc.combustible, '') as combustible,
            COALESCE(m.nombre, '') as marca_nombre,
            COALESCE(mo.nombre, '') as modelo_nombre,            
            COALESCE(vc.cilindrada, '') as cilindrada,
            url_imagen,
            vc.anio_inicio as anio
        FROM repuesto r
        LEFT JOIN repuesto_compatible rc ON r.id = rc.repuesto_id
        LEFT JOIN vehiculo_compatible vc ON rc.vehiculo_id = vc.id        
        LEFT JOIN modelo mo ON mo.id = vc.modelo_id
        LEFT JOIN marca m ON m.id = mo.marca_id
        WHERE r.activo = 1";
    
    $params = array();
    
    if (!empty($marca)) {
        $sql .= " AND m.id LIKE ?";
        $params[] = "%$marca%";
    }
    if (!empty($modelo)) {
        $sql .= " AND mo.nombre LIKE ?";
        $params[] = "%$modelo%";
    }
    if (!empty($anio)) {
        $sql .= " AND vc.anio = ?";
        $params[] = $anio;
    }
    if (!empty($combustible)) {
        $sql .= " AND vc.combustible LIKE ?";
        $params[] = "%$combustible%";
    }
    if (!empty($cilindrada)) {
        $sql .= " AND vc.cilindrada LIKE ?";
        $params[] = "%$cilindrada%";
    }
    if (!empty($search)) {
        $sql .= " AND (r.nombre LIKE ? OR r.descripcion LIKE ? OR r.sku LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    // Añadir el LIMIT al final de la consulta
    $sql .= " LIMIT 100";

    // Log de la consulta SQL y sus parámetros
    error_log("SQL generado: " . $sql);
    error_log("Parámetros SQL: " . json_encode($params));
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['status' => 'success', 'data' => $products]);

} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>