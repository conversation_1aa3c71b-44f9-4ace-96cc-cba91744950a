/**
 * <PERSON>ript para manejar la solicitud automática de folios CAF
 * cuando se detecta que se ha alcanzado el límite de folios
 */

// Función para solicitar folios automáticamente
function solicitarFoliosAutomaticamente(tipoDTE) {
    console.log(`Iniciando solicitud automática de folios para tipo DTE: ${tipoDTE}`);
    
    // Mostrar indicador de carga
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-indicator';
    loadingIndicator.innerHTML = `
        <div class="spinner"></div>
        <p>Solicitando folios CAF para documento tipo ${tipoDTE}...</p>
    `;
    document.body.appendChild(loadingIndicator);
    
    // Crear datos para la solicitud
    const formData = new FormData();
    formData.append('tipoDTE', tipoDTE);
    
    // Realizar la solicitud AJAX
    fetch('solicitar_folios_auto.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log(`Respuesta recibida. Status: ${response.status}`);
        if (!response.ok) {
            throw new Error(`Error en la respuesta del servidor: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Respuesta de solicitud de folios:', data);
        
        // Eliminar indicador de carga
        if (loadingIndicator) {
            document.body.removeChild(loadingIndicator);
        }
        
        // Mostrar notificación de éxito o error
        if (data.success) {
            showNotification(`
                Folios CAF solicitados exitosamente.
                Rango: ${data.rango_inicial} - ${data.rango_final}
                Total folios: ${data.total_folios}
            `, 'success');
            
            // Actualizar el folio en el formulario si está abierto
            const folioInput = document.getElementById('folioDTE');
            if (folioInput) {
                folioInput.value = data.rango_inicial;
                folioInput.classList.remove('last-folio');
                folioInput.classList.add('new-folio');
                
                // Quitar la clase después de un tiempo
                setTimeout(() => {
                    folioInput.classList.remove('new-folio');
                }, 3000);
            }
        } else {
            showNotification(`
                Error al solicitar folios CAF:
                ${data.error || 'Error desconocido'}
            `, 'error');
        }
    })
    .catch(error => {
        console.error('Error en solicitud de folios:', error);
        
        // Eliminar indicador de carga
        if (loadingIndicator) {
            document.body.removeChild(loadingIndicator);
        }
        
        // Mostrar notificación de error
        showNotification(`
            Error al solicitar folios CAF:
            ${error.message || 'Error desconocido'}
        `, 'error');
    });
}

// Función para mostrar notificaciones
function showNotification(message, type = 'info') {
    // Verificar si ya existe la función en el ámbito global
    if (typeof window.showNotification === 'function') {
        // Usar la función existente
        window.showNotification(message, type);
        return;
    }
    
    // Implementación propia si no existe la función global
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            ${type === 'success' ? '<i class="fas fa-check-circle"></i>' : 
              type === 'error' ? '<i class="fas fa-exclamation-circle"></i>' : 
              type === 'warning' ? '<i class="fas fa-exclamation-triangle"></i>' : 
              '<i class="fas fa-info-circle"></i>'}
        </div>
        <div class="notification-content">
            ${message}
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    // Agregar estilos si no existen
    if (!document.getElementById('notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                max-width: 400px;
                padding: 15px;
                border-radius: 5px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                display: flex;
                align-items: flex-start;
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
            }
            
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            .notification.success {
                background-color: #d4edda;
                border-left: 5px solid #28a745;
                color: #155724;
            }
            
            .notification.error {
                background-color: #f8d7da;
                border-left: 5px solid #dc3545;
                color: #721c24;
            }
            
            .notification.warning {
                background-color: #fff3cd;
                border-left: 5px solid #ffc107;
                color: #856404;
            }
            
            .notification.info {
                background-color: #d1ecf1;
                border-left: 5px solid #17a2b8;
                color: #0c5460;
            }
            
            .notification-icon {
                margin-right: 15px;
                font-size: 24px;
            }
            
            .notification-content {
                flex: 1;
                white-space: pre-line;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                opacity: 0.7;
                transition: opacity 0.2s;
                padding: 0 5px;
                margin-left: 10px;
            }
            
            .notification-close:hover {
                opacity: 1;
            }
            
            .loading-indicator {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            
            .spinner {
                border: 5px solid #f3f3f3;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                animation: spin 1s linear infinite;
                margin-bottom: 15px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .loading-indicator p {
                color: white;
                font-size: 18px;
                text-align: center;
                max-width: 80%;
            }
            
            /* Estilos para el campo de folio */
            #folioDTE.last-folio {
                background-color: #fff3cd;
                border-color: #ffc107;
            }
            
            #folioDTE.new-folio {
                background-color: #d4edda;
                border-color: #28a745;
                transition: background-color 0.5s ease;
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Agregar al DOM
    document.body.appendChild(notification);
    
    // Configurar cierre automático
    const timeout = setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 8000);
    
    // Configurar botón de cierre
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        clearTimeout(timeout);
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    });
}

// Función para manejar el mensaje de límite de folios
function handleFolioLimitMessage(tipoDTE) {
    console.log(`Manejando mensaje de límite de folios para tipo DTE: ${tipoDTE}`);
    
    // Mostrar confirmación al usuario
    const confirmSolicitud = confirm(`
Se ha alcanzado el límite del rango de folios para el tipo de documento ${tipoDTE}.
¿Desea solicitar un nuevo rango de folios automáticamente?
    `);
    
    if (confirmSolicitud) {
        solicitarFoliosAutomaticamente(tipoDTE);
    } else {
        console.log('Usuario canceló la solicitud automática de folios');
        showNotification(`
            Recuerde solicitar un nuevo rango de folios manualmente 
            antes de generar más documentos de este tipo.
        `, 'warning');
    }
}

// Escuchar mensajes de la ventana modal
window.addEventListener('message', function(event) {
    // Verificar si el mensaje es sobre límite de folios
    if (event.data && event.data.action === 'folioLimitReached') {
        console.log('Mensaje recibido: límite de folios alcanzado', event.data);
        handleFolioLimitMessage(event.data.tipoDTE);
    }
});

// Función para detectar mensajes de límite de folios en el DOM
function detectFolioLimitMessage() {
    // Buscar mensajes de alerta en la página
    const alertMessages = document.querySelectorAll('.alert, .warning-message, .error-message');
    
    alertMessages.forEach(message => {
        const text = message.textContent || '';
        
        // Verificar si el mensaje contiene texto sobre límite de folios
        if (text.includes('límite del rango de folios') || 
            text.includes('límite de folios') || 
            text.includes('Se ha alcanzado el límite')) {
            
            console.log('Mensaje de límite de folios detectado:', text);
            
            // Extraer el tipo de documento del mensaje
            let tipoDTE = null;
            
            // Patrones comunes para extraer el tipo de documento
            const patterns = [
                /tipo de documento (\d+)/i,
                /documento tipo (\d+)/i,
                /tipo (\d+)/i
            ];
            
            // Intentar extraer el tipo de documento usando los patrones
            for (const pattern of patterns) {
                const match = text.match(pattern);
                if (match && match[1]) {
                    tipoDTE = match[1];
                    break;
                }
            }
            
            // Si no se pudo extraer, intentar buscar nombres comunes
            if (!tipoDTE) {
                if (text.includes('Factura') || text.includes('factura')) {
                    tipoDTE = '33';
                } else if (text.includes('Boleta') || text.includes('boleta')) {
                    tipoDTE = '39';
                } else if (text.includes('Nota de Crédito') || text.includes('nota de crédito')) {
                    tipoDTE = '61';
                } else if (text.includes('Nota de Débito') || text.includes('nota de débito')) {
                    tipoDTE = '56';
                }
            }
            
            // Si se encontró el tipo de documento, manejar el mensaje
            if (tipoDTE) {
                console.log(`Tipo de documento detectado: ${tipoDTE}`);
                
                // Marcar el mensaje como procesado para evitar duplicados
                if (!message.dataset.processed) {
                    message.dataset.processed = 'true';
                    handleFolioLimitMessage(tipoDTE);
                }
            } else {
                console.warn('No se pudo determinar el tipo de documento del mensaje:', text);
            }
        }
    });
}

// Ejecutar la detección cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, configurando detección de mensajes de límite de folios');
    
    // Ejecutar la detección inicial
    setTimeout(detectFolioLimitMessage, 1000);
    
    // Configurar un observador de mutaciones para detectar nuevos mensajes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Verificar si se agregaron nuevos nodos al DOM
                setTimeout(detectFolioLimitMessage, 500);
            }
        });
    });
    
    // Observar cambios en el cuerpo del documento
    observer.observe(document.body, { childList: true, subtree: true });
});

// Exponer funciones globalmente
window.solicitarFoliosAutomaticamente = solicitarFoliosAutomaticamente;
window.handleFolioLimitMessage = handleFolioLimitMessage;
