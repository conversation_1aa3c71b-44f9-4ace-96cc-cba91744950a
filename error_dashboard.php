<?php
// Panel simplificado de administración de errores
// Incluir verificación de autenticación
require_once 'auth_check.php';

// Configuración adicional de caché
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado

require_once 'config.php';
require_once 'db_connection.php';

// Función para formatear JSON para mostrar en la interfaz
function formatJsonDisplay($data) {
    if (is_array($data) || is_object($data)) {
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    return $data;
}

try {
    $conn = getConnection();
    
    // Obtener los últimos 50 errores
    $sql = "SELECT * FROM tb_error_logs ORDER BY fecha DESC LIMIT 50";
    $stmt = $conn->query($sql);
    $errores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "Error al consultar la base de datos: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Errores - TATA REPUESTOS</title>
    
    <!-- CSS externos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- CSS internos -->
    <link rel="stylesheet" href="styles/header.css?v=<?php echo time(); ?>">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-title {
            font-size: 24px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .error-card {
            margin-bottom: 15px;
            border-left: 4px solid #dc3545;
        }
        
        .error-header {
            display: flex;
            justify-content: space-between;
            padding: 15px;
            background-color: #f8f9fa;
        }
        
        .error-type {
            font-weight: bold;
            color: #dc3545;
        }
        
        .error-timestamp {
            color: #6c757d;
        }
        
        .error-message {
            padding: 15px;
            font-weight: 500;
        }
        
        .error-details {
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: none;
        }
        
        .json-preview {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Header principal con logo y navegación -->
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                Tata repuestos
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <div class="user-dropdown">
                        <a href="login.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="dashboard-container">
        <h1 class="page-title">
            <i class="fas fa-exclamation-triangle text-danger me-2"></i> Registro de Errores
        </h1>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>
        
        <div class="d-flex justify-content-between mb-4">
            <div>
                <button class="btn btn-primary refresh-button">
                    <i class="fas fa-sync-alt me-2"></i> Actualizar
                </button>
            </div>
            <div>
                <a href="sobres_envio.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Volver
                </a>
            </div>
        </div>
        
        <!-- Resumen -->
        <div class="alert alert-info mb-4">
            <div class="row">
                <div class="col-md-6">
                    <i class="fas fa-info-circle me-2"></i> Total de errores mostrados: <strong><?php echo count($errores); ?></strong>
                </div>
                <div class="col-md-6 text-end">
                    <i class="fas fa-clock me-2"></i> Última actualización: <strong><?php echo date('Y-m-d H:i:s'); ?></strong>
                </div>
            </div>
        </div>
        
        <!-- Lista de errores -->
        <?php if (empty($errores)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-circle me-2"></i> No se encontraron registros de errores.
            </div>
        <?php else: ?>
            <?php foreach ($errores as $error): ?>
                <div class="card error-card mb-3">
                    <div class="error-header">
                        <div class="error-type">
                            <i class="fas fa-exclamation-circle me-2"></i> <?php echo htmlspecialchars($error['tipo']); ?>
                        </div>
                        <div class="error-timestamp">
                            <i class="far fa-clock me-1"></i> <?php echo $error['fecha']; ?>
                            <span class="ms-2 badge bg-dark">ID: <?php echo $error['id']; ?></span>
                        </div>
                    </div>
                    <div class="error-message">
                        <?php echo htmlspecialchars($error['mensaje']); ?>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary toggle-details" data-id="<?php echo $error['id']; ?>">
                                <i class="fas fa-info-circle me-1"></i> Ver detalles
                            </button>
                        </div>
                    </div>
                    <div class="error-details" id="details-<?php echo $error['id']; ?>">
                        <?php 
                            $detalles = json_decode($error['detalles'], true);
                            if ($detalles): 
                        ?>
                            <div class="json-preview">
                                <pre><?php echo htmlspecialchars(formatJsonDisplay($detalles)); ?></pre>
                            </div>
                        <?php else: ?>
                            <p>No hay detalles adicionales disponibles.</p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Toggle detalles de error
            $('.toggle-details').click(function() {
                const id = $(this).data('id');
                $('#details-' + id).toggle();
                
                // Cambiar el texto del botón
                const buttonText = $(this).text().trim();
                if (buttonText.includes('Ver')) {
                    $(this).html('<i class="fas fa-minus-circle me-1"></i> Ocultar detalles');
                } else {
                    $(this).html('<i class="fas fa-info-circle me-1"></i> Ver detalles');
                }
            });
            
            // Recargar la página
            $('.refresh-button').click(function() {
                location.reload();
            });
        });
    </script>
</body>
</html>
