<?php
// Script para actualizar el estado de los errores
header('Content-Type: application/json');

// Incluir verificación de autenticación
require_once 'auth_check.php';
require_once 'db_connection.php';

// Validar los parámetros requeridos
if (!isset($_POST['error_id']) || !isset($_POST['action'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Faltan parámetros requeridos'
    ]);
    exit;
}

$error_id = (int)$_POST['error_id'];
$action = $_POST['action'];
$notes = isset($_POST['notes']) ? $_POST['notes'] : '';

// Validar la acción
$valid_actions = ['start_progress', 'resolve', 'ignore', 'reopen'];
if (!in_array($action, $valid_actions)) {
    echo json_encode([
        'success' => false,
        'message' => 'Acción no válida'
    ]);
    exit;
}

try {
    $conn = getConnection();
    
    // Mapear la acción al estado correspondiente
    $status_map = [
        'start_progress' => 'in_progress',
        'resolve' => 'resolved',
        'ignore' => 'ignored',
        'reopen' => 'pending'
    ];
    
    $new_status = $status_map[$action];
    
    // Actualizar el estado del error
    $stmt = $conn->prepare("
        UPDATE tb_error_logs 
        SET resolution_status = :status, 
            resolution_notes = :notes,
            resolved_by = :user,
            resolved_at = NOW()
        WHERE id = :id
    ");
    
    $username = isset($_SESSION['username']) ? $_SESSION['username'] : 'system';
    
    $stmt->bindParam(':id', $error_id);
    $stmt->bindParam(':status', $new_status);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':user', $username);
    
    $result = $stmt->execute();
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Estado actualizado correctamente',
            'new_status' => $new_status
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Error al actualizar el estado'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
