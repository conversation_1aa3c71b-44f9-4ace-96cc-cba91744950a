<?php
/**
 * Función para procesar la respuesta XML de la API, guardarla como archivo
 * y registrarla en la base de datos MySQL
 */
function procesarRespuestaXML($xmlContent, $jsonData) {
    // Crear directorio si no existe
    $directorio = 'Documents/Facturas/';
    if (!file_exists($directorio)) {
        $result = mkdir($directorio, 0755, true);
        if (!$result) {
            $errorMsg = "Error al crear el directorio: " . $directorio;
            error_log($errorMsg);
            return [
                'success' => false,
                'mensaje' => $errorMsg,
                'error_type' => 'directory_creation_error'
            ];
        }
    }

    // Extraer información del XML para el nombre del archivo
    try {
        $xml = new SimpleXMLElement($xmlContent);

        // Obtener el tipo de documento y folio
        $tipoDTE = '';
        $folio = '';

        if (isset($xml->Documento->Encabezado->IdDoc->TipoDTE)) {
            $tipoDTE = (string)$xml->Documento->Encabezado->IdDoc->TipoDTE;
        } else {
            error_log("El XML no contiene el elemento TipoDTE esperado");
        }

        if (isset($xml->Documento->Encabezado->IdDoc->Folio)) {
            $folio = (string)$xml->Documento->Encabezado->IdDoc->Folio;
        } else {
            error_log("El XML no contiene el elemento Folio esperado");
        }
    } catch (Exception $e) {
        $errorMsg = "Error al procesar el XML: " . $e->getMessage();
        error_log($errorMsg);
        return [
            'success' => false,
            'mensaje' => $errorMsg,
            'xml_content' => substr($xmlContent, 0, 500), // Muestra los primeros 500 caracteres del XML
            'error_type' => 'xml_parsing_error'
        ];
    }

    // Generar nombre de archivo
    $fecha = date('Ymd_His');
    $nombreArchivo = "factura_DTE_{$tipoDTE}_{$folio}_{$fecha}.xml";
    $rutaCompleta = $directorio . $nombreArchivo;

    // Guardar el archivo XML
    $bytesWritten = file_put_contents($rutaCompleta, $xmlContent);

    if ($bytesWritten === false) {
        $errorMsg = "Error al guardar el archivo XML en la ruta: " . $rutaCompleta;
        error_log($errorMsg);

        // Comprobar permisos de escritura
        $permisos = decoct(fileperms($directorio) & 0777);
        $usuarioServidor = posix_getpwuid(posix_geteuid())['name'] ?? 'desconocido';

        return [
            'success' => false,
            'mensaje' => $errorMsg,
            'detalles' => [
                'directorio' => $directorio,
                'permisos_directorio' => $permisos,
                'usuario_servidor' => $usuarioServidor,
                'is_writable' => is_writable($directorio) ? 'true' : 'false',
                'disk_free_space' => disk_free_space('/') // Espacio libre en disco
            ],
            'error_type' => 'file_writing_error'
        ];
    } else {
        // Verificar que el archivo fue creado
        if (!file_exists($rutaCompleta)) {
            $errorMsg = "El archivo no existe después de intentar escribirlo: " . $rutaCompleta;
            error_log($errorMsg);
            return [
                'success' => false,
                'mensaje' => $errorMsg,
                'error_type' => 'file_missing_error'
            ];
        }

        // Registro en la base de datos
        $dbResult = registrarEnBaseDatos($nombreArchivo, $tipoDTE, $folio, $jsonData);

        if ($dbResult === false) {
            $errorMsg = "Error al registrar en la base de datos";
            return [
                'success' => false,
                'mensaje' => $errorMsg,
                'archivo_guardado' => true,
                'ruta_archivo' => $rutaCompleta,
                'error_type' => 'database_error'
            ];
        }

        return [
            'success' => true,
            'archivo' => $nombreArchivo,
            'ruta' => $rutaCompleta,
            'bytes_escritos' => $bytesWritten,
            'id_en_db' => $dbResult
        ];
    }
}

/**
 * Función para registrar la información en la base de datos
 */
function registrarEnBaseDatos($nombreArchivo, $tipoDTE, $folio, $jsonData) {
    require_once 'db_connection.php';

    try {
        $conn = getConnection();

        // Preparar la consulta
        $sql = "INSERT INTO facturas_dte (
                    nombre_archivo,
                    tipo_dte,
                    folio,
                    json_enviado,
                    fecha_generacion,
                    enviado
                ) VALUES (
                    :nombre_archivo,
                    :tipo_dte,
                    :folio,
                    :json_enviado,
                    NOW(),
                    1
                )";

        $stmt = $conn->prepare($sql);

        // Bind de parámetros
        $stmt->bindParam(':nombre_archivo', $nombreArchivo);
        $stmt->bindParam(':tipo_dte', $tipoDTE);
        $stmt->bindParam(':folio', $folio);
        $stmt->bindParam(':json_enviado', $jsonData);

        // Ejecutar la consulta
        $result = $stmt->execute();

        if (!$result) {
            error_log("Error en la ejecución del SQL: " . implode(", ", $stmt->errorInfo()));
            return false;
        }

        return $conn->lastInsertId();
    } catch (PDOException $e) {
        error_log("Error al registrar en la base de datos: " . $e->getMessage());
        return false;
    }
}

/**
 * Procesar la solicitud POST para enviar el DTE a la API y guardar la respuesta
 */
function enviarDTEYGuardar() {
    // Activar el registro de errores para depuración
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    error_log("Iniciando procesamiento de DTE");

    // Crear un log de diagnóstico
    $diagnostico = [
        'timestamp' => date('Y-m-d H:i:s'),
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'steps' => [],
    ];

    // Verificar que sea una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $diagnostico['steps'][] = ['step' => 'method_check', 'status' => 'error', 'message' => 'Método no permitido'];
        return json_encode(['error' => 'Método no permitido', 'diagnostico' => $diagnostico]);
    }

    $diagnostico['steps'][] = ['step' => 'method_check', 'status' => 'success'];

    // Obtener el JSON y los paths de los archivos
    $jsonData = $_POST['jsonData'] ?? '';
    $certificadoPath = $_POST['certificadoPath'] ?? 'Documents/17365958-K.pfx';
    $foliosPath = $_POST['foliosPath'] ?? 'Documents/folios/Facturas/folios_facturas_10';
    $apiKey = $_POST['apiKey'] ?? '2037-N680-6391-2493-5987';

    $diagnostico['steps'][] = [
        'step' => 'get_input',
        'status' => 'info',
        'certificado_path' => $certificadoPath,
        'folios_path' => $foliosPath,
        'json_length' => strlen($jsonData)
    ];

    if (empty($jsonData)) {
        $diagnostico['steps'][] = ['step' => 'validate_json', 'status' => 'error', 'message' => 'Datos JSON no proporcionados'];
        return json_encode(['error' => 'Datos JSON no proporcionados', 'diagnostico' => $diagnostico]);
    }

    // Validar formato JSON
    $decodedJson = json_decode($jsonData);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $diagnostico['steps'][] = [
            'step' => 'validate_json',
            'status' => 'error',
            'message' => 'JSON inválido: ' . json_last_error_msg(),
            'json_sample' => substr($jsonData, 0, 100)
        ];
        return json_encode([
            'error' => 'JSON inválido: ' . json_last_error_msg(),
            'diagnostico' => $diagnostico
        ]);
    }

    $diagnostico['steps'][] = ['step' => 'validate_json', 'status' => 'success'];

    // Verificar que los archivos existan
    if (!file_exists($certificadoPath)) {
        $diagnostico['steps'][] = [
            'step' => 'check_files',
            'status' => 'error',
            'message' => 'Archivo de certificado no encontrado',
            'path' => $certificadoPath,
            'current_dir' => getcwd()
        ];
        return json_encode([
            'error' => 'Archivo de certificado no encontrado: ' . $certificadoPath,
            'diagnostico' => $diagnostico
        ]);
    }

    if (!file_exists($foliosPath)) {
        $diagnostico['steps'][] = [
            'step' => 'check_files',
            'status' => 'error',
            'message' => 'Archivo de folios no encontrado',
            'path' => $foliosPath,
            'current_dir' => getcwd()
        ];
        return json_encode([
            'error' => 'Archivo de folios no encontrado: ' . $foliosPath,
            'diagnostico' => $diagnostico
        ]);
    }

    $diagnostico['steps'][] = [
        'step' => 'check_files',
        'status' => 'success',
        'certificado_size' => filesize($certificadoPath),
        'folios_size' => filesize($foliosPath)
    ];

    // Crear un límite para los datos multipart
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;

    // Preparar el cuerpo de la solicitud multipart/form-data
    $postData = '';

    // Agregar el input JSON
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonData . "\r\n";

    // Agregar el archivo de certificado
    $fileContents = file_get_contents($certificadoPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Agregar el archivo de folios
    $fileContents = file_get_contents($foliosPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($foliosPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Cerrar el cuerpo del mensaje
    $postData .= "--" . $delimiter . "--\r\n";

    $diagnostico['steps'][] = [
        'step' => 'prepare_request',
        'status' => 'success',
        'multipart_length' => strlen($postData)
    ];

    // Configurar cURL para enviar la solicitud con opciones de depuración
    $ch = curl_init('https://api.simpleapi.cl/api/v1/dte/generar');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: multipart/form-data; boundary=' . $delimiter,
        'Authorization: ' . $apiKey,
        'Content-Length: ' . strlen($postData)
    ]);

    // Opciones adicionales para depuración
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    curl_setopt($ch, CURLINFO_HEADER_OUT, true);
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    // Ejecutar la solicitud
    $response = curl_exec($ch);
    $curlError = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $requestHeaders = curl_getinfo($ch, CURLINFO_HEADER_OUT);

    // Obtener información de depuración
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);

    // Guardar detalles curl para diagnóstico
    $diagnostico['steps'][] = [
        'step' => 'curl_request',
        'status' => $curlError ? 'error' : 'success',
        'http_code' => $httpCode,
        'curl_error' => $curlError,
        'response_size' => $response ? strlen($response) : 0,
        'verbose_log' => $verboseLog
    ];

    curl_close($ch);

    // Verificar si la solicitud fue exitosa
    if ($httpCode == 200) {
        // Usar directamente la respuesta de la API como contenido XML
        $xmlContent = $response;

        // Verificar que la respuesta es un XML válido
        $isValidXml = simplexml_load_string($xmlContent) !== false;

        $diagnostico['steps'][] = [
            'step' => 'validate_response',
            'status' => $isValidXml ? 'success' : 'error',
            'is_valid_xml' => $isValidXml,
            'response_sample' => substr($response, 0, 500) // Primeros 500 caracteres
        ];

        if (!$isValidXml) {
            return json_encode([
                'error' => 'La respuesta de la API no es un XML válido',
                'response_sample' => substr($response, 0, 500),
                'diagnostico' => $diagnostico
            ]);
        }

        // Procesar la respuesta XML sin alterarla
        $resultado = procesarRespuestaXML($xmlContent, $jsonData);

        $diagnostico['steps'][] = [
            'step' => 'process_xml',
            'status' => $resultado['success'] ? 'success' : 'error',
            'result' => $resultado
        ];

        if ($resultado['success']) {
            // Verificar integridad - asegurarse que el contenido guardado sea idéntico
            if (file_exists($resultado['ruta'])) {
                $savedContent = file_get_contents($resultado['ruta']);
                $integrityCheck = $savedContent === $xmlContent;

                $diagnostico['steps'][] = [
                    'step' => 'integrity_check',
                    'status' => $integrityCheck ? 'success' : 'error',
                    'file_exists' => true,
                    'content_match' => $integrityCheck
                ];

                if (!$integrityCheck) {
                    return json_encode([
                        'error' => 'Error de integridad: El contenido guardado no coincide con la respuesta original',
                        'diagnostico' => $diagnostico
                    ]);
                }
            } else {
                $diagnostico['steps'][] = [
                    'step' => 'integrity_check',
                    'status' => 'error',
                    'message' => 'El archivo guardado no existe',
                    'ruta' => $resultado['ruta']
                ];

                return json_encode([
                    'error' => 'Error de integridad: El archivo guardado no existe en la ruta esperada',
                    'ruta' => $resultado['ruta'],
                    'diagnostico' => $diagnostico
                ]);
            }

            return json_encode([
                'success' => true,
                'mensaje' => 'DTE procesado y guardado correctamente',
                'archivo' => $resultado['archivo'],
                'ruta' => $resultado['ruta'],
                'diagnostico' => $diagnostico
            ]);
        } else {
            return json_encode([
                'error' => $resultado['mensaje'],
                'detalles' => $resultado,
                'diagnostico' => $diagnostico
            ]);
        }
    } else {
        $diagnostico['steps'][] = [
            'step' => 'http_response',
            'status' => 'error',
            'http_code' => $httpCode,
            'response_sample' => substr($response, 0, 500)
        ];

        return json_encode([
            'error' => "Error al enviar el DTE. Código HTTP: {$httpCode}",
            'api_response' => $response,
            'diagnostico' => $diagnostico
        ]);
    }
}

// Si este archivo se llama directamente, procesar la solicitud
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('Content-Type: application/json');
    echo enviarDTEYGuardar();
}