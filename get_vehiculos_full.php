<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    $conn = getConnection();
    $query = "SELECT distinct 
        vc.id,
        vc.modelo_id,
        vc.marca_id,
        vc.anio,
        vc.cilindrada,
        vc.version,
        vc.tipo_motor,
        vc.combustible,
        m.nombre as modelo_nombre,
        ma.nombre as marca_nombre
    FROM vehiculo_compatible vc
    LEFT JOIN modelo m ON vc.modelo_id = m.id
    LEFT JOIN marca ma ON vc.marca_id = ma.id
    ORDER BY ma.nombre, m.nombre, vc.anio";
    
    $stmt = $conn->query($query);
    echo json_encode($stmt->fetchAll(PDO::FETCH_ASSOC));
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
