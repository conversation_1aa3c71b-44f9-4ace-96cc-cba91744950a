*   Trying *************:443...
* TCP_NODELAY set
* Connected to api.simpleapi.cl (*************) port 443 (#0)
* ALPN, offering h2
* ALPN, offering http/1.1
* successfully set certificate verify locations:
*   CAfile: /etc/ssl/certs/ca-certificates.crt
  CApath: /etc/ssl/certs
* SSL connection using TLSv1.2 / ECDHE-RSA-AES256-GCM-SHA384
* ALPN, server accepted to use h2
* Server certificate:
*  subject: CN=*.simpleapi.cl
*  start date: Dec 26 00:00:00 2024 GMT
*  expire date: Jan 26 23:59:59 2026 GMT
*  subjectAltName: host "api.simpleapi.cl" matched cert's "*.simpleapi.cl"
*  issuer: C=US; O=DigiCert Inc; OU=www.digicert.com; CN=Thawte TLS RSA CA G1
*  SSL certificate verify ok.
* Using HTTP2, server supports multi-use
* Connection state changed (HTTP/2 confirmed)
* Copying HTTP/2 data in stream buffer to connection buffer after upgrade: len=0
* Using Stream ID: 1 (easy handle 0x56423ecbe940)
> POST /api/v1/envio/enviar HTTP/2
Host: api.simpleapi.cl
accept: */*
content-type: application/x-www-form-urlencoded

* Connection state changed (MAX_CONCURRENT_STREAMS == 100)!
< HTTP/2 401 
< server: Microsoft-IIS/10.0
< x-rate-limit-limit: 1m
< x-rate-limit-remaining: 39
< x-rate-limit-reset: 2025-04-08T03:03:30.7555657Z
< x-powered-by: ASP.NET
< date: Tue, 08 Apr 2025 03:02:30 GMT
* HTTP error before end of send, stop sending
< 
* Connection #0 to host api.simpleapi.cl left intact
