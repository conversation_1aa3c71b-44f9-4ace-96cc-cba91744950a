document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-icon');
    const resetButton = document.getElementById('reset-filters');

    // Variables para almacenar las referencias a las vistas
    const gridView = document.querySelector('.products-grid');
    const tableView = document.querySelector('.products-table');

    function filterProducts() {
        const searchTerm = searchInput.value.toLowerCase();
        const marcaFilter = document.getElementById('marca_filter').value.toLowerCase();
        const modeloFilter = document.getElementById('modelo_filter').value.toLowerCase();
        const anioFilter = document.getElementById('anio').value.toLowerCase();

        // Filtrar tarjetas
        const cards = document.querySelectorAll('.product-card');
        let foundInCards = false;

        cards.forEach(card => {
            const sku = card.querySelector('.product-sku')?.textContent.toLowerCase() || '';
            const name = card.querySelector('.product-name')?.textContent.toLowerCase() || '';
            const brand = card.querySelector('.product-brand')?.textContent.toLowerCase() || '';
            const model = card.querySelector('.product-model')?.textContent.toLowerCase() || '';

            const matchesSearch = !searchTerm ||
                sku.includes(searchTerm) ||
                name.includes(searchTerm) ||
                brand.includes(searchTerm) ||
                model.includes(searchTerm);

            const matchesMarca = !marcaFilter || brand === marcaFilter;
            const matchesModelo = !modeloFilter || model === modeloFilter;

            if (matchesSearch && matchesMarca && matchesModelo) {
                card.style.display = 'flex';
                foundInCards = true;
            } else {
                card.style.display = 'none';
            }
        });

        // Filtrar tabla
        const tableRows = document.querySelectorAll('.products-table tbody tr');
        let foundInTable = false;

        tableRows.forEach(row => {
            const cells = Array.from(row.cells);
            const rowText = cells.map(cell => cell.textContent.toLowerCase()).join(' ');
            const brand = cells[2]?.textContent.toLowerCase() || ''; // Ajusta el índice según tu estructura
            const model = cells[3]?.textContent.toLowerCase() || ''; // Ajusta el índice según tu estructura

            const matchesSearch = !searchTerm || rowText.includes(searchTerm);
            const matchesMarca = !marcaFilter || brand === marcaFilter;
            const matchesModelo = !modeloFilter || model === modeloFilter;

            if (matchesSearch && matchesMarca && matchesModelo) {
                row.style.display = '';
                foundInTable = true;
            } else {
                row.style.display = 'none';
            }
        });

        // Mostrar mensaje de no resultados si es necesario
        showNoResultsMessage(!foundInCards, !foundInTable);
    }

    function showNoResultsMessage(noCardsFound, noTableFound) {
        const noResultsTemplate = `
            <div class="no-results-message">
                <i class="fas fa-search"></i>
                <p>No se encontraron productos que coincidan con tu búsqueda</p>
            </div>
        `;

        // Mostrar mensaje en la vista de tarjetas si no hay resultados
        if (noCardsFound) {
            const gridContainer = document.querySelector('.products-grid');
            if (gridContainer) {
                gridContainer.innerHTML = noResultsTemplate;
            }
        }

        // Mostrar mensaje en la vista de tabla si no hay resultados
        if (noTableFound) {
            const tableBody = document.querySelector('.products-table tbody');
            if (tableBody) {
                const messageRow = document.createElement('tr');
                messageRow.innerHTML = `
                    <td colspan="100%" class="no-results-cell">
                        ${noResultsTemplate}
                    </td>
                `;
                tableBody.innerHTML = '';
                tableBody.appendChild(messageRow);
            }
        }
    }

    function resetFilters() {
        // Limpiar todos los filtros
        searchInput.value = '';
        document.getElementById('marca_filter').value = '';
        document.getElementById('modelo_filter').value = '';
        document.getElementById('anio').value = '';

        // Obtener la vista actual
        const currentView = document.body.classList.contains('grid-view') ? 'grid' : 'table';

        // Restablecer tarjetas
        document.querySelectorAll('.product-card').forEach(card => {
            // Eliminar cualquier estilo inline
            card.removeAttribute('style');
            // Eliminar atributos de filtro
            card.removeAttribute('data-matches-filter');
        });

        // Restablecer filas de tabla
        document.querySelectorAll('.products-table tbody tr').forEach(row => {
            // Eliminar cualquier estilo inline
            row.removeAttribute('style');
            // Eliminar atributos de filtro
            row.removeAttribute('data-matches-filter');
        });

        // Limpiar mensajes de no resultados
        const noResultsMessages = document.querySelectorAll('.no-results-message');
        noResultsMessages.forEach(msg => msg.remove());

        // Forzar actualización de la vista actual
        updateViewVisibility(currentView);
    }

    function updateViewVisibility(viewType) {
        const gridView = document.querySelector('.products-grid');
        const tableView = document.querySelector('.products-table');

        if (viewType === 'grid') {
            document.body.classList.remove('table-view');
            document.body.classList.add('grid-view');
            
            // Asegurar que las tarjetas visibles tengan display flex
            document.querySelectorAll('.product-card').forEach(card => {
                if (!card.hasAttribute('data-matches-filter') || 
                    card.getAttribute('data-matches-filter') === 'true') {
                    card.style.display = 'flex';
                }
            });
        } else {
            document.body.classList.remove('grid-view');
            document.body.classList.add('table-view');
            
            // Asegurar que las filas visibles tengan display table-row
            document.querySelectorAll('.products-table tbody tr').forEach(row => {
                if (!row.hasAttribute('data-matches-filter') || 
                    row.getAttribute('data-matches-filter') === 'true') {
                    row.style.display = 'table-row';
                }
            });
        }
    }

    // Event Listeners
    searchButton.addEventListener('click', filterProducts);
    searchInput.addEventListener('keyup', filterProducts);
    resetButton.addEventListener('click', resetFilters);

    // Event listeners para los filtros dropdown
    ['marca_filter', 'modelo_filter', 'anio'].forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', filterProducts);
        }
    });

    // Event listener para los botones de cambio de vista
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const viewType = btn.dataset.view;
            updateViewVisibility(viewType);
        });
    });
});
