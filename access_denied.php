<?php
session_start();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso Denegado - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #3498db;
            --hover-color: #e67e22;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), #34495e);
            padding: 20px;
        }

        .access-denied-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .icon-container {
            margin-bottom: 1.5rem;
        }

        .icon-container i {
            font-size: 5rem;
            color: var(--secondary-color);
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        p {
            color: #555;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
            text-decoration: none;
            margin: 0 0.5rem;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--hover-color);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .user-info {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #777;
        }

        .user-info p {
            margin-bottom: 0.5rem;
        }

        .user-info strong {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="icon-container">
            <i class="fas fa-lock"></i>
        </div>
        <h1>Acceso Denegado</h1>
        <p>Lo sentimos, no tiene permisos para acceder a esta página. Esta sección requiere privilegios adicionales.</p>
        
        <div>
            <a href="index.php" class="btn btn-primary">Ir al inicio</a>
            <a href="login.php" class="btn btn-secondary">Cerrar sesión</a>
        </div>
        
        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
        <div class="user-info">
            <p>Usuario actual: <strong><?php echo htmlspecialchars($_SESSION['nombre'] ?? $_SESSION['username']); ?></strong></p>
            <p>Rol: <strong><?php echo htmlspecialchars($_SESSION['rol'] ?? 'No definido'); ?></strong></p>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
