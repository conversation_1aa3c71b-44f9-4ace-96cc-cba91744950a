<?php
// No usar session_start() ni hacer comprobaciones de autenticación aquí
// La autenticación se maneja en la página principal

// Define los directorios permitidos
$allowedDirs = [
    'Documents/DTE/',         // Para documentos tributarios XML
    'Documents/sobreEnvio/',  // Para sobres de envío
    'Documents/PDF_88/'       // Para PDFs generados
];

$file = $_GET['file'] ?? '';

// Validar que el archivo solicitado esté en un directorio permitido
$isAllowed = false;
foreach ($allowedDirs as $dir) {
    if (strpos($file, $dir) === 0) {
        $isAllowed = true;
        break;
    }
}

if (!$isAllowed) {
    echo 'Acceso denegado';
    exit;
}

// Verificar que el archivo existe
if (!file_exists($file)) {
    echo 'Archivo no encontrado';
    exit;
}

// Leer el contenido del PDF
$content = file_get_contents($file);

// Convertir a base64
$base64 = base64_encode($content);

// Enviar el contenido base64 directamente, sin HTML envolvente
echo $base64;