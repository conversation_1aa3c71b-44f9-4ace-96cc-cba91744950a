<?php
header('Content-Type: application/json');

$certificadoPath = 'Documents/17365958-K.pfx';
$foliosPath = 'Documents/folios/Facturas/folios_facturas_10';

$response = array();

try {
    // Verificar si los archivos existen
    if (!file_exists($certificadoPath)) {
        throw new Exception("Archivo de certificado no encontrado: " . $certificadoPath);
    }
    
    if (!file_exists($foliosPath)) {
        throw new Exception("Archivo de folios no encontrado: " . $foliosPath);
    }
    
    // Leer los archivos
    $certificadoFile = file_get_contents($certificadoPath);
    $foliosFile = file_get_contents($foliosPath);
    
    // Convertir a base64 para transmisión
    $response['certificadoFile'] = base64_encode($certificadoFile);
    $response['foliosFile'] = base64_encode($foliosFile);
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>