/**
 * dashboard.js - Script para el dashboard de ventas
 *
 * Este archivo maneja la carga de datos y renderización de gráficos
 * para el dashboard de ventas en la pestaña "Resumen".
 */

// Variables globales
let chartVentasDiarias = null;
let chartComparativaMensual = null;
let chartTendenciaAnual = null;
let chartEstacionalidad = null;
let chartDistribucionDTE = null;
let periodoActual = 'mes'; // Valor por defecto

// Colores para los gráficos
const colores = {
    principal: '#34495e',
    secundario: '#2980b9',
    terciario: '#27ae60',
    alerta: '#e74c3c',
    neutro: '#95a5a6',
    gradiente: ['rgba(52, 73, 94, 0.8)', 'rgba(52, 73, 94, 0.2)']
};

// Nombres de los meses
const nombresMeses = [
    'Enero', 'Febrero', '<PERSON><PERSON>', 'Abri<PERSON>', '<PERSON>', '<PERSON><PERSON>',
    '<PERSON>', 'Agos<PERSON>', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

// Inicialización cuando el DOM está listo
document.addEventListener('DOMContentLoaded', function() {
    // Verificar si estamos en la página correcta
    if (!document.getElementById('dashboard-container')) {
        return;
    }

    // Inicializar selectores de período
    const selectorPeriodo = document.getElementById('periodo-selector');
    if (selectorPeriodo) {
        selectorPeriodo.addEventListener('change', function() {
            periodoActual = this.value;
            cargarDashboard(periodoActual);
        });
    }

    // Cargar el dashboard con el período por defecto
    cargarDashboard(periodoActual);
});

/**
 * Carga todos los componentes del dashboard
 * @param {string} periodo - Período de tiempo (dia, mes, anio)
 */
function cargarDashboard(periodo) {
    cargarKPIs(periodo);
    cargarGraficoVentasDiarias(periodo);
    cargarGraficoComparativaMensual();
    cargarGraficoTendenciaAnual();
    cargarGraficoEstacionalidad();
    cargarGraficoDistribucionDTE(periodo);
}

/**
 * Carga los KPIs principales
 * @param {string} periodo - Período de tiempo (dia, mes, anio)
 */
function cargarKPIs(periodo) {
    fetch(`get_dashboard_data.php?tipo=kpis&periodo=${periodo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                actualizarKPIs(data.data, periodo);
            } else {
                console.error('Error al cargar KPIs:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de KPIs:', error);
        });
}

/**
 * Actualiza los elementos HTML de los KPIs con los datos recibidos
 * @param {Object} kpis - Datos de KPIs
 * @param {string} periodo - Período actual
 */
function actualizarKPIs(kpis, periodo) {
    // Formatear valores para mostrar
    const ventaTotal = new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP' }).format(kpis.venta_total);
    const ticketPromedio = new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP' }).format(kpis.ticket_promedio);
    const metaMensual = new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP' }).format(kpis.meta_mensual);

    // Actualizar textos de KPIs
    document.getElementById('kpi-venta-total').textContent = ventaTotal;
    document.getElementById('kpi-num-transacciones').textContent = kpis.num_transacciones;
    document.getElementById('kpi-ticket-promedio').textContent = ticketPromedio;

    // Formatear valores de referencia
    const ventaTotalAnterior = new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP' }).format(kpis.venta_total_anterior || 0);
    const numTransaccionesAnterior = kpis.num_transacciones_anterior || 0;

    // Actualizar variaciones con colores según valor y mostrar valores de referencia
    const ventaVariacionElement = document.getElementById('kpi-venta-variacion');
    ventaVariacionElement.textContent = `${kpis.variacion_venta > 0 ? '+' : ''}${kpis.variacion_venta}%`;
    ventaVariacionElement.className = kpis.variacion_venta >= 0 ? 'variacion positiva' : 'variacion negativa';

    const ventaReferenciaElement = document.getElementById('kpi-venta-referencia');
    if (ventaReferenciaElement) {
        ventaReferenciaElement.textContent = ventaTotalAnterior;
    } else {
        const referenciaElement = document.createElement('div');
        referenciaElement.id = 'kpi-venta-referencia';
        referenciaElement.className = 'kpi-referencia';
        referenciaElement.textContent = ventaTotalAnterior;
        document.getElementById('kpi-venta-variacion').insertAdjacentElement('afterend', referenciaElement);
    }

    const transaccionesVariacionElement = document.getElementById('kpi-transacciones-variacion');
    transaccionesVariacionElement.textContent = `${kpis.variacion_transacciones > 0 ? '+' : ''}${kpis.variacion_transacciones}%`;
    transaccionesVariacionElement.className = kpis.variacion_transacciones >= 0 ? 'variacion positiva' : 'variacion negativa';

    const transaccionesReferenciaElement = document.getElementById('kpi-transacciones-referencia');
    if (transaccionesReferenciaElement) {
        transaccionesReferenciaElement.textContent = numTransaccionesAnterior;
    } else {
        const referenciaElement = document.createElement('div');
        referenciaElement.id = 'kpi-transacciones-referencia';
        referenciaElement.className = 'kpi-referencia';
        referenciaElement.textContent = numTransaccionesAnterior;
        document.getElementById('kpi-transacciones-variacion').insertAdjacentElement('afterend', referenciaElement);
    }

    // Actualizar barra de progreso de meta si existe el elemento
    const progresoMetaElement = document.getElementById('kpi-meta-progreso');
    if (progresoMetaElement) {
        progresoMetaElement.style.width = `${Math.min(kpis.progreso_meta, 100)}%`;
        progresoMetaElement.textContent = `${kpis.progreso_meta}%`;
    }

    // Actualizar texto de meta si existe el elemento
    const metaValorElement = document.getElementById('kpi-meta-valor');
    if (metaValorElement) {
        metaValorElement.textContent = metaMensual;
    }

    // Actualizar título del período
    let tituloPeriodo = '';
    switch (periodo) {
        case 'dia':
            tituloPeriodo = 'Hoy';
            break;
        case 'semana':
            tituloPeriodo = 'Últimos 7 días';
            break;
        case 'mes':
            tituloPeriodo = 'Este Mes';
            break;
        case 'anio':
            tituloPeriodo = 'Este Año';
            break;
    }
    document.getElementById('titulo-periodo').textContent = tituloPeriodo;
}

/**
 * Carga y renderiza el gráfico de ventas diarias
 * @param {string} periodo - Período de tiempo (dia, mes, anio)
 */
function cargarGraficoVentasDiarias(periodo) {
    console.log('Cargando datos de ventas diarias para periodo:', periodo);
    fetch(`get_dashboard_data.php?tipo=ventas_diarias&periodo=${periodo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Datos de ventas diarias recibidos:', data.data);
                renderizarGraficoVentasDiarias(data.data, periodo);
            } else {
                console.error('Error al cargar datos de ventas diarias:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de ventas diarias:', error);
        });
}

/**
 * Renderiza el gráfico de ventas diarias
 * @param {Array} datos - Datos de ventas diarias
 * @param {string} periodo - Período actual
 */
function renderizarGraficoVentasDiarias(datos, periodo) {
    const ctx = document.getElementById('grafico-ventas-diarias').getContext('2d');

    // Preparar datos para el gráfico
    const labels = datos.map(item => {
        // Convertir la fecha a objeto Date y ajustar para la zona horaria local
        const fecha = new Date(item.fecha + 'T00:00:00');
        console.log('Fecha original:', item.fecha, 'Fecha procesada:', fecha);
        return fecha.toLocaleDateString('es-CL', { day: '2-digit', month: 'short' });
    });

    const valores = datos.map(item => parseInt(item.venta_total));

    // Crear o actualizar el gráfico
    if (chartVentasDiarias) {
        chartVentasDiarias.data.labels = labels;
        chartVentasDiarias.data.datasets[0].data = valores;
        chartVentasDiarias.update();
    } else {
        // Crear gradiente
        const gradiente = ctx.createLinearGradient(0, 0, 0, 400);
        gradiente.addColorStop(0, colores.gradiente[0]);
        gradiente.addColorStop(1, colores.gradiente[1]);

        chartVentasDiarias = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Ventas Diarias',
                    data: valores,
                    backgroundColor: gradiente,
                    borderColor: colores.principal,
                    borderWidth: 2,
                    pointBackgroundColor: colores.principal,
                    pointRadius: 4,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return new Intl.NumberFormat('es-CL', {
                                    style: 'currency',
                                    currency: 'CLP'
                                }).format(context.raw);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return '$' + (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return '$' + (value / 1000).toFixed(0) + 'K';
                                }
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
    }

    // Actualizar título del gráfico según el período
    let tituloGrafico = '';
    switch (periodo) {
        case 'dia':
            tituloGrafico = 'Ventas de Hoy';
            break;
        case 'semana':
            tituloGrafico = 'Ventas de los últimos 7 días';
            break;
        case 'mes':
            tituloGrafico = 'Ventas Diarias del Mes';
            break;
        case 'anio':
            tituloGrafico = 'Ventas Diarias del Año';
            break;
    }
    document.getElementById('titulo-ventas-diarias').textContent = tituloGrafico;
}

/**
 * Carga y renderiza el gráfico de comparativa mensual
 */
function cargarGraficoComparativaMensual() {
    fetch('get_dashboard_data.php?tipo=comparativa_mensual')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderizarGraficoComparativaMensual(data.data);
            } else {
                console.error('Error al cargar datos de comparativa mensual:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de comparativa mensual:', error);
        });
}

/**
 * Renderiza el gráfico de comparativa mensual
 * @param {Object} datos - Datos de comparativa mensual
 */
function renderizarGraficoComparativaMensual(datos) {
    const ctx = document.getElementById('grafico-comparativa-mensual').getContext('2d');

    // Preparar datos para el gráfico
    const meses = Array.from({length: 12}, (_, i) => i + 1);

    // Mapear datos del año actual
    const datosActual = meses.map(mes => {
        const encontrado = datos.anio_actual.find(item => parseInt(item.mes) === mes);
        return encontrado ? parseInt(encontrado.venta_total) : 0;
    });

    // Mapear datos del año anterior
    const datosAnterior = meses.map(mes => {
        const encontrado = datos.anio_anterior.find(item => parseInt(item.mes) === mes);
        return encontrado ? parseInt(encontrado.venta_total) : 0;
    });

    // Crear o actualizar el gráfico
    if (chartComparativaMensual) {
        chartComparativaMensual.data.datasets[0].data = datosActual;
        chartComparativaMensual.data.datasets[1].data = datosAnterior;
        chartComparativaMensual.update();
    } else {
        chartComparativaMensual = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: nombresMeses,
                datasets: [
                    {
                        label: `${datos.anio_actual_valor}`,
                        data: datosActual,
                        backgroundColor: colores.principal,
                        borderColor: colores.principal,
                        borderWidth: 1
                    },
                    {
                        label: `${datos.anio_anterior_valor}`,
                        data: datosAnterior,
                        backgroundColor: colores.neutro,
                        borderColor: colores.neutro,
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + new Intl.NumberFormat('es-CL', {
                                    style: 'currency',
                                    currency: 'CLP'
                                }).format(context.raw);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return '$' + (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return '$' + (value / 1000).toFixed(0) + 'K';
                                }
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
    }
}

/**
 * Carga y renderiza el gráfico de tendencia anual
 */
function cargarGraficoTendenciaAnual() {
    fetch('get_dashboard_data.php?tipo=tendencia_anual')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderizarGraficoTendenciaAnual(data.data);
            } else {
                console.error('Error al cargar datos de tendencia anual:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de tendencia anual:', error);
        });
}

/**
 * Renderiza el gráfico de tendencia anual
 * @param {Array} datos - Datos de tendencia anual
 */
function renderizarGraficoTendenciaAnual(datos) {
    const ctx = document.getElementById('grafico-tendencia-anual').getContext('2d');

    // Preparar datos para el gráfico
    const labels = datos.map(item => nombresMeses[item.mes - 1]);
    const valoresReales = datos.map(item => item.es_proyeccion ? null : parseInt(item.venta_total));
    const valoresProyectados = datos.map(item => item.es_proyeccion ? parseInt(item.venta_total) : null);

    // Crear o actualizar el gráfico
    if (chartTendenciaAnual) {
        chartTendenciaAnual.data.labels = labels;
        chartTendenciaAnual.data.datasets[0].data = valoresReales;
        chartTendenciaAnual.data.datasets[1].data = valoresProyectados;
        chartTendenciaAnual.update();
    } else {
        chartTendenciaAnual = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Ventas Reales',
                        data: valoresReales,
                        backgroundColor: colores.principal,
                        borderColor: colores.principal,
                        borderWidth: 2,
                        pointBackgroundColor: colores.principal,
                        pointRadius: 4,
                        tension: 0.3,
                        fill: false
                    },
                    {
                        label: 'Proyección',
                        data: valoresProyectados,
                        backgroundColor: colores.secundario,
                        borderColor: colores.secundario,
                        borderWidth: 2,
                        pointBackgroundColor: colores.secundario,
                        pointRadius: 4,
                        borderDash: [5, 5],
                        tension: 0.3,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + new Intl.NumberFormat('es-CL', {
                                    style: 'currency',
                                    currency: 'CLP'
                                }).format(context.raw || 0);
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return '$' + (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return '$' + (value / 1000).toFixed(0) + 'K';
                                }
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
    }
}

/**
 * Carga y renderiza el gráfico de estacionalidad
 */
function cargarGraficoEstacionalidad() {
    fetch('get_dashboard_data.php?tipo=estacionalidad')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderizarGraficoEstacionalidad(data.data);
            } else {
                console.error('Error al cargar datos de estacionalidad:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de estacionalidad:', error);
        });
}

/**
 * Renderiza el gráfico de estacionalidad
 * @param {Array} datos - Datos de estacionalidad
 */
function renderizarGraficoEstacionalidad(datos) {
    const ctx = document.getElementById('grafico-estacionalidad').getContext('2d');

    // Ordenar los días de la semana (Lunes a Domingo)
    datos.sort((a, b) => {
        // Reordenar para que comience en Lunes (2) y termine en Domingo (1)
        const diaA = a.dia_semana === 1 ? 8 : a.dia_semana;
        const diaB = b.dia_semana === 1 ? 8 : b.dia_semana;
        return diaA - diaB;
    });

    // Preparar datos para el gráfico
    const labels = datos.map(item => item.nombre_dia);
    const valores = datos.map(item => parseInt(item.venta_total));
    const transacciones = datos.map(item => parseInt(item.num_transacciones));

    // Crear o actualizar el gráfico
    if (chartEstacionalidad) {
        chartEstacionalidad.data.labels = labels;
        chartEstacionalidad.data.datasets[0].data = valores;
        chartEstacionalidad.data.datasets[1].data = transacciones;
        chartEstacionalidad.update();
    } else {
        chartEstacionalidad = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Venta Total',
                        data: valores,
                        backgroundColor: colores.principal,
                        borderColor: colores.principal,
                        borderWidth: 1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Número de Transacciones',
                        data: transacciones,
                        backgroundColor: colores.secundario,
                        borderColor: colores.secundario,
                        borderWidth: 1,
                        type: 'line',
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === 'Venta Total') {
                                    return context.dataset.label + ': ' + new Intl.NumberFormat('es-CL', {
                                        style: 'currency',
                                        currency: 'CLP'
                                    }).format(context.raw);
                                } else {
                                    return context.dataset.label + ': ' + context.raw;
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Venta Total'
                        },
                        ticks: {
                            callback: function(value) {
                                if (value >= 1000000) {
                                    return '$' + (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return '$' + (value / 1000).toFixed(0) + 'K';
                                }
                                return '$' + value;
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Transacciones'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
    }
}

/**
 * Carga y renderiza el gráfico de distribución por tipo de DTE
 * @param {string} periodo - Período de tiempo (dia, mes, anio)
 */
function cargarGraficoDistribucionDTE(periodo) {
    fetch(`get_dashboard_data.php?tipo=distribucion_dte&periodo=${periodo}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderizarGraficoDistribucionDTE(data.data, periodo);
            } else {
                console.error('Error al cargar datos de distribución DTE:', data.error);
            }
        })
        .catch(error => {
            console.error('Error en la petición de distribución DTE:', error);
        });
}

/**
 * Renderiza el gráfico de distribución por tipo de DTE
 * @param {Array} datos - Datos de distribución por tipo de DTE
 * @param {string} periodo - Período actual
 */
function renderizarGraficoDistribucionDTE(datos, periodo) {
    const ctx = document.getElementById('grafico-distribucion-dte').getContext('2d');

    // Preparar datos para el gráfico
    const labels = datos.map(item => item.nombre_dte);
    const valores = datos.map(item => parseInt(item.cantidad)); // Usamos cantidad en lugar de venta_total
    const montos = datos.map(item => parseInt(item.venta_total));

    // Colores para cada tipo de DTE
    const coloresDTE = [
        '#3498db', // Azul
        '#2ecc71', // Verde
        '#e74c3c', // Rojo
        '#f39c12'  // Naranja
    ];

    // Crear o actualizar el gráfico
    if (chartDistribucionDTE) {
        chartDistribucionDTE.data.labels = labels;
        chartDistribucionDTE.data.datasets[0].data = valores;
        chartDistribucionDTE.update();
    } else {
        chartDistribucionDTE = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: valores,
                    backgroundColor: coloresDTE,
                    borderColor: '#ffffff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 12
                            },
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                const monto = montos[context.dataIndex];

                                return [
                                    label + ': ' + value + ' documentos',
                                    `${percentage}% del total`,
                                    `Monto: ${new Intl.NumberFormat('es-CL', {
                                        style: 'currency',
                                        currency: 'CLP'
                                    }).format(monto)}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    // Actualizar título del gráfico según el período
    let tituloGrafico = 'Cantidad de Documentos por Tipo de DTE';
    document.getElementById('titulo-distribucion-dte').textContent = tituloGrafico;
}
