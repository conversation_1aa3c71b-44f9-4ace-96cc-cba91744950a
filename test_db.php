<?php
// Archivo para probar la conexión a la base de datos directamente

// Iniciar buffer de salida
ob_start();

// Incluir el archivo de conexión
require_once 'db_connection.php';

// Aumentar límite de tiempo de ejecución
set_time_limit(30);

// Configurar manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Función simple para mostrar mensajes
function showMessage($message, $type = 'info') {
    $color = 'black';
    switch($type) {
        case 'success':
            $color = 'green';
            break;
        case 'error':
            $color = 'red';
            break;
        case 'warning':
            $color = 'orange';
            break;
    }
    
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 5px solid {$color}; background-color: #f9f9f9;'>{$message}</div>";
}

echo "<html><head><title>Prueba de Conexión a la Base de Datos</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h1 { color: #333; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
</style>";
echo "</head><body>";
echo "<h1>Prueba de Conexión a la Base de Datos</h1>";

try {
    // Mostrar información del servidor
    echo "<h2>Información del Servidor</h2>";
    echo "<ul>";
    echo "<li>PHP Version: " . phpversion() . "</li>";
    echo "<li>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
    echo "<li>System: " . php_uname() . "</li>";
    echo "</ul>";
    
    // Probar la conexión
    echo "<h2>Probando Conexión a la Base de Datos</h2>";
    
    showMessage("Intentando conectar a la base de datos...");
    
    $startTime = microtime(true);
    $conn = getConnection();
    $endTime = microtime(true);
    $connectionTime = round(($endTime - $startTime) * 1000, 2);
    
    showMessage("¡Conexión establecida exitosamente! (Tiempo: {$connectionTime}ms)", 'success');
    
    // Probar consulta simple
    echo "<h2>Probando Consulta Simple</h2>";
    
    showMessage("Ejecutando consulta SELECT 1...");
    $startTime = microtime(true);
    $stmt = $conn->query("SELECT 1 as test");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $endTime = microtime(true);
    $queryTime = round(($endTime - $startTime) * 1000, 2);
    
    if ($result && isset($result['test']) && $result['test'] == 1) {
        showMessage("Consulta exitosa: " . print_r($result, true) . " (Tiempo: {$queryTime}ms)", 'success');
    } else {
        showMessage("La consulta no devolvió el resultado esperado", 'error');
    }
    
    // Verificar si existe la tabla tb_upgrades
    echo "<h2>Verificando Tabla tb_upgrades</h2>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'tb_upgrades'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        showMessage("La tabla tb_upgrades existe en la base de datos", 'success');
        
        // Mostrar estructura de la tabla
        echo "<h3>Estructura de la Tabla</h3>";
        $stmt = $conn->query("DESCRIBE tb_upgrades");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Valor por defecto</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] === NULL ? 'NULL' : $column['Default']) . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar registros
        $stmt = $conn->query("SELECT COUNT(*) as total FROM tb_upgrades");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        
        showMessage("La tabla tb_upgrades contiene {$count['total']} registros");
        
        // Mostrar últimos registros
        if ($count['total'] > 0) {
            echo "<h3>Últimos 5 Registros</h3>";
            $stmt = $conn->query("SELECT * FROM tb_upgrades ORDER BY id DESC LIMIT 5");
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr>";
            foreach (array_keys($records[0]) as $key) {
                echo "<th>{$key}</th>";
            }
            echo "</tr>";
            
            foreach ($records as $record) {
                echo "<tr>";
                foreach ($record as $value) {
                    echo "<td>" . (is_null($value) ? 'NULL' : htmlspecialchars($value)) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Probar inserción
        echo "<h2>Probando Inserción en la Tabla</h2>";
        
        try {
            $conn->beginTransaction();
            
            $stmt = $conn->prepare("
                INSERT INTO tb_upgrades 
                (pagina_modificada, descripcion, ruta_archivo, tipo_archivo) 
                VALUES (?, ?, ?, ?)
            ");
            
            $timestamp = date('YmdHis');
            $pagina = "test_page_{$timestamp}.php";
            $descripcion = "Prueba de inserción desde test_db.php - " . date('Y-m-d H:i:s');
            $rutaArchivo = null;
            $tipoArchivo = 'ninguno';
            
            $result = $stmt->execute([$pagina, $descripcion, $rutaArchivo, $tipoArchivo]);
            
            if ($result) {
                $lastId = $conn->lastInsertId();
                showMessage("Inserción exitosa con ID: {$lastId}", 'success');
                
                // Verificar si el registro se insertó correctamente
                $verifyStmt = $conn->prepare("SELECT * FROM tb_upgrades WHERE id = ?");
                $verifyStmt->execute([$lastId]);
                $newRecord = $verifyStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($newRecord) {
                    echo "<h3>Registro Insertado</h3>";
                    echo "<pre>" . print_r($newRecord, true) . "</pre>";
                } else {
                    showMessage("No se pudo verificar la inserción", 'error');
                }
                
                // Eliminar el registro de prueba
                $deleteStmt = $conn->prepare("DELETE FROM tb_upgrades WHERE id = ?");
                $deleteStmt->execute([$lastId]);
                
                showMessage("Registro de prueba eliminado", 'success');
            } else {
                showMessage("Error al insertar: " . print_r($stmt->errorInfo(), true), 'error');
            }
            
            $conn->commit();
        } catch (Exception $e) {
            $conn->rollBack();
            showMessage("Error en la transacción: " . $e->getMessage(), 'error');
        }
    } else {
        showMessage("La tabla tb_upgrades no existe en la base de datos", 'warning');
        
        // Crear la tabla
        echo "<h3>Creando la Tabla</h3>";
        
        try {
            $createTableSql = "
                CREATE TABLE tb_upgrades (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    pagina_modificada VARCHAR(100) NOT NULL,
                    descripcion TEXT NOT NULL,
                    ruta_archivo VARCHAR(255),
                    tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno',
                    usuario VARCHAR(100) DEFAULT 'admin'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $conn->exec($createTableSql);
            showMessage("Tabla tb_upgrades creada exitosamente", 'success');
        } catch (Exception $e) {
            showMessage("Error al crear la tabla: " . $e->getMessage(), 'error');
        }
    }
    
    // Cerrar la conexión
    $conn = null;
    showMessage("Conexión cerrada");
    
} catch (Exception $e) {
    showMessage("Error: " . $e->getMessage(), 'error');
    echo "<pre>{$e->getTraceAsString()}</pre>";
}

echo "</body></html>";

// Limpiar y enviar el buffer de salida
ob_end_flush();
