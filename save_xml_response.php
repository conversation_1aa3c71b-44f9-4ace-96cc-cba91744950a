<?php
// Archivo: enviar_dte.php
header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener el JSON enviado
$jsonData = $_POST['jsonData'] ?? '';

if (empty($jsonData)) {
    echo json_encode(['error' => 'Datos JSON no proporcionados']);
    exit;
}

// Rutas a los archivos
$certificadoPath = 'Documents/17365958-K.pfx';
$foliosPath = 'Documents/folios/Facturas/folios_facturas_10';

// Verificar que los archivos existen
if (!file_exists($certificadoPath)) {
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

if (!file_exists($foliosPath)) {
    echo json_encode(['error' => 'Archivo de folios no encontrado: ' . $foliosPath]);
    exit;
}

// Configurar la solicitud cURL
$ch = curl_init('https://api.simpleapi.cl/api/v1/dte/generar');

// Preparar el formulario multipart
$boundary = uniqid();
$delimiter = '-------------' . $boundary;

// Construir el cuerpo de la solicitud
$postData = '';

// Agregar el campo JSON
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
$postData .= $jsonData . "\r\n";

// Agregar el archivo de certificado
$fileContents = file_get_contents($certificadoPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Agregar el archivo de folios
$fileContents = file_get_contents($foliosPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($foliosPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Cerrar el cuerpo del mensaje
$postData .= "--" . $delimiter . "--\r\n";

// Configuración de cURL
$apiKey = $_POST['apiKey'] ?? '8131-N690-6390-1021-2707'; // Token de autorización
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_HTTPHEADER => [
        "Authorization: $apiKey",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($postData)
    ]
]);

// Ejecutar la solicitud
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

// Si hubo algún error con cURL
if ($curlError) {
    echo json_encode(['error' => 'Error al conectar con la API: ' . $curlError]);
    exit;
}

// Si la respuesta no fue exitosa
if ($httpCode !== 200) {
    echo json_encode([
        'error' => 'Error al enviar el DTE. Código HTTP: ' . $httpCode,
        'response' => $response
    ]);
    exit;
}

// Si la respuesta fue exitosa, guardar el XML y registrar en la base de datos
require_once 'save_xml_response.php';

// Procesar la respuesta XML
$resultado = procesarRespuestaXML($response, $jsonData);

if ($resultado['success']) {
    echo json_encode([
        'success' => true,
        'mensaje' => 'DTE procesado y guardado correctamente',
        'archivo' => $resultado['archivo'],
        'ruta' => $resultado['ruta']
    ]);
} else {
    echo json_encode([
        'error' => $resultado['mensaje'],
        'detalles' => $resultado
    ]);
}
?>