<?php
/**
 * Script para verificar y mostrar el contenido de los archivos XML pendientes
 */

// Configuración de visualización de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Incluir archivos necesarios
require_once 'db_connection.php';

// Función para verificar un archivo XML
function verificarXML($ruta) {
    $resultado = [
        'ruta' => $ruta,
        'existe' => file_exists($ruta) ? 'sí' : 'no',
        'tamaño' => file_exists($ruta) ? filesize($ruta) : 'N/A',
        'permisos' => file_exists($ruta) ? decoct(fileperms($ruta) & 0777) : 'N/A',
        'readable' => is_readable($ruta) ? 'sí' : 'no',
        'contenido' => null,
        'es_xml_valido' => false
    ];
    
    if (file_exists($ruta) && is_readable($ruta)) {
        // Leer el contenido del archivo
        $contenido = file_get_contents($ruta);
        $resultado['contenido'] = substr($contenido, 0, 1000) . (strlen($contenido) > 1000 ? '...' : '');
        
        // Verificar si es un XML válido
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($contenido);
        if ($xml !== false) {
            $resultado['es_xml_valido'] = true;
        } else {
            $resultado['errores_xml'] = [];
            foreach (libxml_get_errors() as $error) {
                $resultado['errores_xml'][] = [
                    'mensaje' => $error->message,
                    'linea' => $error->line,
                    'columna' => $error->column
                ];
            }
            libxml_clear_errors();
        }
    }
    
    return $resultado;
}

// Obtener el ID del documento a verificar
$id = isset($_GET['id']) ? (int)$_GET['id'] : null;

try {
    $conn = getConnection();
    
    if ($id) {
        // Verificar un documento específico
        $stmt = $conn->prepare("
            SELECT id, tipo_dte, nombre_archivo 
            FROM tb_facturas_dte 
            WHERE id = :id
        ");
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $documento = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($documento) {
            $resultado = verificarXML($documento['nombre_archivo']);
            $resultado['id'] = $documento['id'];
            $resultado['tipo_dte'] = $documento['tipo_dte'];
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'documento' => $resultado
            ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        } else {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Documento no encontrado'
            ]);
        }
    } else {
        // Verificar todos los documentos pendientes
        $stmt = $conn->query("
            SELECT id, tipo_dte, nombre_archivo 
            FROM tb_facturas_dte 
            WHERE estado_sobre = 0
            LIMIT 10
        ");
        $documentos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $resultados = [];
        foreach ($documentos as $doc) {
            $resultado = verificarXML($doc['nombre_archivo']);
            $resultado['id'] = $doc['id'];
            $resultado['tipo_dte'] = $doc['tipo_dte'];
            $resultados[] = $resultado;
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'documentos' => $resultados,
            'total' => count($resultados)
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
