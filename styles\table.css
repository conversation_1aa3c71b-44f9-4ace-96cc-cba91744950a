/* Estilos base de la tabla */
.products-table {
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    display: none;
}

/* Estilo para la celda de descripción con tooltip */
.description-cell {
    cursor: help;
    position: relative;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.products-table table {
    width: 100%;
    min-width: 800px;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: table !important;
}

.products-table tbody tr {
    display: table-row !important;
}

.products-table td,
.products-table th {
    display: table-cell !important;
    padding: 12px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
}

.products-table th {
    background: var(--primary-color);
    color: white;
    position: sticky;
    top: 0;
    z-index: 2;
    white-space: nowrap;
}

.table-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

/* Anchos específicos para las columnas */
.products-table th[data-col="imagen"],
.products-table td:first-child {
    width: 80px;
}

.products-table th[data-col="nombre"],
.products-table td:nth-child(2) {
    width: 150px;
}

.products-table th[data-col="marca"],
.products-table td:nth-child(3) {
    width: 100px;
}

.products-table th[data-col="modelo"],
.products-table td:nth-child(4) {
    width: 100px;
}

.products-table th[data-col="año"],
.products-table td:nth-child(5) {
    width: 80px;
}

.products-table th[data-col="combustible"],
.products-table td:nth-child(6) {
    width: 100px;
}

.products-table th[data-col="cilindrada"],
.products-table td:nth-child(7) {
    width: 100px;
}

.products-table th[data-col="descripcion"],
.products-table td:nth-child(8) {
    width: 200px;
}

.products-table th[data-col="precio"],
.products-table td:nth-child(9) {
    width: 100px;
}

.products-table th[data-col="cantidad"],
.products-table td:nth-child(10) {
    width: 120px;
}

.products-table th[data-col="accion"],
.products-table td:nth-child(11) {
    width: 80px;
}

/* Eliminar el color de fondo de la columna de acciones */
.products-table td:last-child,
.products-table th[data-col="accion"] {
    background: transparent !important; /* Asegura que no haya color de fondo */
}

/* Ajustar los botones de acción para mantener su estilo sin el fondo de la columna */
.action-buttons {
    padding: 5px;
    background: transparent;
    text-align: center;
}

.action-container {
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

/* Estilo base para los botones */
.action-buttons button {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Botón Editar */
.edit-btn {
    background-color: #00a1e0;
    color: white;
}

/* Botón Asignar */
.assign-btn {
    background-color: #4caf50;
    color: white;
}

/* Iconos dentro de los botones */
.action-buttons button i {
    font-size: 0.9rem;
}

/* Efecto al hacer click */
.action-buttons button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Estilo para cuando los botones están deshabilitados */
.action-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

@media (max-width: 768px) {
    .products-table {
        margin: 0;
        padding: 0;
        border-radius: 0;
    }

    .products-table::-webkit-scrollbar {
        height: 6px;
    }

    .products-table::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }

    .products-table table {
        margin: 0;
        border: none;
    }

    .products-table th,
    .products-table td {
        padding: 8px;
        font-size: 14px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
    }

    .quantity-btn {
        width: 24px;
        height: 24px;
        padding: 0;
        font-size: 0.9rem;
    }

    .add-to-cart-btn {
        padding: 0.5rem;
        font-size: 0.9rem;
        white-space: nowrap;
    }
}

.search-button {
  background: var(--accent-color);
  border: none;
  padding: 0.8rem 1.5rem;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  margin-left: 10px;
}

.search-button:hover {
  background: var(--hover-color);
}

.reset-button {
  background: var(--secondary-color);
  border: none;
  padding: 0.8rem 1.5rem;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  margin-left: 10px;
}

.reset-button:hover {
  background: var(--hover-color);
}

.module-link {
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  transition: background 0.3s ease;
}

.module-link:hover {
  background: rgba(255, 255, 255, 0.2);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input-wrapper input {
  flex: 1;
}

/* Estilos para los botones de acción con iconos */
.icon-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    margin: 0 3px;
}

.icon-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.icon-btn i {
    font-size: 16px;
}

.edit-btn {
    background-color: #34495e;
}

.edit-btn:hover {
    background-color: #2c3e50;
}

.assign-btn {
    background-color: #e74c3c;
}

.assign-btn:hover {
    background-color: #c0392b;
}

/* Estilo base para todas las etiquetas de categoría */
.category-tag {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    font-size: 0.85rem;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Motor - Azul */
.category-motor {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

/* Sistema de Transmisión - Morado */
.category-transmision {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

/* Sistema de Frenos - Rojo */
.category-frenos {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Sistema de Suspensión - Naranja */
.category-suspension {
    background: linear-gradient(135deg, #f39c12, #d35400);
}

/* Sistema Eléctrico - Amarillo */
.category-electrico {
    background: linear-gradient(135deg, #f1c40f, #d4ac0d);
}

/* Filtros - Morado */
.category-filtros {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

/* Carroceria - Verde Agua */
.category-carroceria {
    background: linear-gradient(135deg, #1abc9c, #16a085);
}

/* Default - Gris */
.category-default {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

/* Ajustes específicos para la tabla de inventario */
#tabla_repuestos {
    font-size: 0.75rem !important;
}

#tabla_repuestos td,
#tabla_repuestos th {
    padding: 0.3rem 0.5rem !important;
    line-height: 1.2 !important;
}

#tabla_repuestos .badge {
    font-size: 0.7rem !important;
    padding: 0.2em 0.4em !important;
}

#tabla_repuestos .btn-sm {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
}

/* Mantener encabezados legibles */
#tabla_repuestos thead th {
    font-size: 0.8rem !important;
    font-weight: 600 !important;
}

/* Ajuste para el contenedor de la tabla */
.table-responsive {
    font-size: 0.75rem !important;
}

/* Estilos modernos para todos los modales */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(52, 73, 94, 0.25);
    overflow: hidden;
    animation: modalFadeIn 0.4s ease-out;
    max-height: 90vh; /* Altura máxima del 90% de la altura de la ventana */
    background-color: #f8f9fa;
}

/* Asegurar que el modal se ajuste a pantallas pequeñas */
@media (max-height: 768px) {
    .modal-content {
        max-height: 95vh;
    }

    .modal-body {
        max-height: 65vh;
        padding: 1rem;
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Animación para el fondo del modal */
.modal-backdrop {
    animation: backdropFadeIn 0.3s ease-out;
}

@keyframes backdropFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.5;
    }
}

.modal-header {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    border-bottom: none;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-weight: 600;
    font-size: 1.4rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin: 0;
    display: flex;
    align-items: center;
    letter-spacing: 0.5px;
}

.modal-title i {
    margin-right: 12px;
    font-size: 1.3em;
    background: rgba(255, 255, 255, 0.15);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-close {
    color: white;
    opacity: 0.9;
    filter: brightness(5);
    transition: all 0.3s ease;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
}

.btn-close:hover {
    opacity: 1;
    transform: rotate(90deg);
    background-color: rgba(255, 255, 255, 0.25);
}

.modal-body {
    background: linear-gradient(to bottom, #f5f7fa, #eef2f7);
    padding: 1.5rem;
    max-height: 70vh; /* Altura máxima del 70% de la altura de la ventana */
    overflow-y: auto; /* Habilitar scroll vertical */
    scrollbar-width: thin; /* Para Firefox */
    scrollbar-color: #3498db #f5f7fa; /* Para Firefox */
}

/* Estilos para la barra de desplazamiento (Chrome, Edge, Safari) */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 10px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 10px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* Estilos para las secciones del formulario en todos los modales */
.modal .row {
    margin-bottom: 0.25rem;
}

.modal .mb-3, .modal .form-group {
    position: relative;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem !important; /* Reducir el espacio entre elementos */
}

.modal .mb-3:hover, .modal .form-group:hover {
    transform: translateY(-2px);
}

/* Reducir el tamaño de las etiquetas para ahorrar espacio */
.modal .form-label, .modal label {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    position: relative;
    display: inline-block;
    font-weight: 600;
    color: #34495e;
}

.modal .form-label::after, .modal label::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2px;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
}

.modal .form-label:hover::after, .modal label:hover::after {
    width: 100%;
}

.modal .form-control,
.modal .form-select {
    border-radius: 8px;
    border: 1px solid #dce4ec;
    padding: 0.5rem 0.75rem; /* Reducir el padding */
    transition: all 0.3s ease;
    background-color: white;
    font-size: 0.95rem; /* Reducir ligeramente el tamaño de la fuente */
    height: calc(1.5em + 0.75rem + 2px); /* Altura más compacta */
}

/* Ajustar la altura de los textareas */
.modal textarea.form-control {
    height: auto;
    min-height: 80px; /* Altura mínima para textareas */
}

.modal .form-control:focus,
.modal .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

.modal .form-control:hover,
.modal .form-select:hover {
    border-color: #3498db;
    transition: all 0.3s ease;
}

.modal .input-group-text {
    background-color: #34495e;
    color: white;
    border: none;
    border-radius: 8px 0 0 8px;
}

.modal-footer {
    background: linear-gradient(to right, #f5f7fa, #eef2f7);
    border-top: 1px solid rgba(52, 73, 94, 0.1);
    padding: 1.2rem 1.5rem;
    justify-content: flex-end;
    gap: 10px;
}

.modal .btn-secondary {
    background-color: #95a5a6;
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal .btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(127, 140, 141, 0.3);
}

.modal .btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal .btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.modal .btn i {
    margin-right: 6px;
    font-size: 1.1em;
}

/* Estilos para los campos deshabilitados */
.modal .form-control:disabled,
.modal .form-select:disabled {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

/* Estilos para el mensaje de cargando */
.modal select option[value=""] {
    color: #6c757d;
    font-style: italic;
}

/* Efecto de transición para todo el modal */
.modal * {
    transition: all 0.3s ease;
}

/* Estilos para botones de acción dentro de los modales */
.modal .btn-sm {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.modal .btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: none;
}

.modal .btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #e74c3c);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

/* Estilos para listas dentro de modales */
.modal .list-group-item {
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #dce4ec;
    transition: all 0.3s ease;
}

.modal .list-group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(52, 73, 94, 0.1);
}

/* Carrocería - Verde */
.category-carroceria {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

/* Sistema de Refrigeración - Celeste */
.category-refrigeracion {
    background: linear-gradient(135deg, #00bcd4, #0097a7);
}

/* Sistema de Escape - Gris oscuro */
.category-escape {
    background: linear-gradient(135deg, #607d8b, #455a64);
}

/* Interior - Marrón */
.category-interior {
    background: linear-gradient(135deg, #795548, #5d4037);
}

/* Filtros - Verde azulado */
.category-filtros {
    background: linear-gradient(135deg, #009688, #00796b);
}

/* Categoría por defecto - Gris */
.category-default {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

/* Efecto hover para todas las categorías */
.category-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.15);
}



