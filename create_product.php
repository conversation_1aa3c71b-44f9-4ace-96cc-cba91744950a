<?php
require_once 'db_connection.php';
require_once 'image_processor.php';

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 0);

try {
    // Validación de campos requeridos
    if (!isset($_POST['sku']) || !isset($_POST['nombre'])) {
        throw new Exception('Datos incompletos');
    }

    $url_imagen = null;

    // Procesar la imagen si fue enviada
    if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
        try {
            // Directorio para guardar las imágenes
            $uploadDir = 'images/fotos_repuestos/';
            $absoluteUploadDir = __DIR__ . '/' . $uploadDir;

            // Asegurarse de que el directorio termine con una barra
            if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
                $absoluteUploadDir .= '/';
            }

            // Registrar información para depuración
            error_log('Procesando imagen en create_product.php');
            error_log('Nombre del archivo: ' . $_FILES['imagen']['name']);
            error_log('Tamaño del archivo: ' . $_FILES['imagen']['size'] . ' bytes');
            error_log('Directorio de destino: ' . $absoluteUploadDir);

            // Verificar que el directorio existe y tiene permisos
            if (!file_exists($absoluteUploadDir)) {
                error_log('El directorio no existe, intentando crearlo: ' . $absoluteUploadDir);
                if (!mkdir($absoluteUploadDir, 0777, true)) {
                    throw new Exception('No se pudo crear el directorio: ' . $absoluteUploadDir);
                }
                chmod($absoluteUploadDir, 0777);
                error_log('Directorio creado con éxito: ' . $absoluteUploadDir);
            }

            if (!is_writable($absoluteUploadDir)) {
                error_log('El directorio no tiene permisos de escritura, intentando cambiar permisos: ' . $absoluteUploadDir);
                if (!chmod($absoluteUploadDir, 0777)) {
                    throw new Exception('No se pudieron cambiar los permisos del directorio: ' . $absoluteUploadDir);
                }
                error_log('Permisos cambiados con éxito: ' . $absoluteUploadDir);
            }

            // Verificar que el archivo temporal existe
            if (!file_exists($_FILES['imagen']['tmp_name'])) {
                throw new Exception('El archivo temporal no existe: ' . $_FILES['imagen']['tmp_name']);
            }

            // Procesar la imagen
            $imageResult = ImageProcessor::processUploadedImage(
                $_FILES['imagen'],
                $absoluteUploadDir,
                'producto_',  // Prefijo para el nombre del archivo
                800,          // Ancho máximo
                800,          // Alto máximo
                85            // Calidad (0-100)
            );

            if (!$imageResult['success']) {
                error_log('Error al procesar la imagen: ' . $imageResult['message']);
                throw new Exception('Error al procesar la imagen: ' . $imageResult['message']);
            }

            // Obtener la ruta relativa para guardar en la base de datos
            $url_imagen = str_replace(__DIR__ . '/', '', $imageResult['path']);

            // Registrar información sobre el procesamiento de la imagen
            error_log('Imagen procesada correctamente: ' . $url_imagen);
        } catch (Exception $e) {
            error_log('Excepción al procesar la imagen: ' . $e->getMessage());
            throw new Exception('Error al procesar la imagen: ' . $e->getMessage());
        }
    }

    // Conectar a la base de datos
    $conn = getConnection();

    $sql = "INSERT INTO repuesto (
        sku, nombre, descripcion, categoria_id, id_subcategoria,
        precio_compra, precio_venta, stock_minimo,
        stock_maximo, unidad_medida, ubicacion_almacen,
        es_original, fabricante, pais_origen,
        codigo_fabricante, url_imagen, activo
    ) VALUES (
        :sku, :nombre, :descripcion, :categoria_id, :id_subcategoria,
        :precio_compra, :precio_venta, :stock_minimo,
        :stock_maximo, :unidad_medida, :ubicacion_almacen,
        :es_original, :fabricante, :pais_origen,
        :codigo_fabricante, :url_imagen, 1
    )";

    $stmt = $conn->prepare($sql);

    // Registrar los valores recibidos para depuración
    error_log("Datos recibidos en create_product.php: " . json_encode($_POST));

    $params = [
        ':sku' => $_POST['sku'],
        ':nombre' => $_POST['nombre'],
        ':descripcion' => $_POST['descripcion'] ?? '',
        ':categoria_id' => $_POST['new_categoria_principal'] ?? null,
        ':id_subcategoria' => $_POST['new_subcategoria'] ?? null,
        ':precio_compra' => $_POST['precio_compra'] ?? 0,
        ':precio_venta' => $_POST['precio_venta'] ?? 0,
        ':stock_minimo' => $_POST['stock_minimo'] ?? 0,
        ':stock_maximo' => $_POST['stock_maximo'] ?? 0,
        ':unidad_medida' => $_POST['unidad_medida'] ?? 'unidad',
        ':ubicacion_almacen' => 'Temuco',
        ':es_original' => $_POST['es_original'] ?? 0,
        ':fabricante' => $_POST['fabricante'] ?? '',
        ':pais_origen' => $_POST['pais_origen'] ?? '',
        ':codigo_fabricante' => $_POST['codigo_fabricante'] ?? '',
        ':url_imagen' => $url_imagen
    ];

    if (!$stmt->execute($params)) {
        throw new Exception('Error al insertar en la base de datos');
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Producto guardado exitosamente',
        'id' => $conn->lastInsertId(),
        'url_imagen' => $url_imagen
    ]);

} catch(Exception $e) {
    error_log("Error en save_product.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}

$conn = null;