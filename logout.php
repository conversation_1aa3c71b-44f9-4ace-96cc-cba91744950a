<?php
// Incluir configuración de sesiones
require_once 'session_config.php';
session_start();

// Registrar el cierre de sesión si hay un usuario logueado
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    require_once 'db_connection.php';
    
    try {
        $conn = getConnection();
        
        // Registrar el cierre de sesión
        $logSql = "INSERT INTO tb_login_logs (username, ip_address, success, fecha, accion) 
                   VALUES (?, ?, 1, NOW(), 'logout')";
        try {
            $logStmt = $conn->prepare($logSql);
            $logStmt->execute([$_SESSION['username'], $_SERVER['REMOTE_ADDR']]);
        } catch (Exception $e) {
            // Si la tabla no existe o falta la columna accion, no hacemos nada
            error_log('Error al registrar cierre de sesión: ' . $e->getMessage());
        }
    } catch (Exception $e) {
        error_log('Error en logout.php: ' . $e->getMessage());
    }
}

// Destruir todas las variables de sesión
$_SESSION = array();

// Si se desea destruir la cookie de sesión
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destruir la sesión
session_destroy();

// Prevenir el caché del navegador
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Redirigir al login con un parámetro único para evitar caché
$redirect_url = 'login.php?logout=' . time();
header('Location: ' . $redirect_url);
exit;
?>
