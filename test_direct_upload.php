<?php
// Script para probar la subida directa de archivos sin usar la clase ImageProcessor

// Mostrar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar si se ha enviado un formulario
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h1>Resultado de la subida directa</h1>";
    
    // Verificar si se ha enviado un archivo
    if (!isset($_FILES['imagen']) || $_FILES['imagen']['error'] !== UPLOAD_ERR_OK) {
        echo "<p style='color:red'>Error: No se ha enviado ninguna imagen o ha ocurrido un error en la subida.</p>";
        if (isset($_FILES['imagen'])) {
            echo "<p>Código de error: " . $_FILES['imagen']['error'] . "</p>";
        }
    } else {
        // Mostrar información del archivo subido
        echo "<h2>Información del archivo subido</h2>";
        echo "<ul>";
        echo "<li>Nombre: " . htmlspecialchars($_FILES['imagen']['name']) . "</li>";
        echo "<li>Tipo: " . htmlspecialchars($_FILES['imagen']['type']) . "</li>";
        echo "<li>Tamaño: " . htmlspecialchars($_FILES['imagen']['size']) . " bytes</li>";
        echo "<li>Ruta temporal: " . htmlspecialchars($_FILES['imagen']['tmp_name']) . "</li>";
        echo "</ul>";
        
        // Directorio para guardar las imágenes
        $uploadDir = 'images/fotos_repuestos/';
        $absoluteUploadDir = __DIR__ . '/' . $uploadDir;
        
        // Asegurarse de que el directorio termine con una barra
        if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
            $absoluteUploadDir .= '/';
        }
        
        echo "<h2>Directorio de destino</h2>";
        echo "<p>Ruta: <code>" . htmlspecialchars($absoluteUploadDir) . "</code></p>";
        
        // Verificar que el directorio existe
        if (!file_exists($absoluteUploadDir)) {
            echo "<p>El directorio no existe, intentando crearlo...</p>";
            if (mkdir($absoluteUploadDir, 0777, true)) {
                echo "<p style='color:green'>Directorio creado correctamente.</p>";
            } else {
                echo "<p style='color:red'>Error al crear el directorio.</p>";
                exit;
            }
        }
        
        // Verificar permisos
        if (!is_writable($absoluteUploadDir)) {
            echo "<p>El directorio no tiene permisos de escritura, intentando cambiar permisos...</p>";
            if (chmod($absoluteUploadDir, 0777)) {
                echo "<p style='color:green'>Permisos cambiados correctamente.</p>";
            } else {
                echo "<p style='color:red'>Error al cambiar los permisos.</p>";
                exit;
            }
        }
        
        // Generar nombre único para el archivo
        $fileExtension = strtolower(pathinfo($_FILES['imagen']['name'], PATHINFO_EXTENSION));
        $fileName = 'test_direct_' . uniqid() . '_' . time() . '.' . $fileExtension;
        $uploadFile = $absoluteUploadDir . $fileName;
        
        echo "<h2>Intentando subir el archivo</h2>";
        echo "<p>Destino: <code>" . htmlspecialchars($uploadFile) . "</code></p>";
        
        // Intentar subir el archivo
        $success = false;
        $method = '';
        
        // Método 1: move_uploaded_file
        echo "<h3>Método 1: move_uploaded_file</h3>";
        if (move_uploaded_file($_FILES['imagen']['tmp_name'], $uploadFile)) {
            $success = true;
            $method = 'move_uploaded_file';
            echo "<p style='color:green'>✓ Archivo subido correctamente usando move_uploaded_file.</p>";
        } else {
            echo "<p style='color:red'>✗ Error al subir el archivo usando move_uploaded_file.</p>";
            
            // Método 2: copy
            echo "<h3>Método 2: copy</h3>";
            if (copy($_FILES['imagen']['tmp_name'], $uploadFile)) {
                $success = true;
                $method = 'copy';
                echo "<p style='color:green'>✓ Archivo copiado correctamente usando copy.</p>";
            } else {
                echo "<p style='color:red'>✗ Error al copiar el archivo usando copy.</p>";
                
                // Método 3: file_put_contents
                echo "<h3>Método 3: file_put_contents</h3>";
                $fileContent = file_get_contents($_FILES['imagen']['tmp_name']);
                if ($fileContent !== false && file_put_contents($uploadFile, $fileContent)) {
                    $success = true;
                    $method = 'file_put_contents';
                    echo "<p style='color:green'>✓ Archivo guardado correctamente usando file_put_contents.</p>";
                } else {
                    echo "<p style='color:red'>✗ Error al guardar el archivo usando file_put_contents.</p>";
                }
            }
        }
        
        // Resultado final
        if ($success) {
            echo "<h2>Resultado final</h2>";
            echo "<p style='color:green'>✓ Archivo subido correctamente usando " . $method . ".</p>";
            echo "<p>Ruta del archivo: <code>" . htmlspecialchars($uploadFile) . "</code></p>";
            
            // Mostrar la imagen
            $webPath = str_replace(__DIR__, '', $uploadFile);
            echo "<p>Vista previa de la imagen:</p>";
            echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
        } else {
            echo "<h2>Resultado final</h2>";
            echo "<p style='color:red'>✗ No se pudo subir el archivo usando ningún método.</p>";
            
            // Mostrar información de diagnóstico
            echo "<h3>Información de diagnóstico</h3>";
            echo "<ul>";
            echo "<li>Permisos del directorio: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</li>";
            echo "<li>Archivo temporal existe: " . (file_exists($_FILES['imagen']['tmp_name']) ? 'Sí' : 'No') . "</li>";
            echo "<li>Tamaño del archivo temporal: " . (file_exists($_FILES['imagen']['tmp_name']) ? filesize($_FILES['imagen']['tmp_name']) . ' bytes' : 'N/A') . "</li>";
            echo "<li>Es escribible el directorio: " . (is_writable($absoluteUploadDir) ? 'Sí' : 'No') . "</li>";
            echo "<li>Espacio libre en disco: " . disk_free_space('/') . " bytes</li>";
            echo "</ul>";
        }
    }
} else {
    // Mostrar formulario
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de subida directa de imágenes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        form {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }
        input[type="file"] {
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Prueba de subida directa de imágenes</h1>
    <p>Este script prueba la subida directa de imágenes sin usar la clase ImageProcessor.</p>
    
    <form action="" method="post" enctype="multipart/form-data">
        <div>
            <label for="imagen">Selecciona una imagen:</label>
            <input type="file" name="imagen" id="imagen" accept="image/*" required>
        </div>
        <div>
            <button type="submit">Subir imagen</button>
        </div>
    </form>
    
    <h2>Información del sistema</h2>
    <ul>
        <li>PHP Version: <?php echo phpversion(); ?></li>
        <li>Server Software: <?php echo $_SERVER['SERVER_SOFTWARE']; ?></li>
        <li>Document Root: <?php echo $_SERVER['DOCUMENT_ROOT']; ?></li>
        <li>Current Script: <?php echo __FILE__; ?></li>
    </ul>
    
    <h2>Límites de PHP para subida de archivos</h2>
    <ul>
        <li>upload_max_filesize: <?php echo ini_get('upload_max_filesize'); ?></li>
        <li>post_max_size: <?php echo ini_get('post_max_size'); ?></li>
        <li>memory_limit: <?php echo ini_get('memory_limit'); ?></li>
        <li>max_execution_time: <?php echo ini_get('max_execution_time'); ?> segundos</li>
        <li>max_file_uploads: <?php echo ini_get('max_file_uploads'); ?></li>
    </ul>
</body>
</html>
<?php
}
?>
