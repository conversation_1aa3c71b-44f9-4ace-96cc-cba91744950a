# Documentación de Sesiones Permanentes

Este documento describe la implementación de sesiones permanentes en el sistema TataRepuestosV2, los problemas detectados y las soluciones implementadas.

## Análisis del Problema

El cliente reportó que las sesiones se cerraban prematuramente a pesar de haber configurado que permanezcan abiertas de forma permanente. Tras un análisis exhaustivo, se identificaron los siguientes problemas:

1. **Inconsistencia en Peticiones AJAX**: Algunas peticiones fetch no incluían `credentials: 'include'`, lo que provocaba que no se enviaran las cookies de sesión.

2. **Falta de Renovación de Sesión**: No existía un mecanismo para renovar automáticamente la sesión durante períodos de inactividad.

3. **Configuración PHP Inconsistente**: Posible conflicto entre la configuración local y la configuración global de PHP para el tiempo de vida de las sesiones.

4. **Problemas de Seguridad**: La implementación original carecía de algunas medidas de seguridad importantes como `cookie_secure` y regeneración periódica del ID de sesión.

5. **Manejo de CSRF Incompleto**: Aunque existía un token CSRF en algunas partes, no se aplicaba de manera consistente.

## Soluciones Implementadas

### 1. Mejoras en la Configuración de Sesiones

- Se modificó `session_config.php` para incluir `session.cookie_secure = 1` para asegurar que las cookies de sesión solo se envíen por HTTPS.
- Se mantuvieron los valores de duración de 30 días para `session.cookie_lifetime` y `session.gc_maxlifetime`.
- Se creó `check_php_config.php` para verificar si las configuraciones locales son respetadas por PHP.

### 2. Regeneración Periódica del ID de Sesión

- Se implementó en `auth_check.php` la regeneración del ID de sesión cada hora de uso activo.
- Esta práctica de seguridad reduce el riesgo de ataques de fijación de sesión y robo de sesión.

```php
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 3600) {
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}
```

### 3. Sistema de Verificación y Renovación de Sesión

- Se creó `session_check.php` como endpoint para verificar el estado de la sesión.
- Se implementó `session-monitor.js` que realiza verificaciones periódicas de la sesión y envía peticiones "keep-alive" durante la actividad del usuario.
- El monitor detecta la actividad del usuario (clics, teclas, scroll) para mantener la sesión activa durante el uso.

### 4. Corrección de Credenciales en Peticiones AJAX

- Se modificó `quote-canvas.js` para incluir `credentials: 'include'` en todas las peticiones fetch.
- Se creó `fetch-helper.js` con funciones helper para estandarizar todas las peticiones AJAX:
  - `fetchWithCredentials()`: Base para todas las peticiones con credentials incluidas
  - `getWithCredentials()`: Para peticiones GET
  - `postJsonWithCredentials()`: Para peticiones POST con datos JSON
  - `postFormWithCredentials()`: Para peticiones POST con FormData

### 5. Mejora de Protección CSRF

- Se implementó `csrf_token.php` con un sistema completo para manejar tokens CSRF.
- Se integró la protección CSRF en el helper de fetch para incluir automáticamente el token en las cabeceras.
- Se proporcionaron funciones para generar campos hidden y meta tags con el token CSRF.

## Guía de Implementación

### Inclusión de Scripts en las Páginas

Agregar en el `<head>` de cada página autenticada:

```html
<!-- Incluir meta tag con token CSRF -->
<?php echo csrfTokenMeta(); ?>

<!-- Incluir scripts de gestión de sesiones -->
<script src="js/fetch-helper.js"></script>
<script src="js/session-monitor.js"></script>
```

### Uso del Helper de Fetch

Reemplazar las llamadas fetch en archivos JavaScript:

```javascript
// Antes
fetch('api/data.php');

// Después
getWithCredentials('api/data.php');

// Antes
fetch('api/save.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
});

// Después
postJsonWithCredentials('api/save.php', data);
```

### Protección CSRF en Páginas PHP

En cada script PHP que procese formularios o peticiones AJAX que modifiquen datos:

```php
require_once 'session_config.php';
session_start();
require_once 'csrf_token.php';

// Verificar sesión y token CSRF en un solo paso
validateSessionAndCsrf();

// Procesar la solicitud...
```

### Verificar la Configuración PHP

Ejecutar `check_php_config.php` en el servidor para verificar que la configuración de PHP respeta los valores establecidos localmente:

```
https://ejemplo.com/check_php_config.php
```

## Recomendaciones Adicionales

1. **Monitoreo Continuo**: Implementar logging detallado de la actividad de sesiones para identificar posibles problemas.

2. **Comunicación al Usuario**: Mostrar información sobre el estado de la sesión y ofrecer un botón para renovarla manualmente si es necesario.

3. **Almacenamiento en Base de Datos**: Considerar migrar el almacenamiento de sesiones a una base de datos para mayor control y persistencia.

4. **Re-autenticación para Operaciones Sensibles**: Implementar re-autenticación para operaciones críticas, incluso con sesiones permanentes.

5. **Actualización Gradual**: Migrar progresivamente todas las peticiones AJAX al nuevo sistema de fetch helper.

## Cómo Probar

1. **Verificación de Sesión Permanente**:
   - Iniciar sesión y cerrar el navegador
   - Reabrir el navegador y verificar que la sesión siga activa

2. **Verificación de Actividad Automática**:
   - Iniciar sesión y dejar la página abierta durante varias horas
   - Verificar que la sesión sigue activa (puede hacerse a través de `session_check.php`)

3. **Verificación de Seguridad**:
   - Intentar realizar operaciones sensibles sin el token CSRF adecuado
   - Confirmar que las peticiones son rechazadas con código 403

## Conclusión

Con estas mejoras, el sistema ahora debe mantener las sesiones activas de forma permanente, mientras se asegura un nivel adecuado de seguridad. Las sesiones no expirarán por inactividad, pero se renovarán periódicamente para evitar problemas de seguridad.

La solución implementada equilibra la necesidad de sesiones persistentes con las mejores prácticas de seguridad, reduciendo significativamente el riesgo de vulnerabilidades mientras se mantiene una experiencia de usuario fluida y sin interrupciones.