.products-grid {
    display: none;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* Reducido de 250px a 200px */
    gap: 1.5rem;
    padding: 1rem;
}

.products-table {
    display: none;
}

/* Estilos base para ambas vistas */
.products-grid, .products-table {
    display: none;
}

/* Las vistas se controlan desde JavaScript */

/* Estilos de tabla base */
.products-table {
    max-width: 1850px;
    margin: 2rem auto;
    padding: 0 1rem;
    overflow-x: auto;
}

.products-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Columnas con ancho específico */
.products-table th,
.products-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

/* Ancho específico para cada columna */
.products-table th[data-col="imagen"],
.products-table td:first-child {
    width: 100px;
}

.products-table th[data-col="marca"],
.products-table td:nth-child(2) {
    width: 120px;
}

.products-table th[data-col="modelo"],
.products-table td:nth-child(3) {
    width: 120px;
}

.products-table th[data-col="año"],
.products-table td:nth-child(4) {
    width: 80px;
}

.products-table th[data-col="motor"],
.products-table td:nth-child(5) {
    width: 120px;
}

.products-table th[data-col="descripcion"],
.products-table td:nth-child(6) {
    width: 250px;
}

.products-table th[data-col="precio"],
.products-table td:nth-child(7) {
    width: 100px;
}

.products-table th[data-col="cantidad"],
.products-table td:nth-child(8) {
    width: 150px;
    text-align: center;
}

.products-table th[data-col="accion"],
.products-table td:last-child {
    width: 120px;
}

.products-table th {
    background: var(--primary-color);
    color: white;
    white-space: nowrap;
}

.table-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

/* Estilos responsive */
@media (max-width: 768px) {
    /* Removed conflicting table styles */
    .products-grid {
        padding: 0.5rem;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Reducido de 250px a 200px */
        gap: 1rem;
    }

    .products-grid .product-card {
        width: auto;
        margin-bottom: 1rem;
    }

    .products-grid .product-info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .products-grid .product-details {
        flex: 1;
        padding-right: 1rem;
        max-width: 65%;
    }

    .products-grid .controls-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 35%;
        min-width: 120px;
        border-left: 1px solid #eee;
        padding-left: 1rem;
        margin-left: auto;
    }

    .products-grid .add-to-cart-btn {
        margin: 1rem 0;
        width: 100%;
        min-height: 40px;
    }

    .products-grid .quantity-controls {
        width: 100%;
        justify-content: center;
        margin: 0.5rem 0;
    }

    .products-table th {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 2;
        background: var(--primary-color);
    }

    .table-image {
        width: 60px;
        height: 60px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
    }

    .quantity-btn {
        width: 24px;
        height: 24px;
        padding: 0;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-to-cart-btn {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

.product-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 450px; /* Reducido de 550px a 450px */
}

.product-card .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-card .product-details {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.product-card .controls-container {

    background: white;
    border-top: 1px solid #eee;
    margin-top: auto;
}

.product-card .quantity-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.product-card .add-to-cart-btn {
    width: 100%;
    padding: 0.8rem;
    display: block;
}

.quantity-display {
    font-weight: 600;
    background-color: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 2px solid #2c3e50;
    color: #2c3e50;
    font-size: 1.1em;
}

/* Estilos para las etapas del carrito */
.cart-stage {
    display: none;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    height: 100%;
    overflow-y: auto;
}

.cart-stage.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem;
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1rem;
    cursor: pointer;
    margin: 1rem;
}

.back-btn:hover {
    color: var(--secondary-color);
}

.cart-actions {
    padding: 1rem;
    background: white;
    position: sticky;
    bottom: 0;
    border-top: 1px solid #eee;
}
