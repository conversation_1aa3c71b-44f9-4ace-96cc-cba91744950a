/**
 * Video Compressor - Comprime videos antes de subirlos
 */
class VideoCompressor {
    constructor(options = {}) {
        this.options = {
            maxSizeMB: options.maxSizeMB || 10,
            maxWidthOrHeight: options.maxWidthOrHeight || 1280,
            useWebWorker: options.useWebWorker !== undefined ? options.useWebWorker : true,
            fileType: options.fileType || 'video/mp4',
            quality: options.quality || 0.7,
            debug: options.debug || false
        };
        
        this.statusElement = options.statusElement || null;
        this.progressElement = options.progressElement || null;
    }
    
    /**
     * Actualiza el estado de la compresión
     */
    updateStatus(message) {
        if (this.statusElement) {
            this.statusElement.textContent = message;
        }
        
        if (this.options.debug) {
            console.log('[VideoCompressor]', message);
        }
    }
    
    /**
     * Actualiza el progreso de la compresión
     */
    updateProgress(percent) {
        if (this.progressElement) {
            this.progressElement.style.width = percent + '%';
            this.progressElement.textContent = percent + '%';
        }
    }
    
    /**
     * Comprime un video
     */
    async compress(file) {
        return new Promise((resolve, reject) => {
            if (!file || !/video\//.test(file.type)) {
                reject(new Error('El archivo no es un video válido'));
                return;
            }
            
            this.updateStatus('Preparando video para compresión...');
            
            // Verificar si el archivo ya es lo suficientemente pequeño
            if (file.size / 1024 / 1024 <= this.options.maxSizeMB) {
                this.updateStatus('El video ya es lo suficientemente pequeño, no se requiere compresión');
                resolve(file);
                return;
            }
            
            // Crear elementos para la compresión
            const video = document.createElement('video');
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Crear URL para el video
            const videoURL = URL.createObjectURL(file);
            
            // Configurar el video
            video.src = videoURL;
            video.muted = true;
            video.autoplay = false;
            video.controls = false;
            
            // Cuando los metadatos estén cargados
            video.onloadedmetadata = () => {
                this.updateStatus('Analizando video...');
                
                // Calcular dimensiones
                let width = video.videoWidth;
                let height = video.videoHeight;
                
                // Redimensionar si es necesario
                if (width > this.options.maxWidthOrHeight || height > this.options.maxWidthOrHeight) {
                    if (width > height) {
                        height = Math.round(height * this.options.maxWidthOrHeight / width);
                        width = this.options.maxWidthOrHeight;
                    } else {
                        width = Math.round(width * this.options.maxWidthOrHeight / height);
                        height = this.options.maxWidthOrHeight;
                    }
                }
                
                // Configurar el canvas
                canvas.width = width;
                canvas.height = height;
                
                // Duración del video
                const duration = video.duration;
                const totalFrames = Math.floor(duration * 30); // 30 fps
                
                this.updateStatus(`Comprimiendo video (${Math.round(duration)}s, ${width}x${height})...`);
                
                // Crear MediaRecorder
                const stream = canvas.captureStream(30);
                const recorder = new MediaRecorder(stream, {
                    mimeType: 'video/webm;codecs=vp9',
                    videoBitsPerSecond: 1000000 * this.options.quality // 1Mbps * calidad
                });
                
                const chunks = [];
                
                recorder.ondataavailable = (e) => {
                    if (e.data.size > 0) {
                        chunks.push(e.data);
                    }
                };
                
                recorder.onstop = () => {
                    this.updateStatus('Finalizando compresión...');
                    
                    // Liberar recursos
                    URL.revokeObjectURL(videoURL);
                    
                    // Crear Blob con los chunks
                    const blob = new Blob(chunks, { type: 'video/webm' });
                    
                    // Convertir a MP4 si es necesario (en este caso solo cambiamos el tipo)
                    const compressedFile = new File([blob], file.name.replace(/\.[^/.]+$/, '.webm'), {
                        type: 'video/webm',
                        lastModified: Date.now()
                    });
                    
                    this.updateStatus(`Compresión completada. Tamaño original: ${(file.size / 1024 / 1024).toFixed(2)}MB, Nuevo tamaño: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
                    this.updateProgress(100);
                    
                    resolve(compressedFile);
                };
                
                // Iniciar grabación
                recorder.start(100);
                
                // Reproducir el video y dibujar en el canvas
                video.currentTime = 0;
                video.play();
                
                let currentFrame = 0;
                
                const drawFrame = () => {
                    if (video.ended || video.paused) {
                        recorder.stop();
                        return;
                    }
                    
                    // Dibujar el frame actual
                    ctx.drawImage(video, 0, 0, width, height);
                    
                    // Actualizar progreso
                    currentFrame++;
                    const progress = Math.min(100, Math.round((currentFrame / totalFrames) * 100));
                    this.updateProgress(progress);
                    
                    // Solicitar el siguiente frame
                    requestAnimationFrame(drawFrame);
                };
                
                // Iniciar el proceso
                drawFrame();
            };
            
            // Manejar errores
            video.onerror = () => {
                URL.revokeObjectURL(videoURL);
                this.updateStatus('Error al cargar el video');
                reject(new Error('Error al cargar el video'));
            };
        });
    }
}

// Exportar la clase
window.VideoCompressor = VideoCompressor;
