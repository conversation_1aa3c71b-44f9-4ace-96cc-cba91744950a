<?php
/**
 * Archivo de verificación de autenticación
 * Incluir este archivo al principio de cada página que requiera autenticación
 */

// Incluir configuración de sesiones para prevenir cierre por inactividad
require_once 'session_config.php';
session_start();

// Verificar si el usuario está logueado
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    // Guardar la URL actual para redirigir después del login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];

    // Redirigir al login
    header('Location: login.php');
    exit;
}

// Comentado para eliminar la expiración por inactividad
// $session_timeout = 14 * 60 * 60; // 14 horas en segundos
// if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time'] > $session_timeout)) {
//     // Destruir la sesión
//     session_unset();
//     session_destroy();
//
//     // Iniciar una nueva sesión para mensajes
//     session_start();
//     $_SESSION['login_error'] = 'Su sesión ha expirado después de 2 horas de inactividad. Por favor, inicie sesión nuevamente.';
//
//     // Redirigir al login
//     header('Location: login.php');
//     exit;
// }

// Actualizar la hora de la última actividad
$_SESSION['login_time'] = time();

// Implementar regeneración periódica del ID de sesión para mayor seguridad
if (!isset($_SESSION['last_regeneration']) || (time() - $_SESSION['last_regeneration']) > 3600) {
    // Regenerar ID cada hora de uso activo
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
    error_log('Regenerando ID de sesión: ' . session_id());
}

// Función para verificar si el usuario tiene un rol específico
function userHasRole($role) {
    return isset($_SESSION['rol']) && $_SESSION['rol'] === $role;
}

// Función para verificar si el usuario tiene alguno de los roles especificados
function userHasAnyRole($roles) {
    if (!isset($_SESSION['rol'])) {
        return false;
    }

    return in_array($_SESSION['rol'], $roles);
}

// Función para redirigir si el usuario no tiene el rol requerido
function requireRole($role) {
    if (!userHasRole($role)) {
        // Redirigir a una página de acceso denegado
        header('Location: access_denied.php');
        exit;
    }
}

// Función para redirigir si el usuario no tiene alguno de los roles requeridos
function requireAnyRole($roles) {
    if (!userHasAnyRole($roles)) {
        // Redirigir a una página de acceso denegado
        header('Location: access_denied.php');
        exit;
    }
}
?>
