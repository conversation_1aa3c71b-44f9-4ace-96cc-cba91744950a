/**
 * Script independiente para ejecutar la herramienta de diagnóstico 
 * de sobres y facturas electrónicas
 */

// Función para ejecutar la herramienta de diagnóstico
function runDiagnosticTool() {
    // Mostrar indicador de carga
    $("#loader").show();
    
    // Limpiar logs anteriores
    clearLogs();
    
    // Mostrar el contenedor de logs
    $("#detailedLogsContainer").show();
    addLogEntry('info', 'Iniciando herramienta de diagnóstico');
    
    // Realizar la petición AJAX al script de diagnóstico
    $.ajax({
        url: 'check_xml_files.php',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $("#loader").hide();
            
            if (data.success) {
                // Registrar información del sistema
                addLogEntry('info', 'Información del sistema', data.system_info);
                
                // Registrar estado de directorios
                Object.keys(data.directorios).forEach(function(dir) {
                    const dirInfo = data.directorios[dir];
                    const dirExists = dirInfo.existe === 'Sí';
                    
                    if (dirExists) {
                        addLogEntry('success', `Directorio: ${dir}`, dirInfo);
                    } else {
                        addLogEntry('error', `Directorio no encontrado: ${dir}`, dirInfo);
                    }
                });
                
                // Registrar estado de archivos XML
                addLogEntry('info', `Documentos pendientes: ${data.documentos_pendientes_total}`);
                
                let xmlValidos = 0;
                let xmlNoEncontrados = 0;
                
                data.xml_documentos.forEach(function(xml) {
                    if (xml.existe === 'Sí') {
                        if (xml.es_xml_valido) {
                            xmlValidos++;
                            addLogEntry('success', `XML válido: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                        } else {
                            addLogEntry('warning', `XML con errores: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                        }
                    } else {
                        xmlNoEncontrados++;
                        addLogEntry('error', `XML no encontrado: ID ${xml.id}, Tipo DTE ${xml.tipo_dte}`, xml);
                    }
                });
                
                // Mostrar resumen
                let statusType = 'info';
                let statusMessage = '';
                
                if (xmlNoEncontrados > 0) {
                    statusType = 'error';
                    statusMessage = `Diagnóstico completado: ${xmlNoEncontrados} archivos XML no encontrados. Revise las rutas y permisos.`;
                    
                    // Sugerir posibles soluciones
                    const directorioPadre = data.xml_documentos[0]?.directorio_padre || 'N/A';
                    addLogEntry('info', 'Posibles soluciones:', [
                        `1. Verifique que el directorio '${directorioPadre}' existe y tiene permisos adecuados.`,
                        '2. Compruebe que las rutas en la base de datos corresponden a las ubicaciones reales de los archivos.',
                        '3. Asegúrese de que el usuario del servidor web tiene permisos de lectura en los directorios correspondientes.'
                    ]);
                } else if (xmlValidos < data.documentos_pendientes_total) {
                    statusType = 'warning';
                    statusMessage = `Diagnóstico completado: Algunos archivos XML (${data.documentos_pendientes_total - xmlValidos}) tienen errores de formato.`;
                } else {
                    statusType = 'success';
                    statusMessage = `Diagnóstico completado: Todos los archivos XML (${xmlValidos}) son válidos.`;
                }
                
                // Mostrar resultado general
                showMessage(statusType, statusMessage);
                
            } else {
                // Error en la ejecución del diagnóstico
                addLogEntry('error', 'Error en la herramienta de diagnóstico', {
                    error: data.error,
                    file: data.error_file,
                    line: data.error_line
                });
                
                showMessage('error', 'Error al ejecutar el diagnóstico: ' + data.error);
            }
        },
        error: function(xhr, status, error) {
            $("#loader").hide();
            
            let errorInfo = {
                status: status,
                error: error
            };
            
            // Tratar de extraer más información del error
            try {
                if (xhr.responseText) {
                    errorInfo.responseText = xhr.responseText;
                    if (xhr.responseText.includes('SQLSTATE[42S22]')) {
                        errorInfo.errorType = 'database_error';
                        errorInfo.solution = 'Hay un problema con la estructura de la base de datos. Es posible que falte la columna "fecha" en alguna tabla.';
                    }
                }
            } catch (e) {
                errorInfo.parseError = e.message;
            }
            
            addLogEntry('error', 'Error de conexión con el servidor', errorInfo);
            
            // Mensaje de error más descriptivo
            if (errorInfo.solution) {
                showMessage('error', 'Error al ejecutar el diagnóstico: ' + error + '. ' + errorInfo.solution, errorInfo);
            } else {
                showMessage('error', 'Error al conectar con el servidor: ' + error, errorInfo);
            }
        }
    });
}