<?php
// Archivo: enviar_dte.php
header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener el JSON enviado
$jsonData = $_POST['jsonData'] ?? '';

if (empty($jsonData)) {
    echo json_encode(['error' => 'Datos JSON no proporcionados']);
    exit;
}

// Rutas a los archivos
$certificadoPath = 'Documents/17365958-K.pfx';
$foliosPath = 'Documents/folios/Facturas/folios_facturas_10';

// Verificar que los archivos existen
if (!file_exists($certificadoPath)) {
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

if (!file_exists($foliosPath)) {
    echo json_encode(['error' => 'Archivo de folios no encontrado: ' . $foliosPath]);
    exit;
}

// Verificaciones adicionales
$jsonObj = json_decode($jsonData, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'error' => 'JSON inválido: ' . json_last_error_msg(),
        'json_preview' => substr($jsonData, 0, 200) . '...'
    ]);
    exit;
}

// Verificar tamaños de archivos
$certSize = filesize($certificadoPath);
$foliosSize = filesize($foliosPath);
if ($certSize === 0) {
    echo json_encode(['error' => 'El archivo de certificado está vacío']);
    exit;
}
if ($foliosSize === 0) {
    echo json_encode(['error' => 'El archivo de folios está vacío']);
    exit;
}

// Configurar la solicitud cURL
$ch = curl_init('https://api.simpleapi.cl/api/v1/dte/generar');

// Preparar el formulario multipart
$boundary = uniqid();
$delimiter = '-------------' . $boundary;

// Construir el cuerpo de la solicitud
$postData = '';

// Agregar el campo JSON
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
$postData .= $jsonData . "\r\n";

// Agregar el archivo de certificado
$fileContents = file_get_contents($certificadoPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Agregar el archivo de folios
$fileContents = file_get_contents($foliosPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($foliosPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Cerrar el cuerpo del mensaje
$postData .= "--" . $delimiter . "--\r\n";

// Configuración de cURL
$apiKey = $_POST['apiKey'] ?? '2037-N680-6391-2493-5987'; // Token de autorización
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_HTTPHEADER => [
        "Authorization: $apiKey",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($postData)
    ]
]);

// Añadir antes de curl_exec para verificar la solicitud
$requestInfo = [
    'url' => 'https://api.simpleapi.cl/api/v1/dte/generar',
    'json_length' => strlen($jsonData),
    'certificado_size' => filesize($certificadoPath),
    'folios_size' => filesize($foliosPath),
    'boundary' => $boundary
];
error_log("Enviando solicitud: " . json_encode($requestInfo));

// Ejecutar la solicitud
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
$curlInfo = curl_getinfo($ch);
curl_close($ch);

// Registrar información de la respuesta para diagnóstico
error_log("Respuesta API: Código=$httpCode, Tamaño=" . strlen($response));
error_log("Primeros 200 bytes: " . substr($response, 0, 200));

// Verificar si la respuesta parece ser XML
$isXml = strpos($response, '<?xml') !== false || strpos($response, '<DTE') !== false;
error_log("¿Parece XML? " . ($isXml ? 'Sí' : 'No'));

// Si hubo algún error con cURL
if ($curlError) {
    echo json_encode(['error' => 'Error al conectar con la API: ' . $curlError]);
    exit;
}

// Si la respuesta no fue exitosa
if ($httpCode !== 200) {
    // Intentar decodificar la respuesta para ver el mensaje de error detallado
    $errorDetails = json_decode($response, true);

    error_log("ERROR API SimpleAPI: Código HTTP $httpCode");
    error_log("Respuesta completa: " . $response);

    $errorMessage = 'Error al enviar el DTE. Código HTTP: ' . $httpCode;

    // Extraer mensaje de error más detallado si está disponible
    if ($errorDetails && isset($errorDetails['message'])) {
        $errorMessage .= ' - ' . $errorDetails['message'];
    } elseif ($errorDetails && isset($errorDetails['error'])) {
        $errorMessage .= ' - ' . $errorDetails['error'];
    }

    echo json_encode([
        'error' => $errorMessage,
        'response_details' => $errorDetails ?: $response,
        'request_info' => [
            'certificado_path' => $certificadoPath,
            'folios_path' => $foliosPath,
            'json_length' => strlen($jsonData)
        ]
    ]);
    exit;
}

// Si la respuesta fue exitosa, guardar el XML y registrar en la base de datos
require_once 'procesar_xml.php';

// Procesar la respuesta XML
$resultado = procesarRespuestaXML($response, $jsonData);

if ($resultado['success']) {
    // Extraer el tipo de documento del JSON enviado
    $jsonObj = json_decode($jsonData, true);
    $tipoDTE = null;

    if (isset($jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'])) {
        $tipoDTE = $jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'];
    }

    // Obtener el siguiente folio disponible para este tipo de documento
    $siguientefolio = null;
    $rutaArchivo = null;

    if ($tipoDTE !== null) {
        require_once 'db_connection.php';
        $conn = getConnection();

        // Llamar al stored procedure para obtener el siguiente folio
        $stmt = $conn->prepare("CALL obtener_siguiente_folio(?)");
        $stmt->bindParam(1, $tipoDTE, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && isset($result['folio'])) {
            $siguientefolio = $result['folio'];
            $rutaArchivo = $result['ruta_archivo'];
        }

        $stmt->closeCursor();

        // NUEVO: Almacenar información del receptor en la base de datos
        try {
            // Extraer información del receptor del JSON
            if (isset($jsonObj['Documento']['Encabezado']['Receptor'])) {
                $receptor = $jsonObj['Documento']['Encabezado']['Receptor'];

                // Verificar si el receptor ya existe (usando el RUT como identificador único)
                $stmt = $conn->prepare("SELECT id FROM tb_receptores WHERE rut = ?");
                $stmt->execute([$receptor['Rut']]);
                $receptorExistente = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($receptorExistente) {
                    // Actualizar receptor existente
                    $stmt = $conn->prepare(
                        "UPDATE tb_receptores
                         SET razon_social = ?, direccion = ?, comuna = ?,
                             giro = ?, contacto = ?
                         WHERE rut = ?"
                    );
                    $stmt->execute([
                        $receptor['RazonSocial'],
                        $receptor['Direccion'],
                        $receptor['Comuna'],
                        $receptor['Giro'] ?? null,
                        $receptor['Contacto'] ?? null,
                        $receptor['Rut']
                    ]);
                    $receptorId = $receptorExistente['id'];
                    $receptorAccion = 'actualizado';
                } else {
                    // Insertar nuevo receptor
                    $stmt = $conn->prepare(
                        "INSERT INTO tb_receptores
                         (rut, razon_social, direccion, comuna, giro, contacto)
                         VALUES (?, ?, ?, ?, ?, ?)"
                    );
                    $stmt->execute([
                        $receptor['Rut'],
                        $receptor['RazonSocial'],
                        $receptor['Direccion'],
                        $receptor['Comuna'],
                        $receptor['Giro'] ?? null,
                        $receptor['Contacto'] ?? null
                    ]);
                    $receptorId = $conn->lastInsertId();
                    $receptorAccion = 'registrado';
                }

                // Agregar información del receptor al resultado
                $resultado['receptor'] = [
                    'id' => $receptorId,
                    'rut' => $receptor['Rut'],
                    'accion' => $receptorAccion
                ];
            }
        } catch (Exception $e) {
            error_log("Error al guardar información del receptor: " . $e->getMessage());
            // No detener el proceso si falla el guardado del receptor
            $resultado['receptor_error'] = $e->getMessage();
        }
    }

    // NUEVO: Generar PDF a partir del XML
    $pdfResult = generarPDFDesdeXML($resultado['ruta']);

    echo json_encode([
        'success' => true,
        'mensaje' => 'DTE procesado y guardado correctamente',
        'archivo' => $resultado['archivo'],
        'ruta' => $resultado['ruta'],
        'siguiente_folio' => $siguientefolio,
        'ruta_caf' => $rutaArchivo,
        'receptor' => $resultado['receptor'] ?? null,
        'receptor_error' => $resultado['receptor_error'] ?? null,
        'pdf' => $pdfResult // Incluir la información del PDF generado
    ]);
} else {
    echo json_encode([
        'error' => $resultado['mensaje'],
        'detalles' => $resultado['detalles'] ?? null,
        'respuesta_api_tamano' => strlen($response),
        'respuesta_api_inicio' => substr($response, 0, 100),
        'respuesta_api_es_xml' => $isXml
    ]);
}

/**
 * Función para generar PDF a partir del archivo XML del DTE
 *
 * @param string $xmlPath Ruta al archivo XML
 * @return array Información sobre el PDF generado o error
 */
function generarPDFDesdeXML($xmlPath) {
    error_log("=== Iniciando generación de PDF ===");
    error_log("Ruta XML: " . $xmlPath);

    if (!file_exists($xmlPath)) {
        error_log("ERROR: Archivo XML no encontrado en: " . $xmlPath);
        return [
            'success' => false,
            'error' => 'Archivo XML no encontrado: ' . $xmlPath
        ];
    }

    // Preparar los datos para la API
    $apiKey = '2037-N680-6391-2493-5987';
    $apiUrl = 'https://api.simpleapi.cl/api/v1/impresion/pdf/80mm';

    // Datos dinámicos para el input
    $fechaActual = date('Y-m-d');
    $horaActual = date('H:i');

    $jsonInput = json_encode([
        'NumeroResolucion' => 0,
        'UnidadSII' => 'TEMUCO',
        'FechaResolucion' => $fechaActual,
        'Ejecutivo' => 'Matias Guajardo',
        'Hora' => $horaActual
    ]);
    error_log("JSON Input preparado: " . $jsonInput);

    // Verificar directorio PDF
    $pdfDir = 'Documents/PDF_88/'; // Cambiado según requerimiento
    error_log("Verificando directorio PDF: " . $pdfDir);

    if (!file_exists($pdfDir)) {
        error_log("Intentando crear directorio PDF");
        if (!mkdir($pdfDir, 0755, true)) {
            error_log("ERROR: No se pudo crear el directorio PDF");
            return [
                'success' => false,
                'error' => 'No se pudo crear el directorio para PDFs: ' . $pdfDir
            ];
        }
        error_log("Directorio PDF creado exitosamente");
    }

    // Preparar nombres de archivos
    $xmlFilename = basename($xmlPath);
    $pdfFilename = str_replace('.xml', '.pdf', $xmlFilename);
    $pdfPath = $pdfDir . $pdfFilename;
    error_log("Nombre archivo PDF a generar: " . $pdfFilename);

    // Preparar cURL
    $ch = curl_init($apiUrl);
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;

    // Construir el cuerpo multipart
    error_log("Preparando datos para envío a API");
    try {
        $fileContents = file_get_contents($xmlPath);
        error_log("XML leído correctamente, tamaño: " . strlen($fileContents) . " bytes");
        error_log("Primeros 200 caracteres del XML: " . substr($fileContents, 0, 200));
    } catch (Exception $e) {
        error_log("ERROR al leer archivo XML: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Error al leer el archivo XML: ' . $e->getMessage()
        ];
    }

    $postData = '';
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonInput . "\r\n";
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="fileEnvio"; filename="' . basename($xmlPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/xml' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";
    $postData .= "--" . $delimiter . "--\r\n";

    error_log("Tamaño total de datos a enviar: " . strlen($postData) . " bytes");
    error_log("Headers de la solicitud:");
    $headers = [
        "Authorization: $apiKey",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($postData)
    ];
    foreach ($headers as $header) {
        error_log($header);
    }

    // Configurar cURL con opciones adicionales para debugging
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_VERBOSE => true,
        CURLOPT_HEADER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);

    // Capturar información detallada de cURL
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    error_log("Enviando solicitud a API de PDF...");
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $curlInfo = curl_getinfo($ch);

    // Obtener información detallada de debug
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    error_log("Debug cURL: " . $verboseLog);

    error_log("Respuesta API PDF - Código HTTP: " . $httpCode);
    error_log("Información cURL completa: " . json_encode($curlInfo, JSON_PRETTY_PRINT));

    if ($curlError) {
        error_log("ERROR cURL: " . $curlError);
        curl_close($ch);
        return [
            'success' => false,
            'error' => 'Error en la solicitud cURL: ' . $curlError,
            'curl_info' => $curlInfo,
            'debug_log' => $verboseLog
        ];
    }

    // Separar headers y body de la respuesta
    $headerSize = $curlInfo['header_size'];
    $responseHeaders = substr($response, 0, $headerSize);
    $responseBody = substr($response, $headerSize);

    error_log("Headers de respuesta: " . $responseHeaders);
    error_log("Tamaño del body de respuesta: " . strlen($responseBody));

    curl_close($ch);

    if ($httpCode !== 200) {
        error_log("ERROR: Respuesta no exitosa de la API");
        error_log("Headers de respuesta completos: " . $responseHeaders);
        error_log("Body de respuesta (primeros 500 bytes): " . substr($responseBody, 0, 500));
        return [
            'success' => false,
            'error' => 'Error al generar el PDF. Código HTTP: ' . $httpCode,
            'response_headers' => $responseHeaders,
            'response_preview' => substr($responseBody, 0, 500)
        ];
    }

    // Verificar si la respuesta parece un PDF válido
    $isPDF = strpos($responseBody, '%PDF-') === 0;
    error_log("¿La respuesta parece un PDF válido? " . ($isPDF ? 'Sí' : 'No'));
    if (!$isPDF) {
        error_log("ERROR: La respuesta no parece ser un PDF válido");
        error_log("Primeros 100 bytes de la respuesta: " . bin2hex(substr($responseBody, 0, 100)));
        return [
            'success' => false,
            'error' => 'La respuesta no es un PDF válido',
            'response_preview' => bin2hex(substr($responseBody, 0, 100))
        ];
    }

    // Intentar guardar el PDF
    error_log("Intentando guardar PDF en: " . $pdfPath);
    if (file_put_contents($pdfPath, $responseBody) === false) {
        error_log("ERROR: No se pudo guardar el archivo PDF");
        error_log("Permisos del directorio: " . decoct(fileperms($pdfDir)));
        error_log("Espacio libre en disco: " . disk_free_space($pdfDir));
        return [
            'success' => false,
            'error' => 'Error al guardar el archivo PDF',
            'dir_perms' => decoct(fileperms($pdfDir)),
            'disk_space' => disk_free_space($pdfDir)
        ];
    }

    // Verificar el archivo guardado
    if (file_exists($pdfPath)) {
        $pdfSize = filesize($pdfPath);
        error_log("PDF guardado exitosamente. Tamaño: " . $pdfSize . " bytes");

        // Verificar que el archivo sea legible
        if ($pdfSize > 0) {
            $pdfCheck = file_get_contents($pdfPath, false, null, 0, 4);
            error_log("Primeros 4 bytes del PDF guardado: " . bin2hex($pdfCheck));
        }
    } else {
        error_log("ERROR: El archivo PDF no existe después de intentar guardarlo");
    }

    // Actualizar la base de datos
    try {
        require_once 'db_connection.php';
        $conn = getConnection();

        $sql = "UPDATE tb_facturas_dte
                SET archivo_pdf = ?
                WHERE nombre_archivo = ?";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$pdfFilename, $xmlFilename]);
        error_log("Base de datos actualizada correctamente");
        error_log("Archivo PDF: " . $pdfFilename);
        error_log("Archivo XML relacionado: " . $xmlFilename);

    } catch (Exception $e) {
        error_log("ERROR al actualizar la base de datos: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
    }

    error_log("=== Proceso de generación de PDF completado ===");
    return [
        'success' => true,
        'archivo_pdf' => $pdfFilename,
        'ruta_pdf' => $pdfPath,
        'tamaño_pdf' => filesize($pdfPath),
        'headers_respuesta' => $responseHeaders,
        'debug_info' => [
            'curl_info' => $curlInfo,
            'verbose_log' => $verboseLog
        ]
    ];
}
?>