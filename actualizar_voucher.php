<?php
// Activar la salida de errores para depuración
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Crear un archivo de registro para depuración
$logFile = 'voucher_error.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Inicio de la solicitud\n", FILE_APPEND);
file_put_contents($logFile, date('Y-m-d H:i:s') . " - POST: " . print_r($_POST, true) . "\n", FILE_APPEND);

// Incluir conexión a la base de datos
require_once 'db_connection.php';

// Obtener la conexión a la base de datos
$conn = getConnection();

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar si se proporcionaron los parámetros necesarios
if (!isset($_POST['dte_id']) || !isset($_POST['id_voucher'])) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error: Parámetros incompletos\n", FILE_APPEND);
    echo json_encode(['success' => false, 'message' => 'Parámetros incompletos']);
    exit;
}

// Obtener y validar los parámetros
$dteId = intval($_POST['dte_id']);
$idVoucher = trim($_POST['id_voucher']);

file_put_contents($logFile, date('Y-m-d H:i:s') . " - DTE ID: $dteId, ID Voucher: $idVoucher\n", FILE_APPEND);

// Validar que el ID del DTE sea válido
if ($dteId <= 0) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error: ID de DTE inválido\n", FILE_APPEND);
    echo json_encode(['success' => false, 'message' => 'ID de DTE inválido']);
    exit;
}

try {
    // Verificar si el registro existe
    $checkStmt = $conn->prepare("SELECT id FROM tb_facturas_dte WHERE id = ?");
    $checkStmt->execute([$dteId]);
    $exists = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if (!$exists) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error: El registro con ID $dteId no existe\n", FILE_APPEND);
        echo json_encode(['success' => false, 'message' => 'El registro no existe']);
        exit;
    }

    // Actualizar el campo id_voucher
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Ejecutando consulta UPDATE\n", FILE_APPEND);
    $stmt = $conn->prepare("UPDATE tb_facturas_dte SET id_voucher = ? WHERE id = ?");
    $result = $stmt->execute([$idVoucher, $dteId]);

    if ($result) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Actualización exitosa\n", FILE_APPEND);
        echo json_encode(['success' => true, 'message' => 'ID Voucher actualizado correctamente']);
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error al actualizar\n", FILE_APPEND);
        echo json_encode(['success' => false, 'message' => 'Error al actualizar el ID Voucher']);
    }
} catch (PDOException $e) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Excepción PDO: " . $e->getMessage() . "\n", FILE_APPEND);
    echo json_encode(['success' => false, 'message' => 'Error en la base de datos: ' . $e->getMessage()]);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - Fin de la solicitud\n\n", FILE_APPEND);

// Cerrar la conexión a la base de datos
closeConnection($conn);
?>
