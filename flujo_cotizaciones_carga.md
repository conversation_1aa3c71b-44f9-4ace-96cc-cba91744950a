# Ingeniería Inversa del Comportamiento 'Click-to-Slide' en el Módulo de Cotizaciones

Este documento detalla el comportamiento de deslizamiento lateral (click-to-slide) activado por el botón "Ver Cotizaciones" en `index.php` (líneas 1244-1246), que navega entre diferentes vistas dentro del módulo de cotizaciones.

## 1. Resumen Conciso del Cambio de Contenedor Lateral

El efecto de deslizamiento se logra mediante la manipulación de clases CSS (`show-list`, `show-detail`) en el elemento `.quote-slide-inner`. Este elemento es un contenedor flexible (`display: flex`) con un ancho total del 300% para albergar tres "niveles" o vistas. Cada nivel (`.quote-level`) ocupa el 33.333% del ancho del contenedor interno. Al añadir las clases mencionadas, se aplica una transformación `translateX` al `.quote-slide-inner`, desplazándolo horizontalmente para mostrar el nivel deseado. La transición es animada suavemente gracias a la propiedad `transition` de CSS.

## 2. Narrativa Paso a Paso del Evento

1.  **Interacción del Usuario**: El usuario hace clic en el botón "Ver Cotizaciones" en `index.php`.
    ```html
    <button class="action-button info" onclick="openQuotesList()">
        <i class="fas fa-clipboard-list"></i> Ver Cotizaciones
    </button>
    ```
2.  **Ejecución de `openQuotesList()`**: La función JavaScript `openQuotesList()` (definida en `js/quote-canvas.js`) es invocada.
3.  **Llamada a `navigateToLevel(2)`**: Dentro de `openQuotesList()`, se llama a `navigateToLevel(2)`, indicando que se debe navegar al segundo nivel (la lista de cotizaciones).
4.  **Actualización de `currentQuoteLevel`**: La variable global `currentQuoteLevel` se actualiza a `2`.
5.  **Manipulación de Clases CSS**: Dentro de `navigateToLevel(2)`:
    *   `slideInner.classList.add('show-list');` añade la clase `show-list` al elemento `slideInner` (que referencia a `.quote-slide-inner`).
    *   `slideInner.classList.remove('show-detail');` asegura que la clase `show-detail` no esté presente.
6.  **Activación de Transformación CSS**: La adición de la clase `show-list` al `.quote-slide-inner` activa la siguiente regla CSS:
    ```css
    .quote-slide-inner.show-list {
        transform: translateX(-33.333%);
    }
    ```
    Esto provoca que el contenedor `.quote-slide-inner` se desplace un 33.333% de su ancho total hacia la izquierda, revelando el segundo nivel (`.quote-level` de la lista de cotizaciones).
7.  **Transición Animada**: La propiedad `transition: transform 0.4s ease-in-out;` aplicada a `.quote-slide-inner` asegura que el desplazamiento sea una animación fluida de 0.4 segundos con una curva de aceleración y desaceleración.
8.  **Actualizaciones Adicionales de UI**: Simultáneamente con la animación, se realizan otras actualizaciones:
    *   El botón de retroceso (`backQuoteBtn`) se hace visible (`display: inline-block`).
    *   El título del módulo (`quoteTitle`) se actualiza a "Lista de Cotizaciones".
9.  **Carga de Datos (Nivel 2 - Lista)**: Después de la llamada a `navigateToLevel(2)`, `openQuotesList()` invoca `loadQuotesList()`, que realiza una petición a `list_quotes.php` para obtener y renderizar los datos de las cotizaciones en el nivel recién visible.
10. **Navegación a Detalle (Nivel 3)**: Cuando se hace clic en una tarjeta de cotización individual en la lista, se llama a `showQuoteDetail(id)`. Esta función realiza una petición a `get_quote_detail.php?id={id}` para obtener los detalles de esa cotización específica y luego invoca `navigateToLevel(3)` para mostrar el tercer nivel.

## 3. Fragmentos de Código Anotados

### HTML (de `index.php`)

```html
<!-- Botón que inicia el flujo -->
<button class="action-button info" onclick="openQuotesList()">
    <i class="fas fa-clipboard-list"></i> Ver Cotizaciones
</button>

<!-- Contenedor principal del sistema de niveles deslizantes -->
<div class="quote-slide-container">
    <div class="quote-slide-inner">
        <!-- Nivel 1: Formulario de cotización -->
        <div class="quote-level" id="quoteFormLevel">
            <!-- Contenido del formulario -->
        </div>
        <!-- Nivel 2: Lista de cotizaciones -->
        <div class="quote-level" id="quotesListLevel">
            <!-- Contenido de la lista de cotizaciones -->
        </div>
        <!-- Nivel 3: Detalle de cotización -->
        <div class="quote-level" id="quoteDetailLevel">
            <!-- Contenido del detalle de cotización -->
        </div>
    </div>
</div>
```

### JavaScript (de `js/quote-canvas.js`)

```javascript
// Variable global que referencia al contenedor interno que se desliza
const slideInner = document.querySelector('.quote-slide-inner');

// Función llamada por el botón "Ver Cotizaciones"
function openQuotesList() {
    navigateToLevel(2); // Navega al segundo nivel (lista de cotizaciones)
    loadQuotesList();    // Carga los datos de la lista de cotizaciones
}

// Función central para manejar la navegación entre niveles
function navigateToLevel(level) {
    currentQuoteLevel = level; // Actualiza el nivel actual

    switch(level) {
        case 1: // Nivel del formulario principal
            slideInner.classList.remove('show-list', 'show-detail'); // Asegura que no haya transformaciones
            backQuoteBtn.style.display = 'none';
            quoteTitle.textContent = 'Generar Cotización';
            break;
        case 2: // Nivel de la lista de cotizaciones
            slideInner.classList.add('show-list');    // Añade la clase para deslizar a la lista
            slideInner.classList.remove('show-detail'); // Remueve otras clases de transformación
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.textContent = 'Lista de Cotizaciones';
            break;
        case 3: // Nivel del detalle de cotización
            slideInner.classList.add('show-detail'); // Añade la clase para deslizar al detalle
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.textContent = 'Detalle de Cotización';
            break;
    }
}
```

### CSS (de `styles/quote.css`)

```css
/* Contenedor que define el área visible y oculta el desbordamiento */
.quote-slide-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden; /* Importante para ocultar los niveles fuera de vista */
}

/* Contenedor interno que se desliza */
.quote-slide-inner {
    display: flex; /* Permite que los niveles se coloquen uno al lado del otro */
    width: 300%;   /* Suficiente espacio para 3 niveles (100% cada uno) */
    height: 100%;
    /* Propiedad clave para la animación de deslizamiento */
    transition: transform 0.4s ease-in-out;
}

/* Cada nivel ocupa un tercio del ancho del contenedor interno */
.quote-level {
    width: 33.333%; /* 100% / 3 niveles */
    height: 100%;
    overflow-y: auto;
}

/* Estado cuando se muestra la lista de cotizaciones (segundo nivel) */
.quote-slide-inner.show-list {
    transform: translateX(-33.333%); /* Desplaza el contenedor a la izquierda */
}

/* Estado cuando se muestra el detalle de cotización (tercer nivel) */
.quote-slide-inner.show-detail {
    transform: translateX(-66.666%); /* Desplaza el contenedor aún más a la izquierda */
}
```

## 4. Diagrama de la Estructura del DOM Antes y Después de la Transición

### Antes (Nivel 1 - Formulario visible):

```
+-------------------------------------------------------------------------------------------------+
| .quote-slide-container (overflow: hidden)                                                       |
| +---------------------------------------------------------------------------------------------+ |
| | .quote-slide-inner (transform: translateX(0%); width: 300%)                                 | |
| | +---------------------+ +---------------------+ +---------------------+                   | |
| | | .quote-level        | | .quote-level        | | .quote-level        |                   | |
| | | (Formulario)        | | (Lista)             | | (Detalle)           |                   | |
| | |                     | |                     | |                     |                   | |
| | +---------------------+ +---------------------+ +---------------------+                   | |
| +---------------------------------------------------------------------------------------------+ |
+-------------------------------------------------------------------------------------------------+
```

### Después (Nivel 2 - Lista visible, tras `openQuotesList()`):

```
+-------------------------------------------------------------------------------------------------+
| .quote-slide-container (overflow: hidden)                                                       |
| +---------------------------------------------------------------------------------------------+ |
| | .quote-slide-inner (transform: translateX(-33.333%); width: 300%; .show-list)               | |
| |                   +---------------------+ +---------------------+ +---------------------+   | |
| |                   | .quote-level        | | .quote-level        | | .quote-level        |   |
| |                   | (Formulario)        | | (Lista)             | | (Detalle)           |   |
| |                   |                     | |                     | |                     |   |
| |                   +---------------------+ +---------------------+ +---------------------+   |
| +---------------------------------------------------------------------------------------------+ |
+-------------------------------------------------------------------------------------------------+
```

## 5. Detalles de Tiempo para Cada Fase

*   **Captura del Evento (`mousedown`/`click`)**: Instantáneo (milisegundos).
*   **Ejecución de JavaScript (`openQuotesList`, `navigateToLevel`)**: Muy rápido (pocos milisegundos), dependiendo de la complejidad del DOM y el motor JS.
*   **Recálculo de Estilos y Layout (Browser Layout/Recalculate Style)**: Ocurre cuando la clase `show-list` es añadida y el navegador calcula la nueva propiedad `transform`. Rápido, pero variable.
*   **Animación (`transition`)**: **0.4 segundos** (`0.4s ease-in-out`). Esta es la duración visible del deslizamiento.
*   **Pintado (`Paint`)**: El navegador pinta los nuevos píxeles a medida que la animación progresa. Continuo durante los 0.4 segundos de la transición.

## 6. Checklist de Posibles Efectos Secundarios o Regresiones a Verificar Durante QA

*   **Funcionalidad del botón "Volver"**:
    *   ¿Aparece correctamente el botón "Volver" (`backQuoteBtn`)?
    *   ¿Funciona correctamente al hacer clic, volviendo al nivel anterior (`navigateToLevel(1)`)?
*   **Carga de la lista de cotizaciones**:
    *   ¿Se cargan y renderizan correctamente las cotizaciones después del deslizamiento?
    *   ¿Qué ocurre si la carga de datos falla o tarda mucho? (Manejo de errores, spinners de carga).
*   **Navegación entre niveles**:
    *   ¿La navegación al "Detalle de Cotización" (`navigateToLevel(3)`) funciona correctamente desde la lista?
    *   ¿El deslizamiento es suave en todas las transiciones (1->2, 2->1, 2->3, 3->2)?
*   **Rendimiento**:
    *   ¿La animación es fluida en diferentes dispositivos y condiciones de red?
    *   ¿Hay algún "jank" o interrupción en la animación?
*   **Responsividad**:
    *   ¿El diseño se adapta correctamente a diferentes tamaños de pantalla durante y después de la transición?
*   **Accesibilidad**:
    *   ¿La navegación es accesible para usuarios con teclados o lectores de pantalla?
*   **Estado de la aplicación**:
    *   ¿Se mantiene el estado correcto de la aplicación después de navegar entre niveles (por ejemplo, datos del formulario, filtros de búsqueda)?
*   **Interacciones simultáneas**:
    *   ¿Qué sucede si el usuario hace clic rápidamente varias veces en el botón o en otros elementos durante la animación?
*   **Compatibilidad con navegadores**:
    *   ¿El comportamiento es consistente en los navegadores soportados?

## 7. Endpoints Utilizados

Para poblar los diferentes niveles del módulo de cotizaciones, se utilizan los siguientes endpoints (archivos PHP) en el servidor:

*   **Nivel 2 (Lista de Cotizaciones)**:
    *   **Endpoint:** `list_quotes.php`
    *   **Función JS que lo invoca:** `loadQuotesList()`
    *   **Descripción:** Recupera una lista de todas las cotizaciones guardadas en la base de datos para mostrarlas en el segundo nivel.

*   **Nivel 3 (Detalle de Cotización)**:
    *   **Endpoint:** `get_quote_detail.php`
    *   **Función JS que lo invoca:** `showQuoteDetail(id)`
    *   **Descripción:** Recupera los detalles completos de una cotización específica, identificada por su `id`, para mostrarlos en el tercer nivel.
