<?php
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Verificar si la tabla ya existe
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tb_dte_productos'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        // Crear la tabla tb_dte_productos
        $sql = "CREATE TABLE tb_dte_productos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dte_id INT NOT NULL,
            repuesto_id INT NOT NULL,
            cantidad INT NOT NULL,
            precio_unitario DECIMAL(10, 2) NOT NULL,
            monto_item DECIMAL(10, 2) NOT NULL,
            fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (dte_id) REFERENCES tb_facturas_dte(id) ON DELETE CASCADE            
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
        echo json_encode(['status' => 'success', 'message' => 'Tabla tb_dte_productos creada exitosamente']);
    } else {
        echo json_encode(['status' => 'info', 'message' => 'La tabla tb_dte_productos ya existe']);
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Error al crear la tabla: ' . $e->getMessage()]);
}
?>
