<?php
header('Content-Type: application/json');

$directories = [
    'Documents/sobreEnvio',
    'Documents/DTE/Facturas',
    'Documents/DTE/Boletas',
    'Documents/PDF_88'
];

$results = [];
$allOk = true;

foreach ($directories as $dir) {
    // Crear directorio si no existe
    if (!file_exists($dir)) {
        if (!mkdir($dir, 0777, true)) {
            $results[$dir] = [
                'exists' => false,
                'created' => false,
                'writable' => false,
                'status' => 'error',
                'message' => 'No se pudo crear el directorio'
            ];
            $allOk = false;
            continue;
        }
        chmod($dir, 0777);
    }
    
    // Comprobar si es escribible
    $isWritable = is_writable($dir);
    
    // Intentar hacer una prueba de escritura
    $testFile = "$dir/test_write_" . time() . ".tmp";
    $writeTest = @file_put_contents($testFile, "Test");
    
    if ($writeTest !== false) {
        @unlink($testFile);
    }
    
    $results[$dir] = [
        'exists' => true,
        'permissions' => decoct(fileperms($dir) & 0777),
        'writable' => $isWritable,
        'write_test' => ($writeTest !== false),
        'status' => ($isWritable && $writeTest !== false) ? 'ok' : 'warning',
        'owner' => function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($dir))['name'] : 'unknown',
        'group' => function_exists('posix_getgrgid') ? posix_getgrgid(filegroup($dir))['name'] : 'unknown'
    ];
    
    if (!$isWritable || $writeTest === false) {
        $allOk = false;
    }
}

echo json_encode([
    'success' => $allOk,
    'directories' => $results,
    'php_user' => function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid())['name'] : 'unknown',
    'timestamp' => date('Y-m-d H:i:s')
]);
?>
