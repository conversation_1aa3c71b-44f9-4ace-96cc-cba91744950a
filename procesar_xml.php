<?php
/**
 * Función para procesar la respuesta XML de la API, guardarla como archivo
 * y registrarla en la base de datos MySQL
 */
function procesarRespuestaXML($xmlContent, $jsonData) {
    try {
        // Parseamos el XML solo para obtener el tipo y folio
        $xml = new SimpleXMLElement($xmlContent);

        // Obtener el tipo de documento y folio
        $tipoDTE = '';
        $folio = '';

        if (isset($xml->Documento->Encabezado->IdDoc->TipoDTE)) {
            $tipoDTE = (string)$xml->Documento->Encabezado->IdDoc->TipoDTE;
        }

        if (isset($xml->Documento->Encabezado->IdDoc->Folio)) {
            $folio = (string)$xml->Documento->Encabezado->IdDoc->Folio;
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'mensaje' => 'Error al procesar el XML: ' . $e->getMessage()
        ];
    }

    // Determinar directorio basado en el tipo de documento
    switch ($tipoDTE) {
        case '33': // Factura Electrónica (33)
        case '34': // Factura Exenta (34)
            $directorio = 'Documents/DTE/Facturas/';
            break;
        case '39': // Boleta (39)
            $directorio = 'Documents/DTE/Boletas/';
            break;
        case '61': // Nota de Crédito (61)
            $directorio = 'Documents/DTE/NotasCredito/';
            break;
        default:
            $directorio = 'Documents/DTE/Otros/';
            break;
    }

    // Crear directorio si no existe
    if (!file_exists($directorio)) {
        if (!mkdir($directorio, 0755, true)) {
            return [
                'success' => false,
                'mensaje' => 'Error al crear el directorio: ' . $directorio
            ];
        }
    }

    // Generar nombre de archivo
    $fecha = date('Ymd_His');
    $nombreArchivo = "DTE_{$tipoDTE}_{$folio}_{$fecha}.xml";
    $rutaCompleta = $directorio . $nombreArchivo;

    // Guardar el XML exactamente como se recibió, sin modificaciones
    $bytesWritten = file_put_contents($rutaCompleta, $xmlContent);

    if ($bytesWritten === false) {
        return [
            'success' => false,
            'mensaje' => 'Error al guardar el archivo XML'
        ];
    }

    // Conectar a la base de datos y registrar
    require_once 'db_connection.php';

    try {
        $conn = getConnection();

        // Primero verificar si ya existe un registro con el mismo tipo_dte y folio
        $checkSql = "SELECT COUNT(*) FROM tb_facturas_dte WHERE tipo_dte = :tipo_dte AND folio = :folio";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bindParam(':tipo_dte', $tipoDTE);
        $checkStmt->bindParam(':folio', $folio);
        $checkStmt->execute();

        $count = $checkStmt->fetchColumn();

        if ($count > 0) {
            error_log("ERROR: Ya existe un registro con tipo_dte=$tipoDTE y folio=$folio");
            return [
                'success' => false,
                'mensaje' => 'Error al registrar en la base de datos: Ya existe un registro con el mismo tipo de documento y folio',
                'detalles' => "Tipo DTE: $tipoDTE, Folio: $folio"
            ];
        }

        // Si no existe, proceder con la inserción
        error_log("Insertando nuevo registro en tb_facturas_dte: tipo_dte=$tipoDTE, folio=$folio");

        $sql = "INSERT INTO tb_facturas_dte (
                    nombre_archivo,
                    tipo_dte,
                    folio,
                    json_enviado,
                    fecha_generacion,
                    estado_sobre,
                    id_sobre
                ) VALUES (
                    :nombre_archivo,
                    :tipo_dte,
                    :folio,
                    :json_enviado,
                    NOW(),
                    0,
                    NULL
                )";

        $stmt = $conn->prepare($sql);

        $stmt->bindParam(':nombre_archivo', $rutaCompleta);
        $stmt->bindParam(':tipo_dte', $tipoDTE);
        $stmt->bindParam(':folio', $folio);
        $stmt->bindParam(':json_enviado', $jsonData);

        $stmt->execute();

        // Obtener el ID del registro insertado
        $dteId = $conn->lastInsertId();

        return [
            'success' => true,
            'archivo' => $nombreArchivo,
            'ruta' => $rutaCompleta,
            'dte_id' => $dteId
        ];

    } catch (PDOException $e) {
        return [
            'success' => false,
            'mensaje' => 'Error al registrar en la base de datos: ' . $e->getMessage()
        ];
    }
}
?>
