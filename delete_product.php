<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener el ID del producto a eliminar
$productId = isset($_POST['id']) ? intval($_POST['id']) : 0;

if ($productId <= 0) {
    echo json_encode(['status' => 'error', 'message' => 'ID de producto no válido']);
    exit;
}

try {
    $conn = getConnection();

    // Iniciar transacción
    $conn->beginTransaction();

    // Obtener la ruta de la imagen asociada al producto antes de eliminarlo
    $stmt = $conn->prepare("SELECT url_imagen FROM repuesto WHERE id = ?");
    $stmt->execute([$productId]);
    $imageUrl = $stmt->fetchColumn();

    // Primero, eliminar registros relacionados en otras tablas
    // 1. Eliminar compatibilidades
    $stmt = $conn->prepare("DELETE FROM repuesto_compatible WHERE repuesto_id = ?");
    $stmt->execute([$productId]);

    // 2. Eliminar registros de stock
    $stmt = $conn->prepare("DELETE FROM stock WHERE repuesto_id = ?");
    $stmt->execute([$productId]);

    // 3. Eliminar movimientos de inventario
    $stmt = $conn->prepare("DELETE FROM movimiento_inventario WHERE repuesto_id = ?");
    $stmt->execute([$productId]);

    // 4. Marcar como inactivo en lugar de eliminar físicamente
    $stmt = $conn->prepare("UPDATE repuesto SET activo = 0 WHERE id = ?");
    $stmt->execute([$productId]);

    // Confirmar transacción
    $conn->commit();

    // Eliminar la imagen asociada al producto si existe
    if (!empty($imageUrl)) {
        $fullImagePath = __DIR__ . '/' . $imageUrl;
        error_log('Verificando imagen a eliminar: ' . $fullImagePath);
        if (file_exists($fullImagePath)) {
            error_log('Eliminando imagen: ' . $fullImagePath);
            @unlink($fullImagePath);
        }
    }

    echo json_encode(['status' => 'success', 'message' => 'Producto eliminado correctamente']);

} catch (PDOException $e) {
    // Revertir cambios en caso de error
    if ($conn) {
        $conn->rollBack();
    }

    echo json_encode([
        'status' => 'error',
        'message' => 'Error al eliminar el producto: ' . $e->getMessage()
    ]);
}
?>
