// Script para manejar el menú desplegable de usuario
document.addEventListener('DOMContentLoaded', function() {
    const userIcon = document.querySelector('.fa-user');
    const userDropdown = document.querySelector('.user-dropdown');
    
    if (userIcon && userDropdown) {
        // Manejar clic en el icono de usuario
        userIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('active');
        });
        
        // Cerrar el menú al hacer clic fuera de él
        document.addEventListener('click', function(e) {
            if (!userDropdown.contains(e.target) && !userIcon.contains(e.target)) {
                userDropdown.classList.remove('active');
            }
        });
        
        // Agregar efecto hover al icono de usuario
        userIcon.addEventListener('mouseenter', function() {
            this.style.color = '#e74c3c';
        });
        
        userIcon.addEventListener('mouseleave', function() {
            this.style.color = '';
        });
    }
});
