<?php
// Habilitar todos los errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_connection.php';

// Configurar encabezados CORS para permitir solicitudes desde cualquier origen durante la depuración
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept');

// Configurar encabezados de respuesta
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Registrar la solicitud para depuración
error_log("Solicitud recibida en get_subcategorias.php: " . json_encode($_GET));

// Verificar si se proporcionó el ID de categoría
if (!isset($_GET['categoria_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Categoría ID no proporcionado']);
    error_log("Error: Categoría ID no proporcionado");
    exit;
}

// Verificar si el ID de categoría es numérico
if (!is_numeric($_GET['categoria_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Categoría ID no válido']);
    error_log("Error: Categoría ID no válido: " . $_GET['categoria_id']);
    exit;
}

try {
    // Obtener conexión a la base de datos
    $conn = getConnection();
    $categoria_id = (int)$_GET['categoria_id'];

    error_log("Buscando subcategorías para categoria_id: " . $categoria_id);

    // Consulta para obtener subcategorías
    $stmt = $conn->prepare("SELECT id, nombre FROM categoria_repuesto WHERE categoria_padre_id = ? ORDER BY nombre");
    $stmt->execute([$categoria_id]);
    $subcategorias = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Registrar resultados
    error_log("Subcategorías encontradas: " . count($subcategorias));
    error_log("Datos: " . json_encode($subcategorias));

    // Verificar si se encontraron subcategorías
    if (empty($subcategorias)) {
        error_log("No se encontraron subcategorías para la categoría ID: " . $categoria_id);
        // Devolver un array vacío en lugar de un error
        echo json_encode([]);
    } else {
        // Devolver las subcategorías encontradas
        echo json_encode($subcategorias);
    }
} catch(Exception $e) {
    // Registrar y devolver error
    error_log("Error en get_subcategorias.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'error' => 'Error al obtener subcategorías',
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>