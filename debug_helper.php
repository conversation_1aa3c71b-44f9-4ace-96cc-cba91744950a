<?php
/**
 * Archivo: debug_helper.php
 * Descripción: Funciones auxiliares para depuración y registro detallado de errores
 */

class DebugHelper {
    // Niveles de log
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';
    
    // Colores para los diferentes niveles de log
    private static $levelColors = [
        self::LEVEL_DEBUG => '#9C27B0',    // Morado
        self::LEVEL_INFO => '#2196F3',     // Azul
        self::LEVEL_WARNING => '#FF9800',  // Naranja
        self::LEVEL_ERROR => '#F44336',    // Rojo
        self::LEVEL_CRITICAL => '#B71C1C', // Rojo oscuro
    ];
    
    // Almacena los logs en memoria
    private static $logs = [];
    
    // Directorio para archivos de log
    private static $logDir = 'logs';
    
    // Archivo de log actual
    private static $logFile = null;
    
    // Indica si se debe mostrar los logs en la salida
    private static $outputToScreen = false;
    
    /**
     * Inicializa el sistema de logs
     * 
     * @param string $logDir Directorio donde se guardarán los logs
     * @param bool $outputToScreen Si es true, muestra los logs en la salida
     */
    public static function init($logDir = 'logs', $outputToScreen = false) {
        self::$logDir = $logDir;
        self::$outputToScreen = $outputToScreen;
        self::$logs = [];
        
        // Crear directorio de logs si no existe
        if (!file_exists(self::$logDir)) {
            mkdir(self::$logDir, 0777, true);
        }
        
        // Crear archivo de log con fecha y hora
        $date = date('Y-m-d_H-i-s');
        self::$logFile = self::$logDir . "/debug_log_{$date}.log";
        
        // Registrar inicio de sesión
        self::log('Iniciando sesión de depuración', self::LEVEL_INFO, [
            'fecha' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'desconocida',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'desconocido',
            'php_version' => PHP_VERSION,
            'server' => $_SERVER['SERVER_NAME'] ?? 'desconocido'
        ]);
    }
    
    /**
     * Registra un mensaje en el log
     * 
     * @param string $message Mensaje a registrar
     * @param string $level Nivel del mensaje (debug, info, warning, error, critical)
     * @param mixed $data Datos adicionales (array, objeto, string, etc.)
     * @param bool $forceOutput Fuerza la salida a pantalla independientemente de la configuración
     */
    public static function log($message, $level = self::LEVEL_INFO, $data = null, $forceOutput = false) {
        // Si no se ha inicializado, inicializar con valores por defecto
        if (empty(self::$logs)) {
            self::init();
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $microtime = microtime(true);
        
        // Crear entrada de log
        $entry = [
            'timestamp' => $timestamp,
            'microtime' => $microtime,
            'level' => $level,
            'message' => $message,
            'data' => $data,
            'backtrace' => ($level === self::LEVEL_ERROR || $level === self::LEVEL_CRITICAL) ? 
                           self::getBacktrace() : null
        ];
        
        // Almacenar en memoria
        self::$logs[] = $entry;
        
        // Escribir en archivo
        self::writeToFile($entry);
        
        // Mostrar en pantalla si está configurado o forzado
        if (self::$outputToScreen || $forceOutput) {
            echo self::formatLogEntryHtml($entry);
            if (function_exists('ob_flush')) {
                ob_flush();
                flush();
            }
        }
        
        // Para errores críticos, también usar error_log de PHP
        if ($level === self::LEVEL_CRITICAL) {
            $dataStr = '';
            if ($data !== null) {
                $dataStr = is_string($data) ? $data : json_encode($data, JSON_UNESCAPED_UNICODE);
            }
            error_log("CRITICAL: {$message} - {$dataStr}");
        }
    }
    
    /**
     * Obtiene una traza de la pila de llamadas
     * 
     * @param int $limit Número máximo de niveles a mostrar
     * @return array Información de la pila de llamadas
     */
    private static function getBacktrace($limit = 10) {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, $limit + 1);
        // Eliminar la primera entrada que corresponde a esta función
        array_shift($backtrace);
        // Eliminar la segunda entrada que corresponde a la función log
        array_shift($backtrace);
        
        $result = [];
        foreach ($backtrace as $index => $trace) {
            if ($index >= $limit) break;
            
            $result[] = [
                'file' => $trace['file'] ?? 'unknown',
                'line' => $trace['line'] ?? 0,
                'function' => $trace['function'] ?? 'unknown',
                'class' => $trace['class'] ?? null
            ];
        }
        
        return $result;
    }
    
    /**
     * Escribe una entrada de log en el archivo
     * 
     * @param array $entry Entrada de log a escribir
     */
    private static function writeToFile($entry) {
        if (!self::$logFile) return;
        
        $line = "[{$entry['timestamp']}] [{$entry['level']}] {$entry['message']}";
        
        if ($entry['data'] !== null) {
            $dataStr = is_string($entry['data']) ? $entry['data'] : json_encode($entry['data'], JSON_UNESCAPED_UNICODE);
            $line .= " - Data: {$dataStr}";
        }
        
        if ($entry['backtrace']) {
            $line .= "\nBacktrace:";
            foreach ($entry['backtrace'] as $index => $trace) {
                $line .= "\n  #{$index} {$trace['file']}:{$trace['line']} - ";
                if ($trace['class']) {
                    $line .= "{$trace['class']}::";
                }
                $line .= "{$trace['function']}()";
            }
        }
        
        $line .= "\n";
        
        file_put_contents(self::$logFile, $line, FILE_APPEND);
    }
    
    /**
     * Formatea una entrada de log para HTML
     * 
     * @param array $entry Entrada de log a formatear
     * @return string HTML formateado
     */
    public static function formatLogEntryHtml($entry) {
        $color = self::$levelColors[$entry['level']] ?? '#757575';
        $levelUpper = strtoupper($entry['level']);
        
        $html = "<div class='log-entry {$entry['level']}' style='margin: 5px 0; padding: 10px; border-left: 4px solid {$color}; background: #f8f9fa; border-radius: 4px;'>";
        $html .= "<div style='display: flex; justify-content: space-between;'>";
        $html .= "<span style='color: #666;'>[{$entry['timestamp']}]</span>";
        $html .= "<span style='color: {$color}; font-weight: bold;'>{$levelUpper}</span>";
        $html .= "</div>";
        
        $html .= "<div style='margin: 5px 0; font-weight: 500;'>{$entry['message']}</div>";
        
        if ($entry['data'] !== null) {
            $html .= "<div class='log-data' style='margin-top: 5px; padding: 8px; background: #f1f1f1; border-radius: 3px; font-family: monospace; font-size: 13px;'>";
            if (is_array($entry['data']) || is_object($entry['data'])) {
                $html .= "<pre style='margin: 0; overflow-x: auto;'>" . htmlspecialchars(json_encode($entry['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
            } else {
                $html .= htmlspecialchars($entry['data']);
            }
            $html .= "</div>";
        }
        
        if ($entry['backtrace']) {
            $html .= "<div class='log-backtrace' style='margin-top: 10px;'>";
            $html .= "<details>";
            $html .= "<summary style='cursor: pointer; color: #0066cc;'>Ver backtrace</summary>";
            $html .= "<div style='margin-top: 5px; padding: 8px; background: #f1f1f1; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
            foreach ($entry['backtrace'] as $index => $trace) {
                $html .= "<div style='margin: 2px 0;'>";
                $html .= "#{$index} <span style='color: #0066cc;'>{$trace['file']}:{$trace['line']}</span> - ";
                if ($trace['class']) {
                    $html .= "<span style='color: #6f42c1;'>{$trace['class']}::</span>";
                }
                $html .= "<span style='color: #e83e8c;'>{$trace['function']}()</span>";
                $html .= "</div>";
            }
            $html .= "</div>";
            $html .= "</details>";
            $html .= "</div>";
        }
        
        $html .= "</div>";
        
        return $html;
    }
    
    /**
     * Obtiene todos los logs almacenados
     * 
     * @return array Logs almacenados
     */
    public static function getLogs() {
        return self::$logs;
    }
    
    /**
     * Limpia los logs almacenados
     */
    public static function clear() {
        self::$logs = [];
    }
    
    /**
     * Analiza una respuesta cURL y registra información detallada
     * 
     * @param resource $ch Recurso cURL
     * @param string $response Respuesta obtenida
     * @param string $url URL de la solicitud
     * @return array Información detallada de la respuesta
     */
    public static function analyzeCurlResponse($ch, $response, $url) {
        $info = curl_getinfo($ch);
        $error = curl_error($ch);
        $errno = curl_errno($ch);
        
        $result = [
            'url' => $url,
            'http_code' => $info['http_code'],
            'content_type' => $info['content_type'] ?? 'desconocido',
            'total_time' => $info['total_time'] ?? 0,
            'size_download' => $info['size_download'] ?? 0,
            'speed_download' => $info['speed_download'] ?? 0,
            'error' => $error,
            'errno' => $errno,
            'response_size' => strlen($response),
            'response_preview' => substr($response, 0, 500) . (strlen($response) > 500 ? '...' : ''),
        ];
        
        // Intentar decodificar la respuesta si es JSON
        if (strpos($info['content_type'] ?? '', 'application/json') !== false) {
            $jsonData = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $result['json_decoded'] = $jsonData;
            } else {
                $result['json_error'] = json_last_error_msg();
            }
        }
        
        // Intentar analizar la respuesta si es XML
        if (strpos($info['content_type'] ?? '', 'application/xml') !== false || 
            strpos($info['content_type'] ?? '', 'text/xml') !== false ||
            strpos($response, '<?xml') === 0) {
            
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($response);
            if ($xml !== false) {
                $result['is_valid_xml'] = true;
                $result['xml_root'] = $xml->getName();
                
                // Buscar elementos específicos como TrackID
                if (isset($xml->TrackId)) {
                    $result['track_id'] = (string)$xml->TrackId;
                } elseif (isset($xml->TRACKID)) {
                    $result['track_id'] = (string)$xml->TRACKID;
                }
            } else {
                $result['is_valid_xml'] = false;
                $xmlErrors = libxml_get_errors();
                $result['xml_errors'] = [];
                foreach ($xmlErrors as $error) {
                    $result['xml_errors'][] = [
                        'code' => $error->code,
                        'message' => $error->message,
                        'line' => $error->line
                    ];
                }
                libxml_clear_errors();
            }
        }
        
        // Registrar en el log
        $logLevel = ($info['http_code'] >= 200 && $info['http_code'] < 300) ? 
                    self::LEVEL_INFO : self::LEVEL_WARNING;
                    
        if ($error || $errno) {
            $logLevel = self::LEVEL_ERROR;
        }
        
        self::log("Respuesta cURL analizada", $logLevel, $result);
        
        return $result;
    }
    
    /**
     * Verifica permisos de directorios y archivos
     * 
     * @param array $paths Rutas a verificar
     * @return array Resultados de la verificación
     */
    public static function checkPermissions($paths) {
        $results = [];
        
        foreach ($paths as $path) {
            $result = [
                'path' => $path,
                'exists' => file_exists($path),
                'is_dir' => is_dir($path),
                'is_file' => is_file($path),
                'is_readable' => is_readable($path),
                'is_writable' => is_writable($path),
            ];
            
            if ($result['exists']) {
                $result['permissions'] = substr(sprintf('%o', fileperms($path)), -4);
                if ($result['is_dir']) {
                    $result['is_executable'] = is_executable($path);
                }
            }
            
            $results[$path] = $result;
        }
        
        self::log("Verificación de permisos", self::LEVEL_INFO, $results);
        
        return $results;
    }
    
    /**
     * Verifica la conexión a la base de datos
     * 
     * @param PDO $conn Conexión PDO
     * @return array Resultados de la verificación
     */
    public static function checkDatabase($conn) {
        $result = [
            'connected' => false,
            'version' => null,
            'tables' => [],
            'error' => null
        ];
        
        try {
            if ($conn instanceof PDO) {
                $result['connected'] = true;
                $result['version'] = $conn->getAttribute(PDO::ATTR_SERVER_VERSION);
                
                // Obtener lista de tablas
                $stmt = $conn->query("SHOW TABLES");
                if ($stmt) {
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    $result['tables'] = $tables;
                    
                    // Verificar tablas específicas
                    $requiredTables = ['tb_facturas_dte', 'tb_sobre_envios', 'tb_sobre_documentos'];
                    $result['missing_tables'] = array_diff($requiredTables, $tables);
                }
            } else {
                $result['error'] = 'La conexión no es una instancia de PDO';
            }
        } catch (PDOException $e) {
            $result['error'] = $e->getMessage();
        }
        
        self::log("Verificación de base de datos", $result['connected'] ? self::LEVEL_INFO : self::LEVEL_ERROR, $result);
        
        return $result;
    }
}
