<?php
// Incluir conexión a la base de datos
require_once 'db_connection.php';

// Activar la salida de errores para depuración
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Prueba de actualización de ID Voucher</h1>";

try {
    // Verificar si el campo id_voucher existe en la tabla
    $stmt = $conn->prepare("SHOW COLUMNS FROM tb_facturas_dte LIKE 'id_voucher'");
    $stmt->execute();
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$column) {
        echo "<p>El campo id_voucher no existe en la tabla tb_facturas_dte. Intentando crearlo...</p>";
        
        $stmt = $conn->prepare("ALTER TABLE tb_facturas_dte ADD COLUMN id_voucher VARCHAR(50) NULL AFTER id_sobre");
        $result = $stmt->execute();
        
        if ($result) {
            echo "<p>Campo id_voucher creado correctamente.</p>";
        } else {
            echo "<p>Error al crear el campo id_voucher.</p>";
        }
    } else {
        echo "<p>El campo id_voucher ya existe en la tabla tb_facturas_dte.</p>";
    }
    
    // Obtener el primer registro de la tabla para pruebas
    $stmt = $conn->prepare("SELECT id, tipo_dte, folio FROM tb_facturas_dte LIMIT 1");
    $stmt->execute();
    $doc = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($doc) {
        echo "<p>Registro encontrado para pruebas: ID=" . $doc['id'] . ", Tipo DTE=" . $doc['tipo_dte'] . ", Folio=" . $doc['folio'] . "</p>";
        
        // Actualizar el campo id_voucher con un valor de prueba
        $testValue = "TEST_" . time();
        $stmt = $conn->prepare("UPDATE tb_facturas_dte SET id_voucher = ? WHERE id = ?");
        $result = $stmt->execute([$testValue, $doc['id']]);
        
        if ($result) {
            echo "<p>Campo id_voucher actualizado correctamente con el valor: " . $testValue . "</p>";
        } else {
            echo "<p>Error al actualizar el campo id_voucher.</p>";
        }
    } else {
        echo "<p>No se encontraron registros en la tabla tb_facturas_dte.</p>";
    }
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
