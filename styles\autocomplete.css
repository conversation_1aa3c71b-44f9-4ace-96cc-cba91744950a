/**
 * Estilos modernos para el autocompletado
 * Basado en la paleta #34495e
 */

:root {
    --primary-color: #34495e;
    --primary-light: #4a6b8a;
    --primary-dark: #2c3e50;
    --accent-color: #3498db;
    --accent-light: #5dade2;
    --accent-dark: #2980b9;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --text-light: #ecf0f1;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #bdc3c7;
    --background-light: #f5f7fa;
    --shadow-color: rgba(52, 73, 94, 0.2);
}

/* Contenedor principal */
.autocomplete-container {
    position: fixed;
    z-index: 9999;
    background-color: white;
    border: none;
    border-radius: 8px;
    box-shadow: 0 6px 16px var(--shadow-color), 0 0 0 1px rgba(0,0,0,0.05);
    max-height: 350px;
    overflow-y: auto;
    width: 400px;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.autocomplete-container.visible {
    opacity: 1;
    transform: translateY(0);
}

.autocomplete-container::-webkit-scrollbar {
    width: 6px;
}

.autocomplete-container::-webkit-scrollbar-track {
    background: transparent;
}

.autocomplete-container::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

/* Título del contenedor */
.autocomplete-title {
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-light);
    font-weight: 600;
    font-size: 0.95rem;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1;
}

.autocomplete-title .result-count {
    font-size: 0.8rem;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
}

/* Elementos de sugerencia */
.autocomplete-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--background-light);
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover {
    background-color: var(--background-light);
}

.autocomplete-item.active {
    background-color: var(--background-light);
    border-left: 3px solid var(--accent-color);
}

.autocomplete-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.autocomplete-item-image {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 12px;
    object-fit: cover;
    background-color: var(--background-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 18px;
}

/* Textos de sugerencia */
.suggestion-sku {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.95rem;
    margin-bottom: 2px;
}

.suggestion-name {
    color: var(--text-dark);
    font-size: 0.85rem;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 310px;
}

.suggestion-price {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Mensaje de no resultados */
.autocomplete-no-results {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.autocomplete-no-results i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
    opacity: 0.5;
}

/* Animación de carga */
@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.autocomplete-loading {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
}

.loading-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(52, 73, 94, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 0.8s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Campo de entrada con autocompletado */
input.autocomplete-input {
    background-image: none;
    padding-right: 12px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input.autocomplete-input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

/* Resaltado de texto */
mark {
    background-color: rgba(243, 156, 18, 0.2);
    color: inherit;
    padding: 0 2px;
    border-radius: 2px;
    font-weight: 500;
}
