<?php
session_start();
require_once 'db_connection.php';

// Inicializar variables
$error = '';
$success = false;
$email = '';

// Procesar el formulario si se envió
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Obtener y sanitizar el correo electrónico
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);

    // Validar el correo electrónico
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Por favor, ingrese un correo electrónico válido.';
    } else {
        try {
            $conn = getConnection();

            // Verificar si el correo existe en la base de datos
            $checkEmailSql = "SELECT id, username FROM tb_usuarios WHERE email = ?";
            $checkStmt = $conn->prepare($checkEmailSql);
            $checkStmt->execute([$email]);
            $user = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                // Generar token de recuperación
                $token = bin2hex(random_bytes(32));
                $expiry = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token válido por 1 hora

                // Guardar el token en la base de datos
                $updateTokenSql = "UPDATE tb_usuarios SET token_recuperacion = ?, expiracion_token = ? WHERE id = ?";
                $updateStmt = $conn->prepare($updateTokenSql);
                $updateStmt->execute([$token, $expiry, $user['id']]);

                // Construir el enlace de recuperación
                $resetLink = "http://{$_SERVER['HTTP_HOST']}/reset_password.php?token=" . $token;

                // En un entorno de producción, aquí enviaríamos un correo electrónico
                // Por ahora, solo mostraremos el enlace en la página
                $success = true;

                // Registrar el intento de recuperación
                $logSql = "INSERT INTO tb_password_reset_logs (usuario_id, email, ip_address, fecha) VALUES (?, ?, ?, NOW())";
                try {
                    $logStmt = $conn->prepare($logSql);
                    $logStmt->execute([$user['id'], $email, $_SERVER['REMOTE_ADDR']]);
                } catch (Exception $e) {
                    // Si la tabla no existe, la creamos
                    if (strpos($e->getMessage(), "doesn't exist") !== false) {
                        $createLogTable = "CREATE TABLE tb_password_reset_logs (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            usuario_id INT NOT NULL,
                            email VARCHAR(100) NOT NULL,
                            ip_address VARCHAR(45) NOT NULL,
                            fecha DATETIME NOT NULL,
                            FOREIGN KEY (usuario_id) REFERENCES tb_usuarios(id)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                        $conn->exec($createLogTable);

                        // Intentar nuevamente la inserción
                        $logStmt = $conn->prepare($logSql);
                        $logStmt->execute([$user['id'], $email, $_SERVER['REMOTE_ADDR']]);
                    }
                }
            } else {
                // No mostrar si el correo existe o no por seguridad
                $success = true;
            }
        } catch (Exception $e) {
            $error = 'Error en el servidor. Por favor, intente nuevamente más tarde.';
            error_log('Error en forgot_password.php: ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recuperar Contraseña - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #3498db;
            --hover-color: #e67e22;
            --error-color: #e74c3c;
            --success-color: #2ecc71;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), #34495e);
        }

        .recovery-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .logo i {
            color: var(--secondary-color);
            margin-right: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .btn {
            width: 100%;
            padding: 0.8rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--hover-color);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
            margin-top: 1rem;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .error-message {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 4px solid var(--error-color);
            color: var(--error-color);
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
            font-size: 0.9rem;
        }

        .success-message {
            background-color: rgba(46, 204, 113, 0.1);
            border-left: 4px solid var(--success-color);
            color: var(--success-color);
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
            font-size: 0.9rem;
        }

        .reset-link {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 15px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .instructions {
            margin-bottom: 1.5rem;
            color: #555;
            font-size: 0.95rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="recovery-container">
        <div class="logo">
            <i class="fas fa-cogs"></i>
            Tata repuestos
        </div>

        <?php if (!empty($error)): ?>
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i> Se ha enviado un enlace de recuperación a su correo electrónico si está registrado en nuestro sistema.
        </div>

        <?php if (isset($resetLink)): ?>
        <div class="instructions">
            <p><strong>Nota:</strong> En un entorno de producción, este enlace se enviaría por correo electrónico. Para fines de desarrollo, puede usar el siguiente enlace:</p>
        </div>
        <div class="reset-link">
            <?php echo htmlspecialchars($resetLink); ?>
        </div>
        <?php endif; ?>

        <a href="login.php" class="btn btn-secondary" style="display: block; text-align: center; text-decoration: none;">Volver al inicio de sesión</a>

        <?php else: ?>

        <div class="instructions">
            <p>Ingrese su correo electrónico y le enviaremos un enlace para restablecer su contraseña.</p>
        </div>

        <form action="forgot_password.php" method="POST">
            <div class="form-group">
                <label for="email">Correo electrónico</label>
                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required autofocus>
            </div>

            <div style="display: flex; flex-direction: column; gap: 10px;">
                <button type="submit" class="btn btn-primary">Enviar enlace de recuperación</button>
                <a href="login.php" class="btn btn-secondary" style="text-align: center; text-decoration: none;">Volver al inicio de sesión</a>
            </div>
        </form>
        <?php endif; ?>
    </div>
</body>
</html>
