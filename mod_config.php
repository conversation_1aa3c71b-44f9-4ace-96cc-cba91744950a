<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';

// Iniciar buffer de salida para capturar cualquier salida no deseada
ob_start();

// Configuración adicional de caché
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado

require_once 'db_connection.php';
require_once 'image_processor.php';

// Configurar manejo de errores para peticiones Ajax
if (isset($_POST['ajax_request']) && $_POST['ajax_request'] === 'true') {
    // Desactivar la visualización de errores para peticiones Ajax
    error_reporting(E_ALL);
    ini_set('display_errors', 0);

    // Configurar un manejador de errores personalizado
    set_error_handler(function($errno, $errstr, $errfile, $errline) {
        global $ajaxErrorMessages;
        if (!isset($ajaxErrorMessages)) {
            $ajaxErrorMessages = [];
        }
        $ajaxErrorMessages[] = "Error PHP [$errno]: $errstr en $errfile:$errline";
        return true; // Prevenir que PHP maneje el error
    });
} else {
    // Para peticiones normales, mostrar todos los errores
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Array para almacenar mensajes de depuración
/*
$debugMessages = [];

// Función para registrar mensajes de depuración
function debugLog($message, $type = 'info') {
    global $debugMessages;

    // Registrar en el log del servidor
    error_log($message);

    // Guardar para mostrar en la interfaz
    $timestamp = date('H:i:s');
    $debugMessages[] = [
        'time' => $timestamp,
        'message' => $message,
        'type' => $type
    ];

    return $message;
}
*/

// Versión simplificada de debugLog que no hace nada (para mantener compatibilidad)
$debugMessages = [];
function debugLog($message, $type = 'info') {
    return $message;
}

// Función para verificar y corregir permisos de directorios en Linux
/*
function verificarYCorregirPermisos($ruta) {
    // Solo ejecutar en entornos Linux
    if (DIRECTORY_SEPARATOR !== '/') {
        debugLog("No es un entorno Linux, omitiendo verificación de permisos");
        return true;
    }

    debugLog("Verificando permisos para: " . $ruta);

    // Verificar si el directorio existe
    if (!file_exists($ruta)) {
        debugLog("El directorio no existe: " . $ruta, 'error');
        return false;
    }

    // Verificar si es un directorio
    if (!is_dir($ruta)) {
        debugLog("La ruta no es un directorio: " . $ruta, 'error');
        return false;
    }

    // Obtener permisos actuales
    $permisos = substr(sprintf('%o', fileperms($ruta)), -4);
    debugLog("Permisos actuales: " . $permisos);

    // Verificar si el directorio es escribible
    if (!is_writable($ruta)) {
        debugLog("El directorio no es escribible, intentando cambiar permisos: " . $ruta, 'warning');

        // Intentar cambiar permisos
        if (@chmod($ruta, 0755)) {
            debugLog("Permisos cambiados a 0755", 'success');
        } else {
            debugLog("No se pudieron cambiar los permisos a 0755, intentando con 0777", 'warning');
            if (@chmod($ruta, 0777)) {
                debugLog("Permisos cambiados a 0777", 'success');
            } else {
                debugLog("No se pudieron cambiar los permisos", 'error');
                return false;
            }
        }
    }

    // Verificar propietario y grupo
    if (function_exists('posix_getpwuid')) {
        $owner = posix_getpwuid(fileowner($ruta));
        $group = posix_getgrgid(filegroup($ruta));
        debugLog("Propietario: " . ($owner ? $owner['name'] : 'desconocido') . ", Grupo: " . ($group ? $group['name'] : 'desconocido'));
    }

    debugLog("El directorio tiene permisos correctos: " . $ruta, 'success');
    return true;
}
*/

// Versión simplificada que solo verifica si el directorio es escribible
function verificarYCorregirPermisos($ruta) {
    // Verificar si el directorio existe y es escribible
    if (!file_exists($ruta)) {
        return false;
    }

    if (!is_dir($ruta)) {
        return false;
    }

    return is_writable($ruta);
}

// Función para obtener la lista de páginas del sistema
function obtenerPaginasSistema() {
    $paginas = [
        'index.php' => 'Módulo de Página Principal (index.php)',
        'inventory.php' => 'Módulo de Inventario (inventory.php)',
        'ventas.php' => 'Módulo de Ventas (ventas.php)',
        'sobres_envios.php' => 'Módulo de Sobres y Envíos (sobres_envios.php)',
        'mod_config.php' => 'Módulo de Configuración (mod_config.php)',
        'Otra' => 'Otro Módulo (especificar en descripción)'
    ];

    return $paginas;
}

// Activar la visualización de errores para depuración
// error_reporting(E_ALL);
// ini_set('display_errors', 1);

// Información del sistema para depuración
/*
debugLog('Sistema operativo: ' . PHP_OS);
debugLog('Separador de directorios: ' . DIRECTORY_SEPARATOR);
debugLog('Directorio actual: ' . __DIR__);

// Verificar el directorio de uploads al inicio
if (DIRECTORY_SEPARATOR === '/') {
    $uploadDir = '/var/www/html/projects/tata_repuestos/images/upgrades/';
    debugLog('Verificando directorio de uploads en Linux: ' . $uploadDir);
    verificarYCorregirPermisos($uploadDir);
} else {
    $uploadDir = __DIR__ . '/images/upgrades/';
    debugLog('Verificando directorio de uploads en Windows: ' . $uploadDir);
    if (!file_exists($uploadDir)) {
        if (mkdir($uploadDir, 0777, true)) {
            debugLog('Directorio creado: ' . $uploadDir, 'success');
        } else {
            debugLog('No se pudo crear el directorio: ' . $uploadDir, 'error');
        }
    } else {
        debugLog('El directorio ya existe: ' . $uploadDir);
    }
}
*/

// Asegurar que el directorio de uploads exista
$uploadDir = __DIR__ . '/images/upgrades/';
if (!file_exists($uploadDir) && !is_dir($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Procesar el formulario si se ha enviado
$mensaje = '';
$tipoMensaje = '';
$isAjaxRequest = isset($_POST['ajax_request']) && $_POST['ajax_request'] === 'true';
$ajaxResponse = [
    'success' => false,
    'message' => '',
    'debug_messages' => []
];

// Verificar si hay mensajes de sesión
session_start();
if (isset($_SESSION['upgrade_message'])) {
    $mensaje = $_SESSION['upgrade_message'];
    $tipoMensaje = isset($_SESSION['upgrade_success']) && $_SESSION['upgrade_success'] ? 'success' : 'error';

    // Limpiar mensajes de sesión
    unset($_SESSION['upgrade_message']);
    unset($_SESSION['upgrade_success']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['registrar_upgrade']) && !isset($_POST['ajax_request'])) {
    // Registrar los datos recibidos para depuración
    debugLog('Datos POST recibidos: ' . print_r($_POST, true));
    debugLog('Archivos recibidos: ' . print_r($_FILES, true));

    try {
        // Validar campos requeridos
        if (empty($_POST['pagina_modificada']) || empty($_POST['descripcion'])) {
            throw new Exception('Todos los campos son obligatorios');
        }

        $paginaModificada = $_POST['pagina_modificada'];
        $descripcion = $_POST['descripcion'];
        $rutaArchivo = null;
        $tipoArchivo = 'ninguno';

        // Procesar la imagen si fue enviada
        debugLog('Verificando archivo subido...');
        if (isset($_FILES['archivo'])) {
            debugLog('Archivo encontrado: ' . print_r($_FILES['archivo'], true));

            if ($_FILES['archivo']['error'] === UPLOAD_ERR_OK) {
                debugLog('Archivo subido correctamente, procesando...', 'success');
                $fileName = $_FILES['archivo']['name'];
                $fileType = $_FILES['archivo']['type'];
                $fileSize = $_FILES['archivo']['size'];
                $fileTmpName = $_FILES['archivo']['tmp_name'];
                $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

                debugLog('Nombre del archivo: ' . $fileName);
                debugLog('Tipo del archivo: ' . $fileType);
                debugLog('Tamaño del archivo: ' . $fileSize);
                debugLog('Archivo temporal: ' . $fileTmpName);
                debugLog('Extensión del archivo: ' . $fileExtension);

                // Verificar si el archivo temporal existe
                if (file_exists($fileTmpName)) {
                    debugLog('El archivo temporal existe y es accesible', 'success');
                } else {
                    debugLog('El archivo temporal NO existe o no es accesible', 'error');
                }
            } else {
                debugLog('Error en la subida del archivo: ' . $_FILES['archivo']['error'], 'error');
                // Mostrar mensaje de error según el código de error
                switch($_FILES['archivo']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                        throw new Exception('El archivo excede el tamaño máximo permitido por PHP');
                    case UPLOAD_ERR_FORM_SIZE:
                        throw new Exception('El archivo excede el tamaño máximo permitido por el formulario');
                    case UPLOAD_ERR_PARTIAL:
                        throw new Exception('El archivo se subió parcialmente');
                    case UPLOAD_ERR_NO_FILE:
                        throw new Exception('No se subió ningún archivo');
                    case UPLOAD_ERR_NO_TMP_DIR:
                        throw new Exception('Falta la carpeta temporal');
                    case UPLOAD_ERR_CANT_WRITE:
                        throw new Exception('Error al escribir el archivo en el disco');
                    case UPLOAD_ERR_EXTENSION:
                        throw new Exception('Una extensión de PHP detuvo la subida del archivo');
                    default:
                        throw new Exception('Error desconocido al subir el archivo');
                }
            }
        } else {
            debugLog('No se encontró ningún archivo en la solicitud');
        }

        // Si tenemos un archivo válido, procesarlo
        if (isset($fileExtension)) {
            // Verificar si es una imagen o un video
            $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

            debugLog('Verificando tipo de archivo con extensión: ' . $fileExtension);

            // Directorio para guardar todos los archivos
            $uploadDir = 'images/upgrades/';
            $absoluteUploadDir = __DIR__ . '/' . $uploadDir;
            debugLog('Directorio para archivos (ruta relativa): ' . $uploadDir);
            debugLog('Directorio para archivos (ruta absoluta): ' . $absoluteUploadDir);

            // Verificar si estamos en un entorno de producción (Linux)
            if (DIRECTORY_SEPARATOR === '/') {
                // En Linux, asegurarse de que la ruta sea correcta
                debugLog('Detectado entorno Linux, verificando ruta absoluta');
                if (file_exists('/var/www/html/projects/tata_repuestos/images/upgrades')) {
                    $absoluteUploadDir = '/var/www/html/projects/tata_repuestos/images/upgrades/';
                    debugLog('Usando ruta absoluta en servidor Linux: ' . $absoluteUploadDir, 'success');
                } else {
                    debugLog('No se encontró el directorio en la ruta absoluta esperada', 'warning');
                }
            }

            // Asegurarse de que el directorio termine con una barra
            if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
                $absoluteUploadDir .= '/';
            }

            // Verificar que el directorio existe y tiene permisos
            if (!file_exists($absoluteUploadDir)) {
                debugLog('El directorio no existe, intentando crearlo: ' . $absoluteUploadDir, 'warning');
                if (!mkdir($absoluteUploadDir, 0777, true)) {
                    debugLog('Error al crear el directorio: ' . $absoluteUploadDir, 'error');
                    throw new Exception('No se pudo crear el directorio: ' . $absoluteUploadDir);
                }
                chmod($absoluteUploadDir, 0777);
                debugLog('Directorio creado exitosamente', 'success');
            } else {
                debugLog('El directorio ya existe');
                // Verificar permisos
                if (!is_writable($absoluteUploadDir)) {
                    debugLog('El directorio no tiene permisos de escritura, intentando cambiar permisos', 'warning');
                    if (chmod($absoluteUploadDir, 0777)) {
                        debugLog('Permisos cambiados exitosamente a 0777', 'success');
                    } else {
                        debugLog('No se pudieron cambiar los permisos', 'error');
                    }
                } else {
                    debugLog('El directorio tiene permisos de escritura', 'success');
                }
            }

            // Verificar y corregir permisos en entornos Linux
            if (DIRECTORY_SEPARATOR === '/') {
                debugLog('Verificando y corrigiendo permisos en entorno Linux...');
                if (!verificarYCorregirPermisos($absoluteUploadDir)) {
                    debugLog('No se pudieron corregir los permisos del directorio: ' . $absoluteUploadDir, 'error');
                    throw new Exception('No se pudieron corregir los permisos del directorio. Por favor, contacte al administrador del sistema.');
                }
            }

            // Determinar el tipo de archivo
            if (in_array($fileExtension, $imageExtensions)) {
                debugLog('Archivo identificado como imagen', 'success');
                $tipoArchivo = 'imagen';
            } elseif (in_array($fileExtension, $videoExtensions)) {
                debugLog('Archivo identificado como video', 'success');
                $tipoArchivo = 'video';
            } else {
                debugLog('Tipo de archivo no soportado: ' . $fileExtension, 'error');
                throw new Exception('Tipo de archivo no soportado. Solo se permiten imágenes (jpg, jpeg, png, gif, webp) y videos (mp4, webm, ogg, mov, avi)');
            }

            // Generar un nombre único para el archivo
            $uniqueId = uniqid();
            $timestamp = time();
            $safeFilename = preg_replace('/[^\w\-\.]/', '_', pathinfo($fileName, PATHINFO_FILENAME));
            $newFileName = 'upgrade_' . $safeFilename . '_' . $uniqueId . '_' . $timestamp . '.' . $fileExtension;
            $filePath = $absoluteUploadDir . $newFileName;

            debugLog('Nombre del archivo generado: ' . $newFileName);
            debugLog('Ruta completa del archivo: ' . $filePath);
            debugLog('Archivo temporal: ' . $fileTmpName);

            // Verificar que el archivo temporal existe
            if (!file_exists($fileTmpName)) {
                debugLog('El archivo temporal no existe: ' . $fileTmpName, 'error');
                throw new Exception('El archivo temporal no existe o no es accesible');
            } else {
                debugLog('El archivo temporal existe y es accesible', 'success');

                // Verificar permisos del archivo temporal
                $tempPerms = substr(sprintf('%o', fileperms($fileTmpName)), -4);
                debugLog('Permisos del archivo temporal: ' . $tempPerms);

                // Verificar tamaño del archivo temporal
                $tempSize = filesize($fileTmpName);
                debugLog('Tamaño del archivo temporal: ' . $tempSize . ' bytes');

                // Verificar si el directorio de destino es escribible
                if (is_writable(dirname($filePath))) {
                    debugLog('El directorio de destino es escribible', 'success');
                } else {
                    debugLog('El directorio de destino NO es escribible', 'error');
                }
            }

            // Mover el archivo subido
            debugLog('Intentando mover el archivo temporal al destino final...');
            if (!move_uploaded_file($fileTmpName, $filePath)) {
                $moveError = error_get_last();
                debugLog('Error al mover el archivo: ' . ($moveError ? $moveError['message'] : 'Desconocido'), 'error');
                debugLog('Permisos del directorio destino: ' . substr(sprintf('%o', fileperms(dirname($filePath))), -4));

                // Intentar con copy como alternativa
                debugLog('Intentando copiar el archivo en lugar de moverlo...', 'warning');
                if (!copy($fileTmpName, $filePath)) {
                    $copyError = error_get_last();
                    debugLog('Error al copiar el archivo: ' . ($copyError ? $copyError['message'] : 'Desconocido'), 'error');

                    // Intentar con file_put_contents como última alternativa
                    debugLog('Intentando usar file_put_contents como última alternativa...', 'warning');
                    $fileContent = file_get_contents($fileTmpName);
                    if ($fileContent !== false) {
                        if (file_put_contents($filePath, $fileContent) !== false) {
                            debugLog('Archivo guardado exitosamente con file_put_contents', 'success');
                        } else {
                            $putError = error_get_last();
                            debugLog('Error al guardar con file_put_contents: ' . ($putError ? $putError['message'] : 'Desconocido'), 'error');
                            throw new Exception('Error al mover, copiar o guardar el archivo');
                        }
                    } else {
                        debugLog('No se pudo leer el contenido del archivo temporal', 'error');
                        throw new Exception('Error al leer el archivo temporal');
                    }
                } else {
                    debugLog('Archivo copiado exitosamente', 'success');
                }
            } else {
                debugLog('Archivo movido exitosamente', 'success');
            }

            // Verificar que el archivo se haya creado correctamente
            if (!file_exists($filePath)) {
                debugLog('El archivo no se creó correctamente: ' . $filePath, 'error');
                throw new Exception('El archivo no se creó correctamente');
            } else {
                debugLog('Archivo creado correctamente: ' . $filePath, 'success');

                // Verificar permisos del archivo creado
                $filePerms = substr(sprintf('%o', fileperms($filePath)), -4);
                debugLog('Permisos del archivo creado: ' . $filePerms);

                // Verificar tamaño del archivo creado
                $fileSize = filesize($filePath);
                debugLog('Tamaño del archivo creado: ' . $fileSize . ' bytes');
            }

            // Obtener la ruta relativa del archivo
            if (DIRECTORY_SEPARATOR === '/') {
                // En Linux, generar la ruta relativa correcta
                debugLog('Generando ruta relativa en entorno Linux');
                if (strpos($filePath, '/var/www/html/projects/tata_repuestos/') === 0) {
                    $rutaArchivo = str_replace('/var/www/html/projects/tata_repuestos/', '', $filePath);
                    debugLog('Ruta relativa generada desde ruta absoluta del servidor', 'success');
                } else {
                    $rutaArchivo = str_replace(__DIR__ . '/', '', $filePath);
                    debugLog('Ruta relativa generada desde __DIR__', 'warning');
                }
            } else {
                // En Windows, usar la ruta relativa normal
                $rutaArchivo = str_replace(__DIR__ . '/', '', $filePath);
                debugLog('Ruta relativa generada en entorno Windows');
            }
            debugLog('Ruta relativa del archivo: ' . $rutaArchivo, 'success');

            // Verificar si la ruta relativa es accesible desde el navegador
            debugLog('URL completa del archivo: ' . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . '/' . $rutaArchivo);
        }

        // Conectar a la base de datos
        debugLog('Conectando a la base de datos...');
        try {
            try {
                $conn = getConnection();

                // Verificar que la conexión es válida
                if ($conn instanceof PDO) {
                    // Intentar una consulta simple para verificar la conexión
                    $testStmt = $conn->query('SELECT 1');
                    if ($testStmt) {
                        debugLog('Conexión a la base de datos establecida y verificada', 'success');
                    } else {
                        $errorInfo = $conn->errorInfo();
                        debugLog('La conexión a la base de datos se estableció pero no se pudo ejecutar una consulta de prueba: ' . print_r($errorInfo, true), 'warning');
                    }
                } else {
                    debugLog('La conexión a la base de datos no es una instancia de PDO', 'error');
                    throw new Exception('Error en la conexión a la base de datos: tipo de conexión inválido');
                }
            } catch (PDOException $pdoEx) {
                debugLog('Error al establecer la conexión a la base de datos: ' . $pdoEx->getMessage(), 'error');
                debugLog('Código de error: ' . $pdoEx->getCode(), 'error');
                throw $pdoEx;
            }

            // Verificar si la tabla tb_upgrades existe
            debugLog('Verificando si la tabla tb_upgrades existe...');
            $checkTableSql = "SHOW TABLES LIKE 'tb_upgrades'";
            $checkTableStmt = $conn->query($checkTableSql);

            if ($checkTableStmt === false) {
                $errorInfo = $conn->errorInfo();
                debugLog('Error al verificar la tabla: ' . print_r($errorInfo, true), 'error');
                throw new Exception('Error al verificar la tabla tb_upgrades: ' . $errorInfo[2]);
            }

            $tableExists = $checkTableStmt->rowCount() > 0;
            debugLog('Resultado de la verificación de tabla: ' . ($tableExists ? 'La tabla existe' : 'La tabla no existe'));

            // Mostrar información de la base de datos
            try {
                $dbInfoStmt = $conn->query("SELECT DATABASE() as db_name");
                if ($dbInfoStmt) {
                    $dbInfo = $dbInfoStmt->fetch(PDO::FETCH_ASSOC);
                    debugLog('Base de datos actual: ' . $dbInfo['db_name']);
                }
            } catch (Exception $e) {
                debugLog('No se pudo obtener información de la base de datos: ' . $e->getMessage(), 'warning');
            }

            if (!$tableExists) {
                debugLog('La tabla tb_upgrades no existe, creando...', 'warning');
                $createTableSql = "
                    CREATE TABLE tb_upgrades (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                        pagina_modificada VARCHAR(100) NOT NULL,
                        descripcion TEXT NOT NULL,
                        ruta_archivo VARCHAR(255),
                        tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno',
                        usuario VARCHAR(100) DEFAULT 'admin'
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";
                $conn->exec($createTableSql);
                debugLog('Tabla tb_upgrades creada exitosamente', 'success');
            } else {
                debugLog('La tabla tb_upgrades ya existe');

                // Verificar si las columnas necesarias existen
                $checkColumnsSql = "SHOW COLUMNS FROM tb_upgrades LIKE 'ruta_archivo'";
                $rutaArchivoExists = $conn->query($checkColumnsSql)->rowCount() > 0;

                if (!$rutaArchivoExists) {
                    debugLog('La columna ruta_archivo no existe, modificando tabla...', 'warning');
                    // Primero verificamos si existen las columnas antiguas
                    $checkOldColumnsSql = "SHOW COLUMNS FROM tb_upgrades LIKE 'ruta_imagen'";
                    $rutaImagenExists = $conn->query($checkOldColumnsSql)->rowCount() > 0;

                    if ($rutaImagenExists) {
                        // Si existe la estructura antigua, hacemos una migración
                        debugLog('Estructura antigua detectada, migrando datos...');
                        $addColumnSql = "ALTER TABLE tb_upgrades ADD COLUMN ruta_archivo VARCHAR(255)";
                        $conn->exec($addColumnSql);
                        debugLog('Columna ruta_archivo agregada');

                        // Migrar datos existentes
                        $migrateDataSql = "UPDATE tb_upgrades SET ruta_archivo = CASE
                            WHEN ruta_imagen IS NOT NULL THEN ruta_imagen
                            WHEN ruta_video IS NOT NULL THEN ruta_video
                            ELSE NULL
                            END";
                        $conn->exec($migrateDataSql);
                        debugLog('Datos migrados exitosamente');

                        // Eliminar columnas antiguas si es necesario
                        // $dropColumnsSql = "ALTER TABLE tb_upgrades DROP COLUMN ruta_imagen, DROP COLUMN ruta_video";
                        // $conn->exec($dropColumnsSql);
                    } else {
                        // Si no existe la estructura antigua, simplemente agregamos la nueva columna
                        debugLog('Agregando nuevas columnas a la tabla...');
                        $addColumnSql = "ALTER TABLE tb_upgrades ADD COLUMN ruta_archivo VARCHAR(255), ADD COLUMN tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno'";
                        $conn->exec($addColumnSql);
                        debugLog('Columnas agregadas exitosamente');
                    }

                    debugLog('Tabla modificada exitosamente', 'success');
                } else {
                    debugLog('La columna ruta_archivo ya existe', 'success');
                }
            }

            // Preparar la consulta SQL
            debugLog('Preparando consulta SQL para insertar registro...');
            $sql = "INSERT INTO tb_upgrades (pagina_modificada, descripcion, ruta_archivo, tipo_archivo)
                    VALUES (:pagina_modificada, :descripcion, :ruta_archivo, :tipo_archivo)";

            $stmt = $conn->prepare($sql);
            debugLog('Consulta SQL preparada');

            // Vincular parámetros
            $stmt->bindParam(':pagina_modificada', $paginaModificada);
            $stmt->bindParam(':descripcion', $descripcion);
            $stmt->bindParam(':ruta_archivo', $rutaArchivo);
            $stmt->bindParam(':tipo_archivo', $tipoArchivo);

            debugLog('Parámetros vinculados:');
            debugLog('pagina_modificada: ' . $paginaModificada);
            debugLog('descripcion: ' . $descripcion);
            debugLog('ruta_archivo: ' . ($rutaArchivo ? $rutaArchivo : 'NULL'));
            debugLog('tipo_archivo: ' . $tipoArchivo);

            // Ejecutar la consulta
            debugLog('Ejecutando consulta...');
            try {
                // Obtener la consulta SQL con los valores reales (para depuración)
                $queryString = $sql;
                $queryString = str_replace(':pagina_modificada', "'" . $paginaModificada . "'", $queryString);
                $queryString = str_replace(':descripcion', "'" . $descripcion . "'", $queryString);
                $queryString = str_replace(':ruta_archivo', $rutaArchivo ? "'" . $rutaArchivo . "'" : 'NULL', $queryString);
                $queryString = str_replace(':tipo_archivo', "'" . $tipoArchivo . "'", $queryString);
                debugLog('Consulta SQL a ejecutar: ' . $queryString);

                // Verificar conexión a la base de datos
                if (!$conn) {
                    debugLog('La conexión a la base de datos no es válida', 'error');
                    throw new Exception('Error de conexión a la base de datos');
                }

                // Verificar que la consulta preparada es válida
                if (!$stmt) {
                    debugLog('La consulta preparada no es válida', 'error');
                    throw new Exception('Error al preparar la consulta SQL');
                }

                // Ejecutar la consulta
                $result = $stmt->execute();

                if ($result) {
                    $lastId = $conn->lastInsertId();
                    debugLog('Registro insertado exitosamente con ID: ' . $lastId, 'success');

                    $mensaje = 'Registro de upgrade guardado correctamente';
                    $tipoMensaje = 'success';
                } else {
                    $errorInfo = $stmt->errorInfo();
                    debugLog('La consulta no se ejecutó correctamente. Error: ' . print_r($errorInfo, true), 'error');
                    throw new Exception('Error al ejecutar la consulta: ' . $errorInfo[2]);
                }
            } catch (PDOException $pdoEx) {
                debugLog('Error PDO al ejecutar la consulta: ' . $pdoEx->getMessage(), 'error');
                debugLog('Código de error: ' . $pdoEx->getCode(), 'error');
                debugLog('Información de error: ' . print_r($pdoEx->errorInfo, true), 'error');
                throw $pdoEx;
            }
        } catch (PDOException $e) {
            debugLog('Error de PDO al interactuar con la base de datos: ' . $e->getMessage(), 'error');
            debugLog('Código de error: ' . $e->getCode(), 'error');
            throw new Exception('Error de base de datos: ' . $e->getMessage());
        }

    } catch (Exception $e) {
        $mensaje = 'Error: ' . $e->getMessage();
        $tipoMensaje = 'error';

        // Capturar información detallada del error
        debugLog('Excepción capturada: ' . $e->getMessage(), 'error');
        debugLog('Archivo: ' . $e->getFile() . ':' . $e->getLine(), 'error');
        debugLog('Traza: ' . $e->getTraceAsString(), 'error');

        if ($isAjaxRequest) {
            // La respuesta Ajax se manejará al final del script
            $ajaxResponse['success'] = false;
            $ajaxResponse['message'] = $e->getMessage();
            $ajaxResponse['error_details'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];
        }
    }
}

// Si es una petición Ajax, enviar respuesta JSON y terminar
if ($isAjaxRequest) {
    // Capturar cualquier salida en el buffer
    $outputBuffer = ob_get_clean();

    // Iniciar un nuevo buffer limpio
    ob_start();

    if ($tipoMensaje === 'success') {
        $ajaxResponse['success'] = true;
        $ajaxResponse['message'] = $mensaje;
    } else {
        $ajaxResponse['success'] = false;
        $ajaxResponse['message'] = $mensaje;
    }

    $ajaxResponse['debug_messages'] = $debugMessages;

    // Añadir información adicional si hay un archivo
    if (isset($rutaArchivo) && !empty($rutaArchivo)) {
        $ajaxResponse['ruta_archivo'] = $rutaArchivo;
        $ajaxResponse['tipo_archivo'] = $tipoArchivo;
    }

    // Añadir ID del registro si está disponible
    if (isset($lastId)) {
        $ajaxResponse['id'] = $lastId;
    }

    // Añadir cualquier salida capturada en el buffer
    if (!empty($outputBuffer)) {
        $ajaxResponse['output_buffer'] = $outputBuffer;
    }

    // Añadir errores PHP capturados
    if (isset($ajaxErrorMessages) && !empty($ajaxErrorMessages)) {
        $ajaxResponse['php_errors'] = $ajaxErrorMessages;
    }

    // Asegurarse de que no haya salida antes de enviar los encabezados
    if (headers_sent($file, $line)) {
        $ajaxResponse['headers_sent'] = true;
        $ajaxResponse['headers_file'] = $file;
        $ajaxResponse['headers_line'] = $line;
    }

    // Enviar la respuesta JSON
    header('Content-Type: application/json');
    echo json_encode($ajaxResponse);

    // Limpiar y terminar
    ob_end_flush();
    exit;
}

// Obtener la lista de upgrades registrados
$upgrades = [];
try {
    $conn = getConnection();
    $stmt = $conn->query("SELECT * FROM tb_upgrades ORDER BY fecha_registro DESC");
    $upgrades = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $mensaje = 'Error al cargar los registros: ' . $e->getMessage();
    $tipoMensaje = 'error';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Agregar meta tags para control de caché -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Módulo Configuración - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/table.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/cards.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/controls.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/header.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/index.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/search.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="styles/video_compressor.css?v=<?php echo time(); ?>">

    <style>
        .config-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .config-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .config-card h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .form-control {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
            white-space: pre-wrap;
            font-family: monospace;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-color), #2980b9);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, var(--accent-color));
            transform: translateY(-2px);
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .upgrade-table {
            width: 100%;
            border-collapse: collapse;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .upgrade-table th, .upgrade-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .upgrade-table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .upgrade-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .upgrade-table tr:hover {
            background-color: #f1f4f7;
        }

        .upgrade-image {
            max-width: 100px;
            max-height: 100px;
            object-fit: contain;
        }

        .upgrade-image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .upgrade-image-modal img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }

        .close-modal {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 30px;
            cursor: pointer;
        }

        /* Estilos para los botones de acción */
        .action-buttons {
            white-space: nowrap;
            text-align: center;
        }

        .icon-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .icon-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .edit-btn {
            color: #17a2b8;
            margin-right: 8px;
        }

        .edit-btn:hover {
            color: #138496;
        }

        .delete-btn {
            color: #dc3545;
        }

        .delete-btn:hover {
            color: #bd2130;
        }

        /* Estilos para la celda de descripción */
        .descripcion-celda {
            white-space: pre-wrap;
            max-width: 400px;
            min-width: 300px;
            text-align: left;
            vertical-align: top;
            padding: 10px;
            line-height: 1.5;
            font-family: monospace;
        }

        /* Estilos para el contenedor contraíble */
        .collapsible-header {
            cursor: pointer;
            user-select: none;
        }

        .collapsible-header h2 {
            display: flex;
            align-items: center;
        }

        .toggle-icon {
            margin-left: 10px;
            transition: transform 0.3s ease;
        }

        .collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .historial-content {
            transition: max-height 0.5s ease, opacity 0.5s ease, padding 0.5s ease;
            max-height: 2000px;
            opacity: 1;
            overflow: hidden;
        }

        .collapsed .historial-content {
            max-height: 0;
            opacity: 0;
            padding-top: 0;
            padding-bottom: 0;
        }

        /* Estilos para el modal */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5); /* Fondo oscuro semi-transparente */
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1050;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
            transition: opacity 0.15s linear;
        }

        .modal.show {
            display: flex !important;
            opacity: 1;
        }

        .modal-dialog {
            position: relative;
            width: auto;
            margin: 1.75rem auto;
            pointer-events: none;
            transform: translate(0, 0);
            transition: transform 0.3s ease-out;
        }

        .modal.show .modal-dialog {
            transform: translate(0, 0);
        }

        .modal-dialog-centered {
            display: flex;
            align-items: center;
            min-height: calc(100% - 3.5rem);
        }

        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            pointer-events: auto;
            background-color: #fff;
            background-clip: padding-box;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            outline: 0;
            max-width: 800px;
            margin: 0 auto;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), #2980b9);
            color: white;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-title {
            font-weight: 600;
            margin: 0;
            line-height: 1.5;
        }

        .btn-close {
            background: transparent;
            border: 0;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            opacity: 0.8;
            padding: 0;
            margin: -1rem -1rem -1rem auto;
            cursor: pointer;
        }

        .btn-close:hover {
            opacity: 1;
            color: white;
        }

        .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-end;
            padding: 1.25rem;
            border-top: 1px solid #dee2e6;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1040;
            width: 100vw;
            height: 100vh;
            background-color: #000;
            opacity: 0.5;
        }

        /* Estilos para el encabezado con botón */
        .header-with-button {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .header-with-button h2 {
            margin: 0;
            font-weight: 600;
            color: var(--primary-color);
        }

        .header-with-button .btn-primary {
            margin-left: 15px;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Header principal con logo y módulos -->
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                TATA REPUESTOS
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link active"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <!-- Icono de Notificación -->
                    <i class="fas fa-bell"></i>

                    <!-- Icono de Usuario -->
                    <i class="fas fa-user"></i>
                    <div class="user-dropdown">
                        <a href="login.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Contenedor principal de configuración -->
    <div class="config-container">
        <?php if (!empty($mensaje)): ?>
            <div class="alert <?php echo $tipoMensaje === 'success' ? 'alert-success' : 'alert-danger'; ?>" style="margin-bottom: 20px; padding: 15px; border-radius: 5px; background-color: <?php echo $tipoMensaje === 'success' ? '#d4edda' : '#f8d7da'; ?>; color: <?php echo $tipoMensaje === 'success' ? '#155724' : '#721c24'; ?>; border: 1px solid <?php echo $tipoMensaje === 'success' ? '#c3e6cb' : '#f5c6cb'; ?>">
                <i class="fas <?php echo $tipoMensaje === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i> <?php echo $mensaje; ?>
            </div>
        <?php endif; ?>

        <!-- Contenedor de mensajes de depuración (comentado) -->
        <!--
        <div id="debug-container" class="config-card" style="display: none;">
            <h2><i class="fas fa-bug"></i> Información de Depuración</h2>
            <div id="debug-messages" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
            <button type="button" class="btn-primary" style="margin-top: 10px;" onclick="document.getElementById('debug-container').style.display = 'none';">Ocultar</button>
        </div>
        -->

        <!-- La tarjeta con el botón para registrar upgrades ha sido eliminada y el botón se ha movido a la tarjeta de historial -->

        <!-- Modal para registrar upgrades -->
        <div class="modal fade" id="upgradeModal" tabindex="-1" aria-labelledby="upgradeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="upgradeModalLabel">
                            <i class="fas fa-upload me-2"></i>Registrar Nuevo Upgrade
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="upgradeForm" action="registrar_upgrade.php" method="post" enctype="multipart/form-data" novalidate onsubmit="return submitFormAjax(event)" data-operation="registrar_upgrade">
                            <!-- Campos ocultos para el formulario -->
                            <input type="hidden" name="registrar_upgrade" value="1" id="registrar_upgrade_field">
                            <input type="hidden" name="ajax_request" value="true">
                            <!-- Campo de debug comentado
                            <input type="hidden" name="debug_info" value="1">
                            -->

                            <div class="form-group mb-3">
                                <label for="pagina_modificada" class="form-label">Página Modificada:</label>
                                <select name="pagina_modificada" id="pagina_modificada" class="form-select" required>
                                    <option value="">Seleccione una página</option>
                                    <?php foreach (obtenerPaginasSistema() as $pagina => $nombre): ?>
                                        <option value="<?php echo $pagina; ?>"><?php echo $nombre; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label for="descripcion" class="form-label">Descripción del Cambio:</label>
                                <textarea name="descripcion" id="descripcion" class="form-control" required placeholder="Describa detalladamente los cambios realizados..." rows="4" style="white-space: pre-wrap; font-family: monospace;"></textarea>
                                <div class="form-text"><i class="fas fa-info-circle"></i> Puede usar Enter para crear líneas separadas. Los saltos de línea se mostrarán en la tabla de historial.</div>
                            </div>
                            <div class="form-group mb-3">
                                <label for="archivo" class="form-label">Archivo (opcional):</label>
                                <div style="display: flex; align-items: center;">
                                    <input type="file" name="archivo" id="archivo" class="form-control" accept="image/*,video/*" onchange="handleFileSelect(this)">
                                    <button type="button" id="compressBtn" class="compress-video-btn ms-2" onclick="compressVideo()">Comprimir Video</button>
                                </div>
                                <small class="form-text text-muted">Puede subir una imagen o un video, o dejar este campo vacío. Formatos soportados: jpg, jpeg, png, gif, webp, mp4, webm, ogg, mov, avi.</small>
                                <div class="form-text text-info mt-1"><i class="fas fa-info-circle"></i> Este campo es completamente opcional. Si no adjunta un archivo, el registro se creará igualmente.</div>
                                <div id="fileSizeWarning" class="file-size-warning mt-2">El archivo es demasiado grande (>10MB). Se recomienda comprimirlo antes de subir.</div>

                                <!-- Contenedor de vista previa de video -->
                                <div id="videoPreviewContainer" class="video-preview-container mt-3">
                                    <video id="videoPreview" class="video-preview" controls></video>
                                </div>

                                <!-- Contenedor de compresión -->
                                <div id="compressionContainer" class="compression-container mt-3">
                                    <div id="compressionStatus" class="compression-status">Preparando compresión...</div>
                                    <div class="compression-progress-container">
                                        <div id="compressionProgress" class="compression-progress">0%</div>
                                    </div>
                                    <div class="compression-actions">
                                        <div id="compressionInfo" class="compression-info"></div>
                                        <button type="button" id="cancelCompression" class="compression-cancel" onclick="cancelCompression()">Cancelar</button>
                                    </div>
                                </div>
                            </div>
                            <div id="formMessage" class="mt-3" style="display: none;"></div>

                            <!-- Contenedor para resultados de Ajax -->
                            <div id="ajaxResult" class="mt-3" style="display: none;">
                                <h4>Resultado de la operación:</h4>
                                <div id="ajaxResponse" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 200px; overflow-y: auto;"></div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>Cancelar
                        </button>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('registrar_upgrade_field').value = '1'; if(validarFormulario()) document.getElementById('upgradeForm').dispatchEvent(new Event('submit'));">
                            <i class="fas fa-save me-1"></i>Guardar Registro
                        </button>
                        <span id="submitStatus" class="ms-2"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tarjeta para mostrar los upgrades registrados -->
        <div class="config-card collapsed">
            <div class="header-with-button collapsible-header" onclick="toggleHistorialContainer(event)">
                <h2><i class="fas fa-history"></i> Historial de Upgrades <i class="fas fa-chevron-down toggle-icon"></i></h2>
                <button type="button" class="btn-primary" onclick="openUpgradeModal(event)">
                    <i class="fas fa-plus-circle"></i> Nuevo Registro de Upgrade
                </button>
            </div>
            <div class="historial-content" id="historialContent">
            <?php if (empty($upgrades)): ?>
                <p>No hay registros de upgrades.</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="upgrade-table">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Página</th>
                                <th>Descripción</th>
                                <th>Archivo</th>
                                <th>Usuario</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($upgrades as $upgrade): ?>
                                <tr>
                                    <td><?php echo date('d/m/Y H:i', strtotime($upgrade['fecha_registro'])); ?></td>
                                    <td><?php echo $upgrade['pagina_modificada']; ?></td>
                                    <td class="descripcion-celda"><?php
                                        // Simplemente mostrar el texto con htmlspecialchars sin nl2br
                                        echo htmlspecialchars($upgrade['descripcion']);
                                    ?></td>
                                    <td>
                                        <?php if ($upgrade['tipo_archivo'] === 'imagen' && !empty($upgrade['ruta_archivo'])): ?>
                                            <img src="<?php echo $upgrade['ruta_archivo']; ?>" alt="Captura de pantalla" class="upgrade-image" onclick="mostrarImagen('<?php echo $upgrade['ruta_archivo']; ?>')">
                                        <?php elseif ($upgrade['tipo_archivo'] === 'video' && !empty($upgrade['ruta_archivo'])): ?>
                                            <video class="upgrade-video" width="100" height="100" controls>
                                                <source src="<?php echo $upgrade['ruta_archivo']; ?>" type="video/<?php echo pathinfo($upgrade['ruta_archivo'], PATHINFO_EXTENSION); ?>">
                                                Tu navegador no soporta el tag de video.
                                            </video>
                                        <?php else: ?>
                                            <span>Sin archivo</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $upgrade['usuario']; ?></td>
                                    <td class="action-buttons">
                                        <button class="icon-btn edit-btn" title="Editar" onclick="editUpgrade(<?php echo $upgrade['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="icon-btn delete-btn" title="Eliminar" onclick="confirmDeleteUpgrade(<?php echo $upgrade['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal para mostrar imagen ampliada -->
    <div id="mediaModal" class="upgrade-image-modal">
        <span class="close-modal" onclick="cerrarModal()">&times;</span>
        <div id="modalContent">
            <!-- El contenido (imagen o video) se cargará dinámicamente aquí -->
        </div>
    </div>

    <!-- Modal para editar un registro -->
    <div class="modal" id="editUpgradeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit"></i> Editar Registro de Upgrade</h5>
                    <button type="button" class="btn-close" onclick="cerrarEditModal()" aria-label="Cerrar">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editUpgradeForm" enctype="multipart/form-data" onsubmit="return submitEditFormAjax(event)">
                        <input type="hidden" id="edit_id" name="id">
                        <input type="hidden" name="editar_upgrade" value="1">
                        <input type="hidden" name="ajax_request" value="true">

                        <div class="form-group">
                            <label for="edit_pagina_modificada">Página Modificada:</label>
                            <select class="form-control" id="edit_pagina_modificada" name="pagina_modificada" required>
                                <option value="">Seleccione una página</option>
                                <option value="index.php">Módulo de Página Principal (index.php)</option>
                                <option value="inventory.php">Módulo de Inventario (inventory.php)</option>
                                <option value="ventas.php">Módulo de Ventas (ventas.php)</option>
                                <option value="sobres_envios.php">Módulo de Sobres y Envíos (sobres_envios.php)</option>
                                <option value="mod_config.php">Módulo de Configuración (mod_config.php)</option>
                                <option value="Otra">Otro Módulo (especificar en descripción)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_descripcion">Descripción del Cambio:</label>
                            <textarea class="form-control" id="edit_descripcion" name="descripcion" rows="5" required style="white-space: pre-wrap; font-family: monospace;"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit_archivo_actual">Archivo Actual:</label>
                            <div id="edit_archivo_actual_container" class="mb-2">
                                <!-- Aquí se mostrará el archivo actual (imagen o video) -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit_archivo">Reemplazar Archivo (opcional):</label>
                            <input type="file" class="form-control" id="edit_archivo" name="archivo" accept="image/*,video/*" onchange="handleEditFileSelect(this)">
                            <small class="form-text text-muted">Formatos permitidos: jpg, jpeg, png, gif, webp, mp4, webm, ogg, mov, avi</small>
                        </div>

                        <div id="edit_fileSizeWarning" style="display: none;" class="alert alert-warning mt-2">
                            <i class="fas fa-exclamation-triangle"></i> El archivo de video es grande. Se recomienda comprimirlo antes de subirlo.
                            <button type="button" class="btn btn-sm btn-warning mt-2" id="edit_compressBtn" onclick="compressEditVideo()">
                                <i class="fas fa-compress"></i> Comprimir Video
                            </button>
                        </div>

                        <div id="edit_videoPreviewContainer" style="display: none;" class="mt-3">
                            <label>Vista previa:</label>
                            <video id="edit_videoPreview" controls style="max-width: 100%; max-height: 200px;"></video>
                        </div>

                        <div id="edit_compressionContainer" style="display: none;" class="mt-3 p-3 border rounded bg-light">
                            <h6><i class="fas fa-cog fa-spin"></i> Compresión de Video</h6>
                            <div id="edit_compressionStatus">Preparando...</div>
                            <div class="progress mt-2">
                                <div id="edit_compressionProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="edit_compressionInfo" class="mt-2 small"></div>
                            <button type="button" class="btn btn-sm btn-secondary mt-2" onclick="cancelEditCompression()">
                                <i class="fas fa-times"></i> Cancelar
                            </button>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="cerrarEditModal()">Cancelar</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Guardar Cambios
                            </button>
                            <span id="editSubmitStatus" class="ms-2"></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/video_compressor.js?v=<?php echo time(); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para mostrar la imagen en el modal
        function mostrarImagen(rutaImagen) {
            const modal = document.getElementById('mediaModal');
            const modalContent = document.getElementById('modalContent');

            // Limpiar el contenido anterior
            modalContent.innerHTML = '';

            // Crear elemento de imagen
            const img = document.createElement('img');
            img.src = rutaImagen;
            img.alt = 'Imagen ampliada';
            img.style.maxWidth = '90%';
            img.style.maxHeight = '90%';
            img.style.objectFit = 'contain';

            // Agregar la imagen al contenido del modal
            modalContent.appendChild(img);

            // Mostrar el modal
            modal.style.display = 'flex';

            // Cerrar el modal al hacer clic fuera del contenido
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    cerrarModal();
                }
            });
        }

        // Función para mostrar video en el modal
        function mostrarVideo(rutaVideo) {
            const modal = document.getElementById('mediaModal');
            const modalContent = document.getElementById('modalContent');

            // Limpiar el contenido anterior
            modalContent.innerHTML = '';

            // Crear elemento de video
            const video = document.createElement('video');
            video.controls = true;
            video.autoplay = true;
            video.style.maxWidth = '90%';
            video.style.maxHeight = '90%';

            // Crear source para el video
            const source = document.createElement('source');
            source.src = rutaVideo;
            source.type = 'video/' + rutaVideo.split('.').pop();

            // Agregar mensaje de fallback
            video.innerHTML = 'Tu navegador no soporta el tag de video.';

            // Agregar source al video
            video.appendChild(source);

            // Agregar el video al contenido del modal
            modalContent.appendChild(video);

            // Mostrar el modal
            modal.style.display = 'flex';

            // Cerrar el modal al hacer clic fuera del contenido
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    cerrarModal();
                }
            });
        }

        // Función para cerrar el modal de medios
        function cerrarModal() {
            const modal = document.getElementById('mediaModal');
            const modalContent = document.getElementById('modalContent');

            // Detener videos si hay alguno reproduciéndose
            const videos = modalContent.querySelectorAll('video');
            videos.forEach(video => {
                video.pause();
            });

            // Ocultar el modal
            modal.style.display = 'none';

            // Limpiar el contenido
            setTimeout(() => {
                modalContent.innerHTML = '';
            }, 300);
        }

        // Función para cerrar el modal de edición
        function cerrarEditModal() {
            console.log('Cerrando modal de edición');
            const modal = document.getElementById('editUpgradeModal');

            // Ocultar el modal
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');

            // Eliminar backdrop si existe
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.parentNode.removeChild(backdrop);
            }

            // Limpiar campos del formulario
            document.getElementById('edit_id').value = '';
            document.getElementById('edit_pagina_modificada').value = '';
            document.getElementById('edit_descripcion').value = '';
            document.getElementById('edit_archivo').value = '';

            // Ocultar elementos de compresión y vista previa
            document.getElementById('edit_fileSizeWarning').style.display = 'none';
            document.getElementById('edit_videoPreviewContainer').style.display = 'none';
            document.getElementById('edit_compressionContainer').style.display = 'none';

            // Limpiar el contenedor de archivo actual
            document.getElementById('edit_archivo_actual_container').innerHTML = '';

            // Limpiar el estado de envío
            document.getElementById('editSubmitStatus').innerHTML = '';
        }

        // Manejar el evento de tecla Escape para cerrar los modales
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Verificar qué modales están abiertos y cerrarlos
                const mediaModal = document.getElementById('mediaModal');
                const editModal = document.getElementById('editUpgradeModal');

                if (mediaModal && mediaModal.style.display === 'flex') {
                    cerrarModal();
                }

                if (editModal && (editModal.style.display === 'flex' || editModal.classList.contains('show'))) {
                    cerrarEditModal();
                }
            }
        });

        // Variables para la compresión de video
        let selectedFile = null;
        let compressor = null;
        let compressedFile = null;

        // Función para manejar la selección de archivos
        function handleFileSelect(input) {
            console.log('handleFileSelect llamado');
            try {
                const file = input.files[0];
                if (!file) {
                    console.log('No se seleccionó ningún archivo');
                    return;
                }

                console.log('Archivo seleccionado:', file.name, 'Tipo:', file.type, 'Tamaño:', (file.size / 1024 / 1024).toFixed(2) + 'MB');

                // Guardar referencia al archivo seleccionado
                selectedFile = file;
                compressedFile = null;

                // Ocultar elementos de compresión
                document.getElementById('compressionContainer').style.display = 'none';
                document.getElementById('compressBtn').style.display = 'none';
                document.getElementById('fileSizeWarning').style.display = 'none';
                document.getElementById('videoPreviewContainer').style.display = 'none';

                // Verificar si es un video
                if (file.type.startsWith('video/')) {
                    console.log('Archivo de video detectado');

                    // Mostrar vista previa del video
                    const videoPreview = document.getElementById('videoPreview');
                    videoPreview.src = URL.createObjectURL(file);
                    document.getElementById('videoPreviewContainer').style.display = 'block';

                    // Verificar tamaño del archivo
                    const fileSizeMB = file.size / 1024 / 1024;
                    console.log(`Tamaño del archivo de video: ${fileSizeMB.toFixed(2)}MB`);

                    if (fileSizeMB > 10) {
                        console.log('Archivo de video grande detectado, mostrando opciones de compresión');
                        document.getElementById('fileSizeWarning').style.display = 'block';
                        document.getElementById('compressBtn').style.display = 'inline-block';
                    }
                }
            } catch (error) {
                console.error('Error en handleFileSelect:', error);
            }
        }

        // Función para comprimir el video
        function compressVideo() {
            if (!selectedFile || !selectedFile.type.startsWith('video/')) {
                alert('Por favor, seleccione un archivo de video primero.');
                return;
            }

            // Mostrar el contenedor de compresión
            const compressionContainer = document.getElementById('compressionContainer');
            compressionContainer.style.display = 'block';

            // Inicializar el compresor con configuración mejorada
            compressor = new VideoCompressor({
                maxSizeMB: 10,
                maxWidthOrHeight: 1280,
                fileType: 'video/mp4', // Cambiar a MP4 para mejor compatibilidad
                quality: 0.7,
                debug: true,
                statusElement: document.getElementById('compressionStatus'),
                progressElement: document.getElementById('compressionProgress')
            });

            // Mostrar mensaje de inicio
            document.getElementById('compressionStatus').textContent = 'Iniciando compresión...';

            // Iniciar la compresión con manejo de errores mejorado
            try {
                compressor.compress(selectedFile)
                    .then(result => {
                        compressedFile = result;

                        // Actualizar información
                        const originalSize = (selectedFile.size / 1024 / 1024).toFixed(2);
                        const compressedSize = (compressedFile.size / 1024 / 1024).toFixed(2);
                        const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(0);

                        document.getElementById('compressionInfo').textContent =
                            `Original: ${originalSize}MB, Comprimido: ${compressedSize}MB (${ratio}% reducido)`;

                        // Actualizar la vista previa
                        const videoPreview = document.getElementById('videoPreview');
                        videoPreview.src = URL.createObjectURL(compressedFile);

                        // Reemplazar el archivo en el input
                        replaceFileInput(compressedFile);

                        // Mensaje de éxito
                        document.getElementById('compressionStatus').textContent = 'Compresión completada con éxito';
                    })
                    .catch(error => {
                        console.error('Error al comprimir el video:', error);
                        document.getElementById('compressionStatus').textContent = 'Error: ' + error.message;
                        alert('Error al comprimir el video: ' + error.message + '. Intente con un archivo más pequeño o en otro formato.');
                    });
            } catch (error) {
                console.error('Error al iniciar la compresión:', error);
                document.getElementById('compressionStatus').textContent = 'Error al iniciar la compresión: ' + error.message;
                alert('Error al iniciar la compresión del video. Intente con un archivo más pequeño o en otro formato.');
            }
        }

        // Función para cancelar la compresión
        function cancelCompression() {
            document.getElementById('compressionContainer').style.display = 'none';
        }

        // Función para reemplazar el archivo en el input
        function replaceFileInput(file) {
            // Crear un nuevo objeto DataTransfer
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);

            // Reemplazar los archivos en el input
            document.getElementById('archivo').files = dataTransfer.files;
        }

        // Función para validar el formulario antes de enviarlo
        function validarFormulario() {
            console.log('Validando formulario...');

            // Obtener los valores de los campos
            const pagina = document.getElementById('pagina_modificada').value;
            const descripcion = document.getElementById('descripcion').value;
            const archivo = document.getElementById('archivo');

            console.log('Valores obtenidos:');
            console.log('pagina_modificada:', pagina);
            console.log('descripcion:', descripcion);
            console.log('archivo:', archivo && archivo.files.length > 0 ? archivo.files[0].name : 'No seleccionado (opcional)');

            // Verificar si los campos obligatorios están vacíos
            if (!pagina || pagina.trim() === '') {
                alert('Por favor, seleccione una página modificada');
                document.getElementById('pagina_modificada').focus();
                return false;
            }

            if (!descripcion || descripcion.trim() === '') {
                alert('Por favor, ingrese una descripción del cambio');
                document.getElementById('descripcion').focus();
                return false;
            }

            // Verificar el archivo (opcional)
            if (archivo && archivo.files.length > 0) {
                const file = archivo.files[0];
                const fileSize = file.size / 1024 / 1024; // tamaño en MB

                // Verificar tamaño del archivo (opcional)
                if (fileSize > 100) {
                    alert('El archivo es demasiado grande. El tamaño máximo permitido es 100 MB.');
                    return false;
                }

                // Verificar tipo de archivo (opcional)
                const fileExt = file.name.split('.').pop().toLowerCase();
                const allowedImageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                const allowedVideoExts = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

                if (!allowedImageExts.includes(fileExt) && !allowedVideoExts.includes(fileExt)) {
                    alert('Tipo de archivo no permitido. Solo se permiten imágenes (jpg, jpeg, png, gif, webp) y videos (mp4, webm, ogg, mov, avi).');
                    return false;
                }
            }

            // Si todo está bien, mostrar un mensaje de carga
            document.getElementById('submitStatus').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            document.getElementById('submitStatus').style.color = '#17a2b8';

            // Permitir que el formulario se envíe
            return true;
        }

        // Función para enviar el formulario mediante Ajax
        function submitFormAjax(event) {
            event.preventDefault();

            // Validar el formulario primero
            if (!validarFormulario()) {
                return false;
            }

            // Mostrar indicador de carga
            const loadingIndicator = document.createElement('div');
            loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';
            loadingIndicator.style.padding = '10px';
            loadingIndicator.style.marginTop = '10px';
            loadingIndicator.style.backgroundColor = '#f8f9fa';
            loadingIndicator.style.borderRadius = '4px';
            document.getElementById('submitStatus').innerHTML = '';
            document.getElementById('submitStatus').appendChild(loadingIndicator);

            // Usar el FormData directamente del formulario
            const formData = new FormData(event.target);

            // Asegurarnos de que estos parámetros críticos estén presentes
            formData.set('ajax_request', 'true');
            // formData.set('debug_info', '1'); // Comentado: ya no necesitamos información de depuración
            formData.set('registrar_upgrade', '1');

            // Verificar si hay un archivo seleccionado
            const archivoInput = document.getElementById('archivo');
            if (archivoInput && archivoInput.files.length > 0) {
                const file = archivoInput.files[0];
                console.log('Archivo a enviar:', file.name, 'Tipo:', file.type, 'Tamaño:', (file.size / 1024 / 1024).toFixed(2) + 'MB');

                // Si es un archivo comprimido, usar ese en lugar del original
                if (compressedFile && file.type.startsWith('video/')) {
                    console.log('Usando archivo comprimido para el envío');
                    formData.set('archivo', compressedFile);
                }
            } else {
                console.log('No hay archivo seleccionado - esto es válido ya que el archivo es opcional');
                // Asegurarse de que el campo archivo esté vacío en el FormData
                if (formData.has('archivo')) {
                    formData.delete('archivo');
                }
            }

            // Mostrar los datos que se están enviando (para depuración)
            console.log('Enviando datos del formulario:', {
                pagina_modificada: formData.get('pagina_modificada'),
                descripcion: formData.get('descripcion'),
                archivo: formData.get('archivo') ? formData.get('archivo').name : 'No hay archivo'
            });

            // Usar Fetch API con timeout extendido para videos
            const controller = new AbortController();
            // Verificar si hay un archivo de video para aumentar el timeout
            let timeoutDuration = 120000; // 2 minutos por defecto para videos
            let isVideoFile = false;

            // Usar la variable archivoInput que ya existe en el DOM
            if (document.getElementById('archivo') && document.getElementById('archivo').files.length > 0) {
                const file = document.getElementById('archivo').files[0];
                console.log('Tipo de archivo:', file.type);

                if (file.type.startsWith('video/')) {
                    console.log('Archivo de video detectado, aumentando timeout');
                    timeoutDuration = 300000; // 5 minutos para videos
                    isVideoFile = true;
                }
            }

            console.log(`Configurando timeout de ${timeoutDuration/1000} segundos`);
            const timeoutId = setTimeout(() => {
                console.log('Timeout alcanzado, abortando solicitud');
                controller.abort();
            }, timeoutDuration);

            // Verificar si hay un archivo de video y mostrar advertencia
            if (isVideoFile) {
                const archivoElement = document.getElementById('archivo');
                const file = archivoElement.files[0];
                const fileSizeMB = file.size / 1024 / 1024;
                console.log(`Subiendo video de ${fileSizeMB.toFixed(2)}MB`);

                if (fileSizeMB > 10 && !compressedFile) {
                    if (!confirm(`Está intentando subir un video de ${fileSizeMB.toFixed(2)}MB sin comprimir. Esto puede tardar varios minutos. \n\n¿Desea continuar con la subida o prefiere comprimir el video primero?`)) {
                        loadingIndicator.remove();
                        document.getElementById('compressBtn').click();
                        return false;
                    }
                }

                // Mostrar mensaje de carga para videos
                document.getElementById('submitStatus').innerHTML += '<div style="margin-top:10px;color:#17a2b8;">Subiendo video, esto puede tardar varios minutos. Por favor, no cierre esta ventana.</div>';
            }

            // Enviar los datos usando FormData con manejo de errores mejorado
            console.log('Iniciando envío de datos...');

            // Mostrar mensaje de progreso para archivos grandes
            if (isVideoFile) {
                const progressMsg = document.createElement('div');
                progressMsg.id = 'uploadProgressMsg';
                progressMsg.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subiendo archivo, por favor espere...';
                progressMsg.style.marginTop = '10px';
                progressMsg.style.color = '#17a2b8';
                document.getElementById('submitStatus').appendChild(progressMsg);
            }

            fetch('registrar_upgrade.php', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            })
            .then(response => {
                console.log('Respuesta recibida:', response.status);
                // Store the status code so we can use it if JSON parsing fails
                const statusCode = response.status;

                // First try to clone the response for error handling
                const clonedResponse = response.clone();

                if (!response.ok) {
                    // If response is not ok, try to get the error message
                    return clonedResponse.text().then(errorText => {
                        console.error('Error response body:', errorText);

                        // Intentar parsear el error como JSON
                        try {
                            const errorJson = JSON.parse(errorText);
                            if (errorJson && errorJson.message) {
                                throw new Error(`Error del servidor: ${errorJson.message}`);
                            }
                        } catch (parseError) {
                            // Si no se puede parsear como JSON, usar el texto completo
                            console.log('No se pudo parsear la respuesta de error como JSON');
                        }

                        throw new Error(`Error HTTP: ${statusCode}. ${errorText.substring(0, 200)}...`);
                    });
                }

                // Try to parse JSON, but handle parsing errors
                return response.json().catch(error => {
                    // If JSON parsing fails, get the text and show that instead
                    return clonedResponse.text().then(text => {
                        console.error('Failed to parse JSON:', text);
                        throw new Error(`Failed to parse JSON response: ${error.message}. Raw response: ${text.substring(0, 100)}...`);
                    });
                });
            })
            .then(data => {
                console.log('Datos de respuesta:', data);
                loadingIndicator.remove();

                // Mostrar el resultado en el área de respuesta
                const ajaxResult = document.getElementById('ajaxResult');
                const ajaxResponse = document.getElementById('ajaxResponse');
                ajaxResult.style.display = 'block';
                ajaxResponse.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                // Check for success condition - check both 'status' and 'success' fields
                if (data.status === 'success' || data.success === true) {
                    // Mostrar mensaje de éxito
                    document.getElementById('formMessage').style.display = 'block';
                    document.getElementById('formMessage').innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' + (data.message || 'Operación exitosa') + '</div>';

                    // Display debug information if available (comentado)
                    /*
                    if (data.debug_messages && data.debug_messages.length > 0) {
                        const debugContainer = document.getElementById('debug-container');
                        const debugMessages = document.getElementById('debug-messages');

                        if (debugContainer && debugMessages) {
                            debugContainer.style.display = 'block';
                            debugMessages.innerHTML = '';

                            data.debug_messages.forEach(msg => {
                                const msgElement = document.createElement('div');
                                msgElement.style.marginBottom = '5px';
                                msgElement.style.padding = '3px';
                                msgElement.style.borderLeft = '3px solid #ccc';

                                switch(msg.type) {
                                    case 'error':
                                        msgElement.style.borderLeftColor = '#dc3545';
                                        msgElement.style.backgroundColor = '#f8d7da';
                                        break;
                                    case 'warning':
                                        msgElement.style.borderLeftColor = '#ffc107';
                                        msgElement.style.backgroundColor = '#fff3cd';
                                        break;
                                    case 'success':
                                        msgElement.style.borderLeftColor = '#28a745';
                                        msgElement.style.backgroundColor = '#d4edda';
                                        break;
                                    default:
                                        msgElement.style.borderLeftColor = '#17a2b8';
                                        msgElement.style.backgroundColor = '#f8f9fa';
                                }

                                msgElement.innerHTML = `<span style="color:#666;font-size:0.8em;">[${msg.time}]</span> ${msg.message}`;
                                debugMessages.appendChild(msgElement);
                            });

                            debugMessages.scrollTop = debugMessages.scrollHeight;
                        }
                    }
                    */

                    // Resetear formulario
                    event.target.reset();

                    // Cerrar el modal si está abierto
                    const upgradeModal = bootstrap.Modal.getInstance(document.getElementById('upgradeModal'));
                    if (upgradeModal) {
                        upgradeModal.hide();
                    }

                    // Mostrar mensaje de éxito y recargar la página después de un breve retraso
                    alert('Registro guardado correctamente');
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                } else {
                    document.getElementById('formMessage').style.display = 'block';
                    document.getElementById('formMessage').innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle"></i> Error: ' + (data.message || 'Error desconocido') + '</div>';

                    // Show details in debug container (comentado)
                    /*
                    const debugContainer = document.getElementById('debug-container');
                    const debugMessages = document.getElementById('debug-messages');

                    if (debugContainer && debugMessages) {
                        debugContainer.style.display = 'block';
                        debugMessages.innerHTML = '<div style="color:#721c24;background-color:#f8d7da;padding:10px;margin-bottom:10px;border-radius:5px;">Error en el procesamiento. Detalles:</div>';
                        debugMessages.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    }
                    */
                }
            })
            .catch(error => {
                loadingIndicator.remove();
                console.error('Error en la petición:', error);

                // Eliminar mensaje de progreso si existe
                const progressMsg = document.getElementById('uploadProgressMsg');
                if (progressMsg) progressMsg.remove();

                document.getElementById('formMessage').style.display = 'block';

                if (error.name === 'AbortError') {
                    document.getElementById('formMessage').innerHTML = '<div class="alert alert-danger"><i class="fas fa-clock"></i> La petición tardó demasiado tiempo. Por favor, inténtelo de nuevo.</div>';

                    // Sugerencias para archivos grandes
                    if (isVideoFile) {
                        document.getElementById('formMessage').innerHTML += '<div class="alert alert-info mt-2"><i class="fas fa-info-circle"></i> Sugerencias para archivos grandes:<ul><li>Intente comprimir el video antes de subirlo</li><li>Reduzca la duración o resolución del video</li><li>Verifique su conexión a internet</li></ul></div>';
                    }
                } else {
                    document.getElementById('formMessage').innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error al procesar la solicitud: ' + error.message + '</div>';

                    // Información adicional para depuración
                    const errorDetails = document.createElement('div');
                    errorDetails.className = 'alert alert-secondary mt-2';
                    errorDetails.innerHTML = '<strong>Detalles técnicos:</strong><br>Tipo de error: ' + error.name + '<br>Mensaje: ' + error.message;
                    document.getElementById('formMessage').appendChild(errorDetails);
                }

                // Show details in debug container (comentado)
                /*
                const debugContainer = document.getElementById('debug-container');
                const debugMessages = document.getElementById('debug-messages');

                if (debugContainer && debugMessages) {
                    debugContainer.style.display = 'block';
                    debugMessages.innerHTML = '<div style="color:#721c24;background-color:#f8d7da;padding:10px;margin-bottom:10px;border-radius:5px;">Error en la petición:</div>';
                    debugMessages.innerHTML += '<pre>' + error.stack + '</pre>';
                }
                */

                // Mostrar detalles adicionales en consola
                console.error('Detalles del error:', error);
            })
            .finally(() => {
                clearTimeout(timeoutId);
            });

            return false;
        }

        // Configurar eventos para videos en la tabla y mostrar mensajes de depuración
        // Función para alternar la visibilidad del contenedor de historial
        function toggleHistorialContainer(event) {
            // Evitar que el clic en el botón de nuevo registro active el toggle
            if (event.target.closest('.btn-primary')) {
                return;
            }

            const header = event.currentTarget;
            const container = header.closest('.config-card');
            container.classList.toggle('collapsed');

            // Guardar el estado en localStorage
            const isCollapsed = container.classList.contains('collapsed');
            localStorage.setItem('historialCollapsed', isCollapsed);
        }

        // Función para abrir el modal de registro de upgrades
        function openUpgradeModal(event) {
            // Evitar que el clic en el botón propague al header
            if (event) {
                event.stopPropagation();
            }

            // Obtener el elemento del modal
            const modalElement = document.getElementById('upgradeModal');

            // Asegurarse de que no haya instancias previas del modal
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // Crear una nueva instancia del modal con opciones mejoradas
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,      // Mostrar backdrop oscuro
                keyboard: true,       // El modal se puede cerrar con la tecla Escape
                focus: true           // Enfocar el modal al abrirlo
            });

            // Limpiar el formulario antes de mostrar el modal
            const form = document.getElementById('upgradeForm');
            if (form) {
                form.reset();
            }

            // Agregar clase para centrar el modal verticalmente
            modalElement.querySelector('.modal-dialog').classList.add('modal-dialog-centered');

            // Mostrar el modal con efecto de fade
            modal.show();

            // Asegurarse de que el modal esté visible y centrado
            setTimeout(() => {
                modalElement.classList.add('show');
                document.body.classList.add('modal-open');

                // Enfocar el primer campo del formulario
                const firstInput = form.querySelector('select, input, textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 50);
        }

        // Función para confirmar y eliminar un registro de upgrade
        function confirmDeleteUpgrade(id) {
            console.log(`Solicitando confirmación para eliminar registro ID: ${id}`);

            if (confirm(`¿Está seguro que desea eliminar el registro con ID: ${id}?`)) {
                console.log(`Confirmación recibida, procediendo a eliminar registro ID: ${id}`);
                deleteUpgrade(id);
            } else {
                console.log(`Eliminación cancelada por el usuario para el registro ID: ${id}`);
            }
        }

        // Función para eliminar un registro de upgrade
        async function deleteUpgrade(id) {
            try {
                console.log('Iniciando eliminación del registro con ID:', id);

                // Mostrar indicador de carga
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'deleteLoadingIndicator';
                loadingIndicator.innerHTML = '<div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div style="background-color: white; padding: 20px; border-radius: 5px; text-align: center;"><i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px; color: #3498db;"></i><div>Eliminando registro...</div></div></div>';
                document.body.appendChild(loadingIndicator);

                const formData = new FormData();
                formData.append('id', id);

                console.log('Enviando solicitud a eliminar_upgrade_v2.php');
                let response;
                try {
                    response = await fetch('eliminar_upgrade_v2.php', {
                        method: 'POST',
                        body: formData
                    });

                    console.log('Respuesta recibida, código de estado:', response.status);
                } catch (fetchError) {
                    console.error('Error de red al enviar la solicitud:', fetchError);
                    throw new Error('Error de conexión al servidor. Por favor, verifique su conexión a internet.');
                }

                // Verificar si la respuesta es válida
                if (!response.ok) {
                    console.error('Error HTTP:', response.status, response.statusText);

                    // Intentar obtener más información sobre el error
                    let errorText = await response.text();
                    console.error('Contenido de la respuesta de error:', errorText);

                    throw new Error(`Error HTTP: ${response.status} ${response.statusText}. Detalles: ${errorText.substring(0, 100)}...`);
                }

                // Intentar parsear la respuesta como JSON
                let data;
                let responseText;
                try {
                    responseText = await response.text();
                    console.log('Respuesta en texto:', responseText);

                    // Verificar si la respuesta está vacía
                    if (!responseText.trim()) {
                        throw new Error('La respuesta del servidor está vacía');
                    }

                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Error al parsear JSON:', parseError);
                    console.error('Texto recibido:', responseText);
                    throw new Error(`Error al procesar la respuesta del servidor: ${parseError.message}. Texto recibido: ${responseText.substring(0, 100)}...`);
                }

                console.log('Datos de respuesta:', data);

                // Eliminar el indicador de carga
                document.body.removeChild(loadingIndicator);

                if (data.status === 'success') {
                    // Mostrar mensaje de éxito
                    console.log('Eliminación exitosa');
                    alert('Registro eliminado correctamente');

                    // Recargar la página para actualizar la tabla
                    location.reload();
                } else {
                    // Mostrar mensaje de error
                    console.error('Error en la respuesta:', data.message);
                    alert('Error al eliminar el registro: ' + data.message);
                }
            } catch (error) {
                console.error('Error en la función deleteUpgrade:', error);

                // Eliminar el indicador de carga si existe
                const loadingIndicator = document.getElementById('deleteLoadingIndicator');
                if (loadingIndicator) {
                    document.body.removeChild(loadingIndicator);
                }

                alert('Error al eliminar el registro: ' + error.message);
            }
        }

        // Variables para la edición
        let editSelectedFile = null;
        let editCompressedFile = null;
        let currentEditId = null;

        // Función para abrir el modal de edición y cargar los datos
        async function editUpgrade(id) {
            try {
                console.log('Iniciando edición del registro con ID:', id);
                currentEditId = id;

                // Mostrar indicador de carga
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'editLoadingIndicator';
                loadingIndicator.innerHTML = '<div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div style="background-color: white; padding: 20px; border-radius: 5px; text-align: center;"><i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px; color: #3498db;"></i><div>Cargando datos...</div></div></div>';
                document.body.appendChild(loadingIndicator);

                // Obtener los datos del registro
                const response = await fetch(`obtener_upgrade.php?id=${id}`);

                if (!response.ok) {
                    throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();

                // Eliminar el indicador de carga
                document.body.removeChild(loadingIndicator);

                if (data.status !== 'success') {
                    throw new Error(data.message || 'Error al obtener los datos del registro');
                }

                const registro = data.data;
                console.log('Datos del registro obtenidos:', registro);

                // Llenar el formulario con los datos
                document.getElementById('edit_id').value = registro.id;
                document.getElementById('edit_pagina_modificada').value = registro.pagina_modificada;
                // Simplemente asignar la descripción tal como viene
                document.getElementById('edit_descripcion').value = registro.descripcion;

                // Mostrar el archivo actual si existe
                const archivoContainer = document.getElementById('edit_archivo_actual_container');
                archivoContainer.innerHTML = '';

                if (registro.ruta_archivo && registro.tipo_archivo !== 'ninguno') {
                    if (registro.tipo_archivo === 'imagen') {
                        // Mostrar imagen
                        const img = document.createElement('img');
                        img.src = registro.ruta_archivo;
                        img.alt = 'Imagen actual';
                        img.style.maxWidth = '100%';
                        img.style.maxHeight = '200px';
                        img.style.cursor = 'pointer';
                        img.onclick = function() { mostrarImagen(registro.ruta_archivo); };
                        archivoContainer.appendChild(img);
                    } else if (registro.tipo_archivo === 'video') {
                        // Mostrar video
                        const video = document.createElement('video');
                        video.controls = true;
                        video.style.maxWidth = '100%';
                        video.style.maxHeight = '200px';

                        const source = document.createElement('source');
                        source.src = registro.ruta_archivo;
                        source.type = 'video/' + registro.ruta_archivo.split('.').pop();

                        video.appendChild(source);
                        video.innerHTML += 'Tu navegador no soporta el tag de video.';

                        archivoContainer.appendChild(video);
                    }
                } else {
                    archivoContainer.innerHTML = '<p>No hay archivo asociado a este registro</p>';
                }

                // Limpiar el input de archivo
                document.getElementById('edit_archivo').value = '';

                // Ocultar elementos de compresión y vista previa
                document.getElementById('edit_fileSizeWarning').style.display = 'none';
                document.getElementById('edit_videoPreviewContainer').style.display = 'none';
                document.getElementById('edit_compressionContainer').style.display = 'none';

                // Abrir el modal
                const modal = document.getElementById('editUpgradeModal');
                modal.classList.add('show');
                modal.style.display = 'flex';
                document.body.classList.add('modal-open');

                // Enfocar el primer campo del formulario
                document.getElementById('edit_pagina_modificada').focus();
            } catch (error) {
                console.error('Error al cargar los datos para edición:', error);
                alert('Error al cargar los datos: ' + error.message);

                // Eliminar el indicador de carga si existe
                const loadingIndicator = document.getElementById('editLoadingIndicator');
                if (loadingIndicator) {
                    document.body.removeChild(loadingIndicator);
                }
            }
        }

        // Función para manejar la selección de archivos en el formulario de edición
        function handleEditFileSelect(input) {
            console.log('handleEditFileSelect llamado');
            try {
                const file = input.files[0];
                if (!file) {
                    console.log('No se seleccionó ningún archivo');
                    return;
                }

                console.log('Archivo seleccionado:', file.name, 'Tipo:', file.type, 'Tamaño:', (file.size / 1024 / 1024).toFixed(2) + 'MB');

                // Guardar referencia al archivo seleccionado
                editSelectedFile = file;
                editCompressedFile = null;

                // Ocultar elementos de compresión
                document.getElementById('edit_compressionContainer').style.display = 'none';
                document.getElementById('edit_compressBtn').style.display = 'none';
                document.getElementById('edit_fileSizeWarning').style.display = 'none';
                document.getElementById('edit_videoPreviewContainer').style.display = 'none';

                // Verificar si es un video
                if (file.type.startsWith('video/')) {
                    console.log('Archivo de video detectado');

                    // Mostrar vista previa del video
                    const videoPreview = document.getElementById('edit_videoPreview');
                    videoPreview.src = URL.createObjectURL(file);
                    document.getElementById('edit_videoPreviewContainer').style.display = 'block';

                    // Verificar tamaño del archivo
                    const fileSizeMB = file.size / 1024 / 1024;
                    console.log(`Tamaño del archivo de video: ${fileSizeMB.toFixed(2)}MB`);

                    if (fileSizeMB > 10) {
                        console.log('Archivo de video grande detectado, mostrando opciones de compresión');
                        document.getElementById('edit_fileSizeWarning').style.display = 'block';
                        document.getElementById('edit_compressBtn').style.display = 'inline-block';
                    }
                }
            } catch (error) {
                console.error('Error en handleEditFileSelect:', error);
            }
        }

        // Función para comprimir el video en el formulario de edición
        function compressEditVideo() {
            if (!editSelectedFile || !editSelectedFile.type.startsWith('video/')) {
                alert('Por favor, seleccione un archivo de video primero.');
                return;
            }

            // Mostrar el contenedor de compresión
            const compressionContainer = document.getElementById('edit_compressionContainer');
            compressionContainer.style.display = 'block';

            // Inicializar el compresor con configuración mejorada
            const compressor = new VideoCompressor({
                maxSizeMB: 10,
                maxWidthOrHeight: 1280,
                fileType: 'video/mp4', // Cambiar a MP4 para mejor compatibilidad
                quality: 0.7,
                debug: true,
                statusElement: document.getElementById('edit_compressionStatus'),
                progressElement: document.getElementById('edit_compressionProgress')
            });

            // Mostrar mensaje de inicio
            document.getElementById('edit_compressionStatus').textContent = 'Iniciando compresión...';

            // Iniciar la compresión con manejo de errores mejorado
            try {
                compressor.compress(editSelectedFile)
                    .then(result => {
                        editCompressedFile = result;

                        // Actualizar información
                        const originalSize = (editSelectedFile.size / 1024 / 1024).toFixed(2);
                        const compressedSize = (editCompressedFile.size / 1024 / 1024).toFixed(2);
                        const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(0);

                        document.getElementById('edit_compressionInfo').textContent =
                            `Original: ${originalSize}MB, Comprimido: ${compressedSize}MB (${ratio}% reducido)`;

                        // Actualizar la vista previa
                        const videoPreview = document.getElementById('edit_videoPreview');
                        videoPreview.src = URL.createObjectURL(editCompressedFile);

                        // Reemplazar el archivo en el input
                        replaceEditFileInput(editCompressedFile);

                        // Mensaje de éxito
                        document.getElementById('edit_compressionStatus').textContent = 'Compresión completada con éxito';
                    })
                    .catch(error => {
                        console.error('Error al comprimir el video:', error);
                        document.getElementById('edit_compressionStatus').textContent = 'Error: ' + error.message;
                        alert('Error al comprimir el video: ' + error.message + '. Intente con un archivo más pequeño o en otro formato.');
                    });
            } catch (error) {
                console.error('Error al iniciar la compresión:', error);
                document.getElementById('edit_compressionStatus').textContent = 'Error al iniciar la compresión: ' + error.message;
                alert('Error al iniciar la compresión del video. Intente con un archivo más pequeño o en otro formato.');
            }
        }

        // Función para cancelar la compresión en el formulario de edición
        function cancelEditCompression() {
            document.getElementById('edit_compressionContainer').style.display = 'none';
        }

        // Función para reemplazar el archivo en el input de edición
        function replaceEditFileInput(file) {
            // Crear un nuevo objeto DataTransfer
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);

            // Reemplazar los archivos en el input
            document.getElementById('edit_archivo').files = dataTransfer.files;
        }

        // Función para validar el formulario de edición antes de enviarlo
        function validarEditFormulario() {
            console.log('Validando formulario de edición...');

            // Obtener los valores de los campos
            const pagina = document.getElementById('edit_pagina_modificada').value;
            const descripcion = document.getElementById('edit_descripcion').value;
            const archivo = document.getElementById('edit_archivo');

            console.log('Valores obtenidos:');
            console.log('pagina_modificada:', pagina);
            console.log('descripcion:', descripcion);
            console.log('archivo:', archivo && archivo.files.length > 0 ? archivo.files[0].name : 'No seleccionado (opcional)');

            // Verificar si los campos obligatorios están vacíos
            if (!pagina || pagina.trim() === '') {
                alert('Por favor, seleccione una página modificada');
                document.getElementById('edit_pagina_modificada').focus();
                return false;
            }

            if (!descripcion || descripcion.trim() === '') {
                alert('Por favor, ingrese una descripción del cambio');
                document.getElementById('edit_descripcion').focus();
                return false;
            }

            // Verificar el archivo (opcional)
            if (archivo && archivo.files.length > 0) {
                const file = archivo.files[0];
                const fileSize = file.size / 1024 / 1024; // tamaño en MB

                // Verificar tamaño del archivo (opcional)
                if (fileSize > 100) {
                    alert('El archivo es demasiado grande. El tamaño máximo permitido es 100 MB.');
                    return false;
                }

                // Verificar tipo de archivo (opcional)
                const fileExt = file.name.split('.').pop().toLowerCase();
                const allowedImageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                const allowedVideoExts = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

                if (!allowedImageExts.includes(fileExt) && !allowedVideoExts.includes(fileExt)) {
                    alert('Tipo de archivo no permitido. Solo se permiten imágenes (jpg, jpeg, png, gif, webp) y videos (mp4, webm, ogg, mov, avi).');
                    return false;
                }
            }

            // Si todo está bien, mostrar un mensaje de carga
            document.getElementById('editSubmitStatus').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            document.getElementById('editSubmitStatus').style.color = '#17a2b8';

            // Permitir que el formulario se envíe
            return true;
        }

        // Función para enviar el formulario de edición mediante Ajax
        async function submitEditFormAjax(event) {
            event.preventDefault();

            // Validar el formulario primero
            if (!validarEditFormulario()) {
                return false;
            }

            // Mostrar indicador de carga
            const loadingIndicator = document.createElement('div');
            loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Procesando...';
            loadingIndicator.style.padding = '10px';
            loadingIndicator.style.marginTop = '10px';
            loadingIndicator.style.backgroundColor = '#f8f9fa';
            loadingIndicator.style.borderRadius = '4px';
            document.getElementById('editSubmitStatus').innerHTML = '';
            document.getElementById('editSubmitStatus').appendChild(loadingIndicator);

            // Usar el FormData directamente del formulario
            const formData = new FormData(event.target);

            // Verificar si hay un archivo seleccionado
            const archivoInput = document.getElementById('edit_archivo');
            if (archivoInput && archivoInput.files.length > 0) {
                const file = archivoInput.files[0];
                console.log('Archivo a enviar:', file.name, 'Tipo:', file.type, 'Tamaño:', (file.size / 1024 / 1024).toFixed(2) + 'MB');

                // Si es un archivo comprimido, usar ese en lugar del original
                if (editCompressedFile && file.type.startsWith('video/')) {
                    console.log('Usando archivo comprimido para el envío');
                    formData.set('archivo', editCompressedFile);
                }
            }

            try {
                // Enviar los datos usando FormData
                console.log('Iniciando envío de datos...');

                const response = await fetch('editar_upgrade.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`Error HTTP: ${response.status} ${response.statusText}`);
                }

                const responseText = await response.text();
                console.log('Respuesta en texto:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Error al parsear JSON:', parseError);
                    throw new Error('Error al procesar la respuesta del servidor');
                }

                console.log('Respuesta del servidor:', data);

                if (data.success) {
                    // Mostrar mensaje de éxito
                    document.getElementById('editSubmitStatus').innerHTML = '<div style="color: #28a745;"><i class="fas fa-check-circle"></i> Registro actualizado correctamente</div>';

                    // Cerrar el modal después de un breve retraso
                    setTimeout(() => {
                        // Cerrar el modal
                        const modal = document.getElementById('editUpgradeModal');
                        modal.classList.remove('show');
                        modal.style.display = 'none';
                        document.body.classList.remove('modal-open');

                        // Recargar la página para mostrar los cambios
                        location.reload();
                    }, 1500);
                } else {
                    // Mostrar mensaje de error
                    document.getElementById('editSubmitStatus').innerHTML = `<div style="color: #dc3545;"><i class="fas fa-exclamation-circle"></i> ${data.message || 'Error al actualizar el registro'}</div>`;
                }
            } catch (error) {
                console.error('Error al enviar el formulario:', error);
                document.getElementById('editSubmitStatus').innerHTML = `<div style="color: #dc3545;"><i class="fas fa-exclamation-circle"></i> ${error.message}</div>`;
            }

            return false;
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded: Inicializando elementos de la interfaz');

            // Cargar el estado guardado del contenedor de historial
            // Si no hay estado guardado, establecer como contraído por defecto
            if (localStorage.getItem('historialCollapsed') === null) {
                localStorage.setItem('historialCollapsed', 'true');
            }

            const isHistorialCollapsed = localStorage.getItem('historialCollapsed') === 'true';

            // Buscar el contenedor de historial
            const headers = document.querySelectorAll('.collapsible-header');
            headers.forEach(header => {
                const container = header.closest('.config-card');
                if (container) {
                    // Si debe estar expandido, quitar la clase collapsed
                    if (!isHistorialCollapsed) {
                        container.classList.remove('collapsed');
                    }
                }
            });

            // Asegurarse de que los modales estén ocultos al cargar la página
            const modales = [document.getElementById('upgradeModal'), document.getElementById('editUpgradeModal')];

            modales.forEach(modal => {
                if (modal) {
                    // Ocultar el modal manualmente
                    modal.style.display = 'none';
                    modal.classList.remove('show');

                    // Configurar el botón de cierre del modal
                    const closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"]');
                    closeButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const modalInstance = bootstrap.Modal.getInstance(modal);
                            if (modalInstance) {
                                modalInstance.hide();
                            } else {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                document.body.classList.remove('modal-open');

                                // Eliminar backdrop
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) {
                                    backdrop.parentNode.removeChild(backdrop);
                                }
                            }
                        });
                    });
                }
            });

            // Eliminar cualquier backdrop del modal que pueda estar presente
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.parentNode.removeChild(backdrop);
            }

            // Asegurarse de que el body no tenga la clase modal-open
            document.body.classList.remove('modal-open');

            // Inicializar elementos de la interfaz con manejo de errores
            try {
                // Ocultar elementos de compresión y vista previa
                const elementsToHide = [
                    'compressionContainer',
                    'compressBtn',
                    'fileSizeWarning',
                    'videoPreviewContainer'
                ];

                elementsToHide.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.style.display = 'none';
                    } else {
                        console.warn(`Elemento con ID '${id}' no encontrado en el DOM`);
                    }
                });

                // Asegurarnos de que el input de archivo tenga el evento onchange correctamente configurado
                const archivoInput = document.getElementById('archivo');
                if (archivoInput) {
                    console.log('Configurando evento onchange para el input de archivo');
                    archivoInput.onchange = function() { handleFileSelect(this); };
                } else {
                    console.warn('Input de archivo no encontrado en el DOM');
                }

                // Agregar evento click a los videos en la tabla
                const tableVideos = document.querySelectorAll('.upgrade-video');
                tableVideos.forEach(video => {
                    video.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        const videoSrc = video.querySelector('source').src;
                        mostrarVideo(videoSrc);
                    });
                });
            } catch (error) {
                console.error('Error al inicializar elementos de la interfaz:', error);
            }

            // Mostrar el menú desplegable del usuario al hacer clic en el icono
            const userIcon = document.querySelector('.fa-user');
            const userDropdown = document.querySelector('.user-dropdown');

            if (userIcon && userDropdown) {
                userIcon.addEventListener('click', function() {
                    userDropdown.classList.toggle('active');
                });

                // Cerrar el menú al hacer clic fuera de él
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.icon-container')) {
                        userDropdown.classList.remove('active');
                    }
                });
            }

            // Mostrar mensajes de depuración (comentado)
            /*
            const debugContainer = document.getElementById('debug-messages');
            if (debugContainer) {
                // Obtener mensajes de depuración del PHP
                const debugMessages = <?php echo json_encode($debugMessages); ?>;

                if (debugMessages && debugMessages.length > 0) {
                    // Mostrar el contenedor de depuración si hay mensajes
                    document.getElementById('debug-container').style.display = 'block';

                    // Agregar cada mensaje al contenedor
                    debugMessages.forEach(function(msg) {
                        const msgElement = document.createElement('div');
                        msgElement.style.marginBottom = '5px';
                        msgElement.style.padding = '3px';
                        msgElement.style.borderLeft = '3px solid #ccc';

                        // Aplicar estilo según el tipo de mensaje
                        switch(msg.type) {
                            case 'error':
                                msgElement.style.borderLeftColor = '#dc3545';
                                msgElement.style.backgroundColor = '#f8d7da';
                                break;
                            case 'warning':
                                msgElement.style.borderLeftColor = '#ffc107';
                                msgElement.style.backgroundColor = '#fff3cd';
                                break;
                            case 'success':
                                msgElement.style.borderLeftColor = '#28a745';
                                msgElement.style.backgroundColor = '#d4edda';
                                break;
                            default:
                                msgElement.style.borderLeftColor = '#17a2b8';
                                msgElement.style.backgroundColor = '#f8f9fa';
                        }

                        msgElement.innerHTML = `<span style="color:#666;font-size:0.8em;">[${msg.time}]</span> ${msg.message}`;
                        debugContainer.appendChild(msgElement);
                    });

                    // Desplazar al final para mostrar los mensajes más recientes
                    debugContainer.scrollTop = debugContainer.scrollHeight;
                }
            }
            */
        });
    </script>
</body>
</html>


