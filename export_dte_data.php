<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';
require_once 'db_connection.php';

// Configurar headers para prevenir caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Obtener el mes seleccionado del parámetro GET
$mes = isset($_GET['mes']) ? $_GET['mes'] : 'current';

try {
    $conn = getConnection();
    
    // Construir la consulta SQL basada en el filtro de mes
    if ($mes === 'current') {
        // Obtener los últimos 30 registros
        $sql = "
            SELECT DTE.*
            , ENVIOS.estado_envio
            , ENVIOS.glosa
            , ENVIOS.trackid
            , ENVIOS.fecha_envio
            , COALESCE(JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Totales.MontoTotal')), 0) as monto_total
            , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.Rut')) as rut_receptor
            , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.RazonSocial')) as razon_social_receptor
            FROM tb_facturas_dte DTE
            LEFT JOIN tb_sobre_envios ENVIOS
            ON DTE.id_sobre = ENVIOS.id
            ORDER BY fecha_generacion DESC
            LIMIT 30
        ";
        $stmt = $conn->prepare($sql);
    } else {
        // Filtrar por el mes seleccionado (formato: YYYY-MM)
        list($year, $month) = explode('-', $mes);
        $sql = "
            SELECT DTE.*
            , ENVIOS.estado_envio
            , ENVIOS.glosa
            , ENVIOS.trackid
            , ENVIOS.fecha_envio
            , COALESCE(JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Totales.MontoTotal')), 0) as monto_total
            , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.Rut')) as rut_receptor
            , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.RazonSocial')) as razon_social_receptor
            FROM tb_facturas_dte DTE
            LEFT JOIN tb_sobre_envios ENVIOS
            ON DTE.id_sobre = ENVIOS.id
            WHERE YEAR(fecha_generacion) = ? AND MONTH(fecha_generacion) = ?
            ORDER BY fecha_generacion DESC
        ";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(1, $year, PDO::PARAM_INT);
        $stmt->bindParam(2, $month, PDO::PARAM_INT);
    }
    
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($result) > 0) {
        // Generar la tabla HTML para Excel
        $output = '
            <table class="table" bordered="1">
                <tr>
                    <th>Tipo DTE</th>
                    <th>Folio</th>
                    <th>Fecha Generación</th>
                    <th>RUT Receptor</th>
                    <th>Razón Social</th>
                    <th>Monto Total</th>
                    <th>Estado Sobre</th>
                    <th>ID Sobre</th>
                    <th>Track ID</th>
                    <th>ID Voucher</th>
                </tr>';

        foreach ($result as $row) {
            // Determinar el tipo de DTE en texto
            $tipo_dte_texto = '';
            switch ($row["tipo_dte"]) {
                case 33:
                    $tipo_dte_texto = 'Factura (33)';
                    break;
                case 39:
                    $tipo_dte_texto = 'Boleta (39)';
                    break;
                case 61:
                    $tipo_dte_texto = 'Nota Crédito (61)';
                    break;
                case 56:
                    $tipo_dte_texto = 'Nota Débito (56)';
                    break;
                default:
                    $tipo_dte_texto = $row["tipo_dte"];
            }
            
            // Determinar el estado del sobre en texto
            $estado_sobre_texto = '';
            if ($row["estado_sobre"] == 1) {
                $estado_sobre_texto = 'Generado';
            } elseif ($row["estado_sobre"] == 0) {
                $estado_sobre_texto = 'Pendiente';
            } else {
                $estado_sobre_texto = $row["estado_sobre"];
            }
            
            $output .= '
                <tr>
                    <td>' . $tipo_dte_texto . '</td>
                    <td style="mso-number-format:\'@\';">' . $row["folio"] . '</td>
                    <td>' . $row["fecha_generacion"] . '</td>
                    <td>' . ($row["rut_receptor"] ?? '') . '</td>
                    <td>' . ($row["razon_social_receptor"] ?? '') . '</td>
                    <td>$' . number_format(intval($row["monto_total"] ?? 0), 0, ',', '.') . '</td>
                    <td>' . $estado_sobre_texto . '</td>
                    <td>' . ($row["id_sobre"] ?? '') . '</td>
                    <td>' . ($row["trackid"] ?? 'N/A') . '</td>
                    <td>' . ($row["id_voucher"] ?? 'N/A') . '</td>
                </tr>';
        }

        $output .= '</table>';

        // Determinar el nombre del archivo basado en el mes
        if ($mes === 'current') {
            $filename = 'DTE_ultimos_30.xls';
        } else {
            $filename = 'DTE_' . $mes . '.xls';
        }

        // Enviar los headers para descarga
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename=' . $filename);
        echo $output;
        exit;
    } else {
        // No hay datos para exportar
        header('Content-Type: text/html');
        echo '<h3>No hay datos para exportar en el período seleccionado.</h3>';
        echo '<p><a href="ventas.php">Volver a la página de ventas</a></p>';
        exit;
    }
} catch (Exception $e) {
    header('Content-Type: text/html');
    echo '<h3>Error al exportar los datos</h3>';
    echo '<p>Mensaje de error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p><a href="ventas.php">Volver a la página de ventas</a></p>';
    exit;
}
?>