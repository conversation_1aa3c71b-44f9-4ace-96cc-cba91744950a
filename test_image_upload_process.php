<?php
// Script para procesar la subida de imágenes de prueba
require_once 'image_processor.php';

// Mostrar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Resultado de la prueba de subida de imagen</h1>";

// Verificar si se ha enviado un archivo
if (!isset($_FILES['imagen']) || $_FILES['imagen']['error'] !== UPLOAD_ERR_OK) {
    echo "<p style='color:red'>Error: No se ha enviado ninguna imagen o ha ocurrido un error en la subida.</p>";
    if (isset($_FILES['imagen'])) {
        echo "<p>Código de error: " . $_FILES['imagen']['error'] . "</p>";

        // Mostrar mensaje de error según el código
        switch ($_FILES['imagen']['error']) {
            case UPLOAD_ERR_INI_SIZE:
                echo "<p>El archivo excede el tamaño máximo permitido por php.ini.</p>";
                break;
            case UPLOAD_ERR_FORM_SIZE:
                echo "<p>El archivo excede el tamaño máximo permitido por el formulario.</p>";
                break;
            case UPLOAD_ERR_PARTIAL:
                echo "<p>El archivo se subió parcialmente.</p>";
                break;
            case UPLOAD_ERR_NO_FILE:
                echo "<p>No se subió ningún archivo.</p>";
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                echo "<p>Falta la carpeta temporal.</p>";
                break;
            case UPLOAD_ERR_CANT_WRITE:
                echo "<p>No se pudo escribir el archivo en el disco.</p>";
                break;
            case UPLOAD_ERR_EXTENSION:
                echo "<p>Una extensión de PHP detuvo la subida del archivo.</p>";
                break;
        }
    }
    exit;
}

// Mostrar información del archivo subido
echo "<h2>Información del archivo subido</h2>";
echo "<ul>";
echo "<li>Nombre: " . htmlspecialchars($_FILES['imagen']['name']) . "</li>";
echo "<li>Tipo: " . htmlspecialchars($_FILES['imagen']['type']) . "</li>";
echo "<li>Tamaño: " . htmlspecialchars($_FILES['imagen']['size']) . " bytes</li>";
echo "<li>Ruta temporal: " . htmlspecialchars($_FILES['imagen']['tmp_name']) . "</li>";
echo "</ul>";

// Directorio para guardar las imágenes
$uploadDir = 'images/fotos_repuestos/';
$absoluteUploadDir = __DIR__ . '/' . $uploadDir;

// Asegurarse de que el directorio termine con una barra
if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
    $absoluteUploadDir .= '/';
}

echo "<h2>Procesamiento de la imagen</h2>";
echo "<p>Directorio de destino: <code>" . htmlspecialchars($absoluteUploadDir) . "</code></p>";

// Verificar que el directorio existe y tiene permisos
if (!file_exists($absoluteUploadDir)) {
    echo "<p style='color:red'>Error: El directorio no existe.</p>";

    // Intentar crear el directorio
    echo "<p>Intentando crear el directorio...</p>";
    if (mkdir($absoluteUploadDir, 0777, true)) {
        echo "<p style='color:green'>Directorio creado correctamente.</p>";
    } else {
        echo "<p style='color:red'>No se pudo crear el directorio.</p>";
        exit;
    }
}

if (!is_writable($absoluteUploadDir)) {
    echo "<p style='color:red'>Error: El directorio no tiene permisos de escritura.</p>";

    // Intentar cambiar permisos
    echo "<p>Intentando cambiar permisos...</p>";
    if (chmod($absoluteUploadDir, 0777)) {
        echo "<p style='color:green'>Permisos cambiados correctamente.</p>";
    } else {
        echo "<p style='color:red'>No se pudieron cambiar los permisos.</p>";
        exit;
    }
}

// Método 1: Usar la clase ImageProcessor
echo "<h3>Método 1: Usando la clase ImageProcessor</h3>";

try {
    $imageResult = ImageProcessor::processUploadedImage(
        $_FILES['imagen'],
        $absoluteUploadDir,
        'test_',  // Prefijo para el nombre del archivo
        800,      // Ancho máximo
        800,      // Alto máximo
        85        // Calidad
    );

    if ($imageResult['success']) {
        echo "<p style='color:green'>✓ Imagen procesada correctamente usando ImageProcessor.</p>";
        echo "<p>Ruta de la imagen: <code>" . htmlspecialchars($imageResult['path']) . "</code></p>";

        // Mostrar la imagen
        $webPath = str_replace(__DIR__, '', $imageResult['path']);
        echo "<p>Vista previa de la imagen:</p>";
        echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
    } else {
        echo "<p style='color:red'>✗ Error al procesar la imagen usando ImageProcessor: " . htmlspecialchars($imageResult['message']) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Excepción al procesar la imagen: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Método 2: Subida directa sin procesamiento
echo "<h3>Método 2: Subida directa sin procesamiento</h3>";

// Generar nombre único para el archivo
$fileExtension = strtolower(pathinfo($_FILES['imagen']['name'], PATHINFO_EXTENSION));
$fileName = 'direct_' . uniqid() . '_' . time() . '.' . $fileExtension;
$uploadFile = $absoluteUploadDir . $fileName;

try {
    // Crear una copia del archivo temporal para evitar problemas con move_uploaded_file
    $tempFile = $_FILES['imagen']['tmp_name'];

    // Verificar que el archivo temporal existe
    if (!file_exists($tempFile)) {
        throw new Exception("El archivo temporal no existe: " . $tempFile);
    }

    echo "<p>Intentando copiar archivo desde: <code>" . htmlspecialchars($tempFile) . "</code></p>";
    echo "<p>Hacia: <code>" . htmlspecialchars($uploadFile) . "</code></p>";

    // Intentar copiar el archivo en lugar de moverlo
    if (copy($tempFile, $uploadFile)) {
        echo "<p style='color:green'>✓ Imagen copiada correctamente usando copy().</p>";
        echo "<p>Ruta de la imagen: <code>" . htmlspecialchars($uploadFile) . "</code></p>";

        // Mostrar la imagen
        $webPath = str_replace(__DIR__, '', $uploadFile);
        echo "<p>Vista previa de la imagen:</p>";
        echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
    }
    // Si copy falla, intentar con move_uploaded_file
    else if (move_uploaded_file($tempFile, $uploadFile)) {
        echo "<p style='color:green'>✓ Imagen subida correctamente usando move_uploaded_file().</p>";
        echo "<p>Ruta de la imagen: <code>" . htmlspecialchars($uploadFile) . "</code></p>";

        // Mostrar la imagen
        $webPath = str_replace(__DIR__, '', $uploadFile);
        echo "<p>Vista previa de la imagen:</p>";
        echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
    }
    // Si ambos métodos fallan, intentar con file_put_contents
    else {
        $fileContent = file_get_contents($tempFile);
        if ($fileContent !== false && file_put_contents($uploadFile, $fileContent)) {
            echo "<p style='color:green'>✓ Imagen guardada correctamente usando file_put_contents().</p>";
            echo "<p>Ruta de la imagen: <code>" . htmlspecialchars($uploadFile) . "</code></p>";

            // Mostrar la imagen
            $webPath = str_replace(__DIR__, '', $uploadFile);
            echo "<p>Vista previa de la imagen:</p>";
            echo "<img src='" . htmlspecialchars($webPath) . "' style='max-width: 300px; max-height: 300px;' />";
        } else {
            echo "<p style='color:red'>✗ Error al subir la imagen usando todos los métodos disponibles.</p>";

            // Mostrar más detalles sobre el error
            echo "<p>Detalles:</p>";
            echo "<ul>";
            echo "<li>Permisos del directorio: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</li>";
            echo "<li>Archivo temporal existe: " . (file_exists($tempFile) ? 'Sí' : 'No') . "</li>";
            echo "<li>Tamaño del archivo temporal: " . (file_exists($tempFile) ? filesize($tempFile) . ' bytes' : 'N/A') . "</li>";
            echo "<li>Es escribible el directorio: " . (is_writable($absoluteUploadDir) ? 'Sí' : 'No') . "</li>";
            echo "<li>Espacio libre en disco: " . disk_free_space('/') . " bytes</li>";
            echo "</ul>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Excepción al subir la imagen: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<p><a href='test_image_upload.php'>Volver al formulario de prueba</a></p>";
?>
