// Funcionalidad para cambiar entre vistas de tarjetas y tabla

// Función global para establecer la vista activa
function setActiveView(viewType) {
    console.log('Estableciendo vista activa:', viewType);

    const productsGrid = document.querySelector('.products-grid');
    const productsTable = document.querySelector('.products-table');

    if (!productsGrid || !productsTable) {
        console.error('No se encontraron los elementos de vista');
        return;
    }

    // Actualizar botones activos
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(btn => {
        if (btn.dataset.view === viewType) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });

    // Usar clases en el body para controlar la visualización
    const body = document.body;

    if (viewType === 'grid') {
        body.classList.remove('table-view');
        body.classList.add('grid-view');
        
        // Asegurar que todas las tarjetas sean visibles
        document.querySelectorAll('.product-card').forEach(card => {
            if (!card.hasAttribute('data-matches-filter') || 
                card.getAttribute('data-matches-filter') === 'true') {
                card.removeAttribute('style');
            }
        });
    } else {
        body.classList.remove('grid-view');
        body.classList.add('table-view');
        
        // Asegurar que todas las filas sean visibles
        document.querySelectorAll('.products-table tbody tr').forEach(row => {
            if (!row.hasAttribute('data-matches-filter') || 
                row.getAttribute('data-matches-filter') === 'true') {
                row.removeAttribute('style');
            }
        });
    }

    // Guardar la vista actual en una variable global
    window.currentView = viewType;

    // Guardar la preferencia de vista en localStorage
    localStorage.setItem('preferredView', viewType);

    // Aplicar filtros activos si hay un término de búsqueda global
    if (window.currentSearchTerm) {
        reapplySearchFilter(viewType);
    }
}

// Función para reaplicar filtros después de cambiar de vista
function reapplySearchFilter(viewType) {
    console.log('Reaplicando filtro después de cambiar vista:', window.currentSearchTerm);

    if (viewType === 'grid') {
        // Mostrar solo las tarjetas que coinciden con el filtro
        const cards = document.querySelectorAll('.product-card');
        cards.forEach(card => {
            if (card.getAttribute('data-matches-filter') === 'true') {
                card.style.cssText = 'display: flex !important';
            } else {
                card.style.cssText = 'display: none !important';
            }
        });
    } else if (viewType === 'table') {
        // Mostrar solo las filas que coinciden con el filtro
        const tableRows = document.querySelectorAll('.products-table tbody tr');
        tableRows.forEach(row => {
            if (row.getAttribute('data-matches-filter') === 'true') {
                row.style.cssText = 'display: table-row !important';
            } else {
                row.style.cssText = 'display: none !important';
            }
        });

        // Forzar repintado de la tabla
        const tableContainer = document.querySelector('.products-table');
        if (tableContainer) {
            tableContainer.style.opacity = '0.99';
            setTimeout(() => {
                tableContainer.style.opacity = '1';
            }, 10);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando cambio de vistas...');

    // Obtener referencias a los elementos
    const viewButtons = document.querySelectorAll('.view-btn');
    const productsGrid = document.querySelector('.products-grid');
    const productsTable = document.querySelector('.products-table');

    console.log('Elementos de vista:', {
        buttons: viewButtons.length,
        grid: productsGrid ? 'Encontrado' : 'No encontrado',
        table: productsTable ? 'Encontrado' : 'No encontrado'
    });

    if (viewButtons.length > 0 && productsGrid && productsTable) {
        // Función para cambiar entre vistas (ahora usa la función global)
        function switchView(viewType) {
            console.log('Cambiando a vista:', viewType);

            // Usar la función global para establecer la vista activa
            setActiveView(viewType);

            // Ocultar mensaje de no resultados al cambiar de vista
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.style.display = 'none';
            }

            // Verificar si hay resultados visibles
            setTimeout(() => {
                checkVisibleResults(viewType);
            }, 100);
        }

        // Función para verificar si hay resultados visibles después de cambiar de vista
        function checkVisibleResults(viewType) {
            if (window.currentSearchTerm) {
                let visibleCount = 0;

                if (viewType === 'grid') {
                    const visibleCards = document.querySelectorAll('.product-card[data-matches-filter="true"]');
                    visibleCount = visibleCards.length;
                    console.log(`Tarjetas visibles después de cambio de vista: ${visibleCount}`);
                } else if (viewType === 'table') {
                    const visibleRows = document.querySelectorAll('.products-table tbody tr[data-matches-filter="true"]');
                    visibleCount = visibleRows.length;
                    console.log(`Filas visibles después de cambio de vista: ${visibleCount}`);
                }

                // Mostrar mensaje de no resultados si no hay coincidencias
                if (visibleCount === 0) {
                    showNoResultsMessage();
                }
            }
        }

        // Función para mostrar mensaje de no resultados
        function showNoResultsMessage() {
            console.log('Mostrando mensaje de no resultados desde view-switcher');
            let noResultsMsg = document.getElementById('no-results-message');

            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'no-results-message';
                noResultsMsg.className = 'no-results';
                noResultsMsg.innerHTML = `
                    <div style="text-align: center; padding: 20px; margin: 20px auto; max-width: 500px; background-color: #f8f9fa; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <i class="fas fa-search" style="font-size: 3rem; color: #95a5a6; margin-bottom: 15px;"></i>
                        <h3 style="color: #2c3e50; margin-bottom: 10px;">No se encontraron resultados</h3>
                        <p style="color: #7f8c8d;">No hay productos que coincidan con tu búsqueda. Intenta con otros términos o restablece los filtros.</p>
                    </div>
                `;

                const productsContainer = document.getElementById('products-container');
                if (productsContainer) {
                    productsContainer.insertAdjacentElement('afterend', noResultsMsg);
                } else {
                    document.body.appendChild(noResultsMsg);
                }
            } else {
                noResultsMsg.style.display = 'block';
            }
        }

        // Agregar event listeners a los botones de vista
        viewButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const viewType = this.dataset.view;
                switchView(viewType);
            });
        });

        // Establecer la vista inicial (usar la preferencia guardada o table por defecto)
        const savedView = localStorage.getItem('preferredView') || 'table';

        // Usar la función global para establecer la vista activa
        setActiveView(savedView);

        console.log('Vista inicial establecida:', window.currentView);

        console.log('Inicialización completa del cambio de vistas');
    }
});
