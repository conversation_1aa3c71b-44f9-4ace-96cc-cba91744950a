<?php
// Archivo: get_almacenes.php
// Descripción: Endpoint para obtener la lista de almacenes

// Incluir archivos necesarios
require_once 'db_connection.php';

// Inicializar respuesta
$response = ['status' => 'error', 'message' => ''];

try {
    // Obtener conexión a la base de datos
    $conn = getConnection();

    // Consultar almacenes
    $stmt = $conn->prepare("SELECT id, nombre FROM almacen WHERE activo = 1 ORDER BY nombre");
    $stmt->execute();
    $almacenes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Si no hay almacenes, crear uno por defecto (TEMUCO)
    if (empty($almacenes)) {
        $stmt = $conn->prepare("INSERT INTO almacen (nombre, activo) VALUES ('TEMUCO', 1)");
        if ($stmt->execute()) {
            $almacenes[] = [
                'id' => $conn->lastInsertId(),
                'nombre' => 'TEMUCO'
            ];
        }
    }

    $response = [
        'status' => 'success',
        'almacenes' => $almacenes
    ];

} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'message' => $e->getMessage()
    ];
}

// Devolver respuesta en formato JSON
header('Content-Type: application/json');
echo json_encode($response);
