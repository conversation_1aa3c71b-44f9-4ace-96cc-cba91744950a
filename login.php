<?php
// Incluir configuración de sesiones para prevenir cierre por inactividad
require_once 'session_config.php';
session_start();

// Verificar si el usuario ya está logueado
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: index.php');
    exit;
}

// Obtener mensaje de error si existe
$error = '';
if (isset($_SESSION['login_error'])) {
    $error = $_SESSION['login_error'];
    unset($_SESSION['login_error']); // Limpiar el mensaje de error
}

// Obtener el nombre de usuario si existe (para rellenar el campo)
$username_prefill = '';
if (isset($_SESSION['username_prefill'])) {
    $username_prefill = $_SESSION['username_prefill'];
    unset($_SESSION['username_prefill']); // Limpiar el valor
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #0f3460;
            --highlight-color: #e94560;
            --text-primary: #eaeaea;
            --text-secondary: #b4b4b4;
            --error-color: #ff4757;
            --success-color: #32ff7e;
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #0a0a0a;
            position: relative;
            overflow: hidden;
        }

        /* Fondo animado mejorado */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at 20% 50%, #1a1a2e 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, #16213e 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, #0f3460 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, #e94560 0%, transparent 30%);
            animation: gradientShift 20s ease-in-out infinite;
            z-index: -2;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(255, 255, 255, 0.03) 2px, rgba(255, 255, 255, 0.03) 4px),
                repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(255, 255, 255, 0.03) 2px, rgba(255, 255, 255, 0.03) 4px);
            z-index: -1;
        }

        @keyframes gradientShift {
            0%, 100% {
                transform: rotate(0deg) scale(1.5);
            }
            25% {
                transform: rotate(90deg) scale(1.2);
            }
            50% {
                transform: rotate(180deg) scale(1.5);
            }
            75% {
                transform: rotate(270deg) scale(1.2);
            }
        }

        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid var(--glass-border);
            padding: 3rem;
            border-radius: 24px;
            box-shadow: 
                0 25px 45px rgba(0, 0, 0, 0.3),
                0 0 100px rgba(233, 69, 96, 0.1),
                inset 0 0 30px rgba(255, 255, 255, 0.01);
            width: 100%;
            max-width: 440px;
            position: relative;
            z-index: 1;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #e94560, #0f3460, #16213e, #e94560);
            border-radius: 24px;
            background-size: 400% 400%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: borderGradient 10s ease infinite;
        }

        .login-container:hover::before {
            opacity: 1;
        }

        @keyframes borderGradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .logo {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, #e94560, #ff6b6b);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(233, 69, 96, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        .logo-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .logo-text {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
            letter-spacing: -0.5px;
            margin-top: 0.5rem;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 400;
            margin-top: 0.25rem;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px) rotateZ(0deg);
            }
            50% {
                transform: translateY(-10px) rotateZ(2deg);
            }
        }

        .form-group {
            margin-bottom: 1.75rem;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
            transition: color 0.3s ease;
        }

        .form-group.focused label {
            color: var(--highlight-color);
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            transition: color 0.3s ease;
            z-index: 3;
        }

        .form-group.focused .input-icon {
            color: var(--highlight-color);
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            font-size: 1rem;
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            z-index: 2;
            font-weight: 400;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--highlight-color);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: 
                0 0 0 3px rgba(233, 69, 96, 0.1),
                0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .form-group input::placeholder {
            color: var(--text-secondary);
            opacity: 0.6;
        }

        .btn {
            width: 100%;
            padding: 1.125rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 1px;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e94560, #ff6b6b);
            color: white;
            box-shadow: 
                0 8px 32px rgba(233, 69, 96, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 12px 40px rgba(233, 69, 96, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:active {
            transform: translateY(0px);
            box-shadow: 
                0 4px 20px rgba(233, 69, 96, 0.3),
                inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .links {
            margin-top: 2rem;
            text-align: center;
            font-size: 0.875rem;
            position: relative;
            z-index: 2;
        }

        .links a {
            color: var(--text-secondary);
            text-decoration: none;
            margin: 0 0.75rem;
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
        }

        .links a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--highlight-color);
            transition: width 0.3s ease;
        }

        .links a:hover {
            color: var(--text-primary);
        }

        .links a:hover::after {
            width: 100%;
        }

        .links span {
            color: var(--text-secondary);
            opacity: 0.5;
        }

        .divider {
            margin: 2rem 0;
            text-align: center;
            position: relative;
            color: var(--text-secondary);
            z-index: 2;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .divider::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 45%;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--glass-border));
        }

        .divider::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            width: 45%;
            height: 1px;
            background: linear-gradient(270deg, transparent, var(--glass-border));
        }


        .password-container {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-secondary);
            z-index: 3;
            transition: all 0.3s ease;
            padding: 0.25rem;
            border-radius: 4px;
        }

        .toggle-password:hover {
            color: var(--highlight-color);
            background: rgba(233, 69, 96, 0.1);
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            color: var(--text-secondary);
            position: relative;
            z-index: 2;
        }

        .remember-me input[type="checkbox"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid var(--glass-border);
            border-radius: 4px;
            margin-right: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background: rgba(255, 255, 255, 0.03);
        }

        .remember-me input[type="checkbox"]:checked {
            background: var(--highlight-color);
            border-color: var(--highlight-color);
        }

        .remember-me input[type="checkbox"]:checked::after {
            content: '\2713';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .remember-me label {
            font-size: 0.875rem;
            cursor: pointer;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .remember-me:hover label {
            color: var(--text-primary);
        }

        .login-container:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 
                0 35px 60px rgba(0, 0, 0, 0.4),
                0 0 120px rgba(233, 69, 96, 0.15),
                inset 0 0 30px rgba(255, 255, 255, 0.02);
        }

        /* Efectos para los mensajes de error y éxito */
        .message {
            padding: 1rem 1.25rem;
            margin-bottom: 1.5rem;
            border-radius: 12px;
            font-size: 0.875rem;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            animation: slideIn 0.3s ease-out;
            border: 1px solid;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-message {
            background: rgba(255, 71, 87, 0.1);
            border-color: rgba(255, 71, 87, 0.3);
            color: #ff6b7a;
        }

        .error-message i {
            color: var(--error-color);
        }

        .success-message {
            background: rgba(50, 255, 126, 0.1);
            border-color: rgba(50, 255, 126, 0.3);
            color: #5aff8e;
        }

        .success-message i {
            color: var(--success-color);
        }

        /* Loader para el botón */
        .btn-loading {
            position: relative;
            color: transparent;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spinner 0.8s linear infinite;
        }

        @keyframes spinner {
            to {
                transform: rotate(360deg);
            }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 1rem;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
            }

            .logo-icon i {
                font-size: 2rem;
            }

            .logo-text {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            
            <div class="logo-text">Tata Repuestos</div>
            <div class="logo-subtitle">Sistema de Gestión</div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="message error-message">
            <i class="fas fa-exclamation-circle"></i>
            <span><?php echo htmlspecialchars($error); ?></span>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['success_message'])): ?>
        <div class="message success-message">
            <i class="fas fa-check-circle"></i>
            <span><?php echo htmlspecialchars($_SESSION['success_message']); ?></span>
            <?php unset($_SESSION['success_message']); ?>
        </div>
        <?php endif; ?>

        <form action="validate_login.php" method="POST" id="loginForm">
            <div class="form-group" id="usernameGroup">
                <label for="username">Usuario</label>
                <div class="input-wrapper">
                    <i class="input-icon fas fa-user"></i>
                    <input type="text" id="username" name="username" placeholder="Ingresa tu usuario" value="<?php echo htmlspecialchars($username_prefill); ?>" required autofocus>
                </div>
            </div>
            <div class="form-group" id="passwordGroup">
                <label for="password">Contraseña</label>
                <div class="input-wrapper password-container">
                    <i class="input-icon fas fa-lock"></i>
                    <input type="password" id="password" name="password" placeholder="Ingresa tu contraseña" required>
                    <i class="toggle-password fas fa-eye" onclick="togglePasswordVisibility()"></i>
                </div>
            </div>
            <div class="remember-me">
                <input type="checkbox" id="remember" name="remember">
                <label for="remember">Recordarme</label>
            </div>
            <button type="submit" class="btn btn-primary" id="submitBtn">
                <span class="btn-text">Iniciar Sesión</span>
            </button>
        </form>
        <div class="divider">o</div>
        <div class="links">
            <a href="register.php">Crear cuenta</a>
            <span>|</span>
            <a href="forgot_password.php">¿Olvidaste tu contraseña?</a>
        </div>
    </div>

    <script>
    // Toggle password visibility
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('.toggle-password');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Add focus effects to form groups
    document.getElementById('username').addEventListener('focus', function() {
        document.getElementById('usernameGroup').classList.add('focused');
    });

    document.getElementById('username').addEventListener('blur', function() {
        document.getElementById('usernameGroup').classList.remove('focused');
    });

    document.getElementById('password').addEventListener('focus', function() {
        document.getElementById('passwordGroup').classList.add('focused');
    });

    document.getElementById('password').addEventListener('blur', function() {
        document.getElementById('passwordGroup').classList.remove('focused');
    });

    // Form submission with loading state
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.classList.add('btn-loading');
        submitBtn.disabled = true;
    });

    // Animate elements on load
    window.addEventListener('load', function() {
        const container = document.querySelector('.login-container');
        container.style.opacity = '0';
        container.style.transform = 'translateY(20px)';
        
        setTimeout(function() {
            container.style.transition = 'all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1)';
            container.style.opacity = '1';
            container.style.transform = 'translateY(0)';
        }, 100);
    });
    </script>
</body>
</html>
