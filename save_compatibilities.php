<?php
header('Content-Type: application/json');
require_once 'db_connection.php';

try {
    $conn = getConnection();
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['compatibilities']) || empty($data['compatibilities'])) {
        throw new Exception('No se seleccionaron compatibilidades');
    }

    $stmt = $conn->prepare("
        INSERT INTO repuesto_compatible (repuesto_id, vehiculo_id) 
        VALUES (:repuesto_id, :vehiculo_id)
        ON DUPLICATE KEY UPDATE repuesto_id = VALUES(repuesto_id)
    ");

    $conn->beginTransaction();

    foreach ($data['compatibilities'] as $compatibility) {
        $stmt->execute([
            ':repuesto_id' => $compatibility['repuesto_id'],
            ':vehiculo_id' => $compatibility['vehiculo_id']
        ]);
    }

    $conn->commit();

    echo json_encode([
        'status' => 'success',
        'message' => 'Compatibilidades guardadas correctamente'
    ]);

} catch(Exception $e) {
    if ($conn) {
        $conn->rollBack();
    }
    
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
