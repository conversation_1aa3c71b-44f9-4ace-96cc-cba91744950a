<?php
// Desactivar la salida de errores para evitar que se mezclen con la respuesta JSON
ini_set('display_errors', 0);
error_reporting(0);

// Incluir conexión a la base de datos
require_once 'db_connection.php';

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar si se proporcionaron los parámetros necesarios
if (!isset($_POST['dte_id']) || !isset($_POST['id_voucher'])) {
    echo json_encode(['success' => false, 'message' => 'Parámetros incompletos']);
    exit;
}

// Obtener y validar los parámetros
$dteId = intval($_POST['dte_id']);
$idVoucher = trim($_POST['id_voucher']);

// Validar que el ID del DTE sea válido
if ($dteId <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID de DTE inválido']);
    exit;
}

try {
    // Actualizar el campo id_voucher
    $stmt = $conn->prepare("UPDATE tb_facturas_dte SET id_voucher = ? WHERE id = ?");
    $result = $stmt->execute([$idVoucher, $dteId]);

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'ID Voucher actualizado correctamente']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error al actualizar el ID Voucher']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Error en la base de datos: ' . $e->getMessage()]);
}