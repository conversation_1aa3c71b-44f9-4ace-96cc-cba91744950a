<?php
// Script para verificar y corregir permisos de directorios

// Configuración básica
header('Content-Type: text/html; charset=UTF-8');
echo "<h1>Verificación de Permisos</h1>";
echo "<pre>";

// Directorios a verificar
$directories = [
    'Documents',
    'Documents/DTE',
    'Documents/DTE/Facturas',
    'Documents/DTE/Boletas',
    'Documents/sobreEnvio',
    'Documents/PDF_88',
    'Documents/folios',
    'Documents/folios/Facturas',
    'Documents/folios/Boletas'
];

function checkDirectory($dir) {
    echo "Verificando directorio: $dir\n";
    
    // Crear el directorio si no existe
    if (!file_exists($dir)) {
        echo "  - Directorio no existe, creando...\n";
        if (mkdir($dir, 0777, true)) {
            echo "  - Directorio creado correctamente\n";
        } else {
            echo "  - ERROR: No se pudo crear el directorio\n";
            return false;
        }
    }
    
    // Verificar permisos actuales
    $perms = fileperms($dir) & 0777;
    echo "  - Permisos actuales: " . decoct($perms) . "\n";
    
    // Verificar si es escribible
    if (is_writable($dir)) {
        echo "  - El directorio es escribible ✓\n";
    } else {
        echo "  - ADVERTENCIA: El directorio NO es escribible ✗\n";
        
        // Intentar cambiar permisos
        echo "  - Intentando cambiar permisos a 0777...\n";
        if (chmod($dir, 0777)) {
            echo "  - Permisos cambiados con éxito ✓\n";
        } else {
            echo "  - ERROR: No se pudieron cambiar los permisos ✗\n";
            
            // Información adicional
            echo "  - Usuario PHP: " . (function_exists('posix_getpwuid') ? posix_getpwuid(posix_geteuid())['name'] : 'N/A') . "\n";
            echo "  - Propietario del directorio: " . (function_exists('posix_getpwuid') ? posix_getpwuid(fileowner($dir))['name'] : 'N/A') . "\n";
        }
    }
    
    // Verificar permisos nuevamente
    $perms = fileperms($dir) & 0777;
    echo "  - Permisos finales: " . decoct($perms) . "\n";
    
    // Probar escritura
    $testFile = "$dir/test_write_" . time() . ".tmp";
    if (file_put_contents($testFile, "Test")) {
        echo "  - Prueba de escritura: EXITOSA ✓\n";
        unlink($testFile); // Eliminar archivo de prueba
    } else {
        echo "  - Prueba de escritura: FALLIDA ✗\n";
        $error = error_get_last();
        echo "  - Error: " . ($error['message'] ?? 'Desconocido') . "\n";
    }
    
    echo "\n";
    return true;
}

echo "Iniciando verificación de permisos...\n\n";

$allOk = true;
foreach ($directories as $dir) {
    $result = checkDirectory($dir);
    if (!$result) {
        $allOk = false;
    }
}

echo "\nResumen:\n";
if ($allOk) {
    echo "✅ Todos los directorios parecen estar configurados correctamente.\n";
} else {
    echo "⚠️ Se encontraron problemas con algunos directorios.\n";
    echo "Posibles soluciones desde consola (SSH):\n";
    echo "1. Ejecutar: chmod -R 777 Documents\n";
    echo "2. Cambiar propietario: chown -R www-data:www-data Documents\n";
}

echo "</pre>";
echo "<p><a href='index.php'>Volver al inicio</a></p>";
?>
