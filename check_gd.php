<?php
// Script para verificar la extensión GD y sus capacidades

// Mostrar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Verificación de la extensión GD</h1>";

// Verificar si GD está instalado
if (!extension_loaded('gd')) {
    echo "<p style='color:red'>La extensión GD NO está instalada.</p>";
    echo "<p>Por favor, instale la extensión GD para poder procesar imágenes.</p>";
    exit;
}

echo "<p style='color:green'>La extensión GD está instalada.</p>";

// Obtener información de GD
$gdInfo = gd_info();
echo "<h2>Información de GD</h2>";
echo "<pre>";
print_r($gdInfo);
echo "</pre>";

// Verificar soporte para diferentes formatos de imagen
echo "<h2>Soporte de formatos de imagen</h2>";
echo "<ul>";
echo "<li>JPEG: " . (isset($gdInfo['JPEG Support']) && $gdInfo['JPEG Support'] ? "<span style='color:green'>Soportado</span>" : "<span style='color:red'>No soportado</span>") . "</li>";
echo "<li>PNG: " . (isset($gdInfo['PNG Support']) && $gdInfo['PNG Support'] ? "<span style='color:green'>Soportado</span>" : "<span style='color:red'>No soportado</span>") . "</li>";
echo "<li>GIF: " . (isset($gdInfo['GIF Read Support']) && $gdInfo['GIF Read Support'] ? "<span style='color:green'>Lectura soportada</span>" : "<span style='color:red'>Lectura no soportada</span>") . "</li>";
echo "<li>GIF (escritura): " . (isset($gdInfo['GIF Create Support']) && $gdInfo['GIF Create Support'] ? "<span style='color:green'>Escritura soportada</span>" : "<span style='color:red'>Escritura no soportada</span>") . "</li>";
echo "<li>WebP: " . (isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'] ? "<span style='color:green'>Soportado</span>" : "<span style='color:red'>No soportado</span>") . "</li>";
echo "<li>BMP: " . (isset($gdInfo['BMP Support']) && $gdInfo['BMP Support'] ? "<span style='color:green'>Soportado</span>" : "<span style='color:red'>No soportado</span>") . "</li>";
echo "</ul>";

// Verificar funciones específicas
echo "<h2>Verificación de funciones específicas</h2>";
echo "<ul>";
$functions = [
    'imagecreatetruecolor',
    'imagecreatefromjpeg',
    'imagecreatefrompng',
    'imagecreatefromgif',
    'imagecreatefromwebp',
    'imagecreatefrombmp',
    'imagejpeg',
    'imagepng',
    'imagegif',
    'imagewebp',
    'imagebmp',
    'imagerotate',
    'imagefilter',
    'imagecrop',
    'imagescale',
    'imagepalettetotruecolor',
    'imagealphablending',
    'imagesavealpha',
    'imagecolorallocatealpha',
    'imagefilledrectangle',
    'imagecopyresampled',
    'imagedestroy'
];

foreach ($functions as $function) {
    echo "<li>$function: " . (function_exists($function) ? "<span style='color:green'>Disponible</span>" : "<span style='color:red'>No disponible</span>") . "</li>";
}
echo "</ul>";

// Prueba de creación de imagen
echo "<h2>Prueba de creación de imagen</h2>";
try {
    // Crear una imagen de prueba
    $image = imagecreatetruecolor(100, 100);
    if ($image === false) {
        throw new Exception("No se pudo crear la imagen");
    }
    
    // Dibujar algo en la imagen
    $red = imagecolorallocate($image, 255, 0, 0);
    imagefilledrectangle($image, 0, 0, 100, 100, $red);
    
    // Guardar la imagen en un archivo temporal
    $tempFile = tempnam(sys_get_temp_dir(), 'gd_test') . '.png';
    if (imagepng($image, $tempFile)) {
        echo "<p style='color:green'>Se creó y guardó una imagen de prueba correctamente.</p>";
        echo "<p>Archivo temporal: $tempFile</p>";
        
        // Mostrar la imagen
        $imageData = base64_encode(file_get_contents($tempFile));
        echo "<p>Vista previa de la imagen de prueba:</p>";
        echo "<img src='data:image/png;base64,$imageData' />";
        
        // Limpiar
        imagedestroy($image);
        unlink($tempFile);
    } else {
        throw new Exception("No se pudo guardar la imagen");
    }
} catch (Exception $e) {
    echo "<p style='color:red'>Error en la prueba de creación de imagen: " . $e->getMessage() . "</p>";
}

// Prueba de conversión a WebP
echo "<h2>Prueba de conversión a WebP</h2>";
if (function_exists('imagewebp')) {
    try {
        // Crear una imagen de prueba
        $image = imagecreatetruecolor(100, 100);
        if ($image === false) {
            throw new Exception("No se pudo crear la imagen");
        }
        
        // Dibujar algo en la imagen
        $blue = imagecolorallocate($image, 0, 0, 255);
        imagefilledrectangle($image, 0, 0, 100, 100, $blue);
        
        // Guardar la imagen en formato WebP
        $tempFile = tempnam(sys_get_temp_dir(), 'gd_test') . '.webp';
        if (imagewebp($image, $tempFile, 80)) {
            echo "<p style='color:green'>Se creó y guardó una imagen WebP de prueba correctamente.</p>";
            echo "<p>Archivo temporal: $tempFile</p>";
            
            // Mostrar la imagen
            $imageData = base64_encode(file_get_contents($tempFile));
            echo "<p>Vista previa de la imagen WebP de prueba:</p>";
            echo "<img src='data:image/webp;base64,$imageData' />";
            
            // Limpiar
            imagedestroy($image);
            unlink($tempFile);
        } else {
            throw new Exception("No se pudo guardar la imagen WebP");
        }
    } catch (Exception $e) {
        echo "<p style='color:red'>Error en la prueba de conversión a WebP: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color:red'>La función imagewebp no está disponible. No se puede realizar la prueba de conversión a WebP.</p>";
}

// Información del sistema
echo "<h2>Información del sistema</h2>";
echo "<ul>";
echo "<li>PHP Version: " . phpversion() . "</li>";
echo "<li>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li>OS: " . PHP_OS . "</li>";
echo "<li>SAPI: " . php_sapi_name() . "</li>";
echo "</ul>";

// Límites de PHP
echo "<h2>Límites de PHP</h2>";
echo "<ul>";
echo "<li>memory_limit: " . ini_get('memory_limit') . "</li>";
echo "<li>max_execution_time: " . ini_get('max_execution_time') . " segundos</li>";
echo "<li>upload_max_filesize: " . ini_get('upload_max_filesize') . "</li>";
echo "<li>post_max_size: " . ini_get('post_max_size') . "</li>";
echo "</ul>";
?>
