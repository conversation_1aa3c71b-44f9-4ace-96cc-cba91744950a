/* Estilos de controles de cantidad y botones */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.quantity-btn {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quantity-display {
    font-weight: 600;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: 0.5rem;
}

/* Estilo específico para el botón en la tabla */
.products-table .add-to-cart-btn {
    padding: 0.5rem;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    width: 100%;
}

.add-to-cart-btn:hover {
    background: var(--hover-color);
}

/* Controles de vista */
.view-controls {
    max-width: 1400px;
    margin: 1rem auto;
    padding: 0 1rem;
    display: flex;
    gap: 1rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

/* Estilos para controles de descuento */
.discount-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.discount-type {
    width: 60px;
}

.discount-value {
    flex-grow: 1;
    width: 80px;
}

.checkout-form {
    padding: 1rem;
    border-top: 1px solid #eee;
}

.checkout-input {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.checkout-btn {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
}

.checkout-btn:hover {
    background: var(--hover-color);
}

.modern-input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
}

.modern-select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
}

.client-section {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.client-section input {
    flex-grow: 2;
    height: 40px;
}

.client-section button {
    flex-shrink: 0;
    height: 40px;
    width: 50px;
}