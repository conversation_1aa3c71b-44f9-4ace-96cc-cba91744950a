<?php
// Archivo: enviar_dte.php
header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener el JSON enviado
$jsonData = $_POST['jsonData'] ?? '';

if (empty($jsonData)) {
    echo json_encode(['error' => 'Datos JSON no proporcionados']);
    exit;
}

// Obtener el tipo de documento desde el JSON
$jsonObj = json_decode($jsonData, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'error' => 'JSON inválido: ' . json_last_error_msg(),
        'json_preview' => substr($jsonData, 0, 200) . '...'
    ]);
    exit;
}

// Extraer el tipoDTE del JSON
$tipoDTE = null;
if (isset($jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'])) {
    $tipoDTE = $jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'];
} else {
    echo json_encode(['error' => 'No se pudo determinar el tipo de documento (TipoDTE) desde el JSON']);
    exit;
}

// Rutas a los archivos
$certificadoPath = 'Documents/17365958-K.pfx';

// Obtener la ruta del archivo de folios desde la base de datos
require_once 'db_connection.php';
try {
    $conn = getConnection();

    // Logging adicional para Notas de Crédito
    if ($tipoDTE == 61) {
        error_log("=== BUSCANDO FOLIOS PARA NOTA DE CRÉDITO (61) ===");

        // Verificar todos los registros de folios para este tipo de documento
        $stmtAll = $conn->prepare("SELECT id, ruta_archivo, activo, siguiente_folio, rango_final FROM folios_caf WHERE tipo_documento = ? ORDER BY activo DESC");
        $stmtAll->execute([$tipoDTE]);
        $allFolios = $stmtAll->fetchAll(PDO::FETCH_ASSOC);

        if (empty($allFolios)) {
            error_log("No se encontraron registros de folios para Notas de Crédito (61)");
        } else {
            error_log("Registros de folios encontrados para Notas de Crédito: " . json_encode($allFolios));
        }
    }

    $stmt = $conn->prepare("SELECT ruta_archivo FROM folios_caf WHERE tipo_documento = ? AND activo = 1");
    $stmt->execute([$tipoDTE]);
    $folio = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$folio || empty($folio['ruta_archivo'])) {
        // Mensaje de error específico para Notas de Crédito
        if ($tipoDTE == 61) {
            error_log("ERROR: No se encontró un archivo de folios activo para Notas de Crédito (61)");
            echo json_encode([
                'error' => 'No se encontró un archivo de folios activo para Notas de Crédito',
                'detalles' => 'Por favor, verifique que exista un registro en la tabla folios_caf con tipo_documento = 61 y activo = 1, o solicite nuevos folios CAF para Notas de Crédito'
            ]);
        } else {
            echo json_encode([
                'error' => 'No se encontró un archivo de folios activo para el tipo de documento ' . $tipoDTE,
                'detalles' => 'Por favor, verifique que exista un registro en la tabla folios_caf con tipo_documento = ' . $tipoDTE . ' y activo = 1'
            ]);
        }
        exit;
    }

    $foliosPath = $folio['ruta_archivo'];
    error_log("Ruta de folios encontrada para tipo_documento $tipoDTE: $foliosPath");
} catch (Exception $e) {
    error_log("ERROR al consultar la base de datos para obtener folios: " . $e->getMessage());
    echo json_encode([
        'error' => 'Error al consultar la base de datos para obtener la ruta del archivo de folios',
        'detalles' => $e->getMessage()
    ]);
    exit;
}

// Verificar que los archivos existen
if (!file_exists($certificadoPath)) {
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

if (!file_exists($foliosPath)) {
    echo json_encode(['error' => 'Archivo de folios no encontrado: ' . $foliosPath]);
    exit;
}

// Verificar tamaños de archivos
$certSize = filesize($certificadoPath);
$foliosSize = filesize($foliosPath);
if ($certSize === 0) {
    echo json_encode(['error' => 'El archivo de certificado está vacío']);
    exit;
}
if ($foliosSize === 0) {
    echo json_encode(['error' => 'El archivo de folios está vacío']);
    exit;
}

// Registrar información para diagnóstico
error_log("Procesando DTE tipo $tipoDTE con archivo de folios: $foliosPath");

// Configurar la solicitud cURL
$ch = curl_init('https://api.simpleapi.cl/api/v1/dte/generar');

// Preparar el formulario multipart
$boundary = uniqid();
$delimiter = '-------------' . $boundary;

// Construir el cuerpo de la solicitud
$postData = '';

// Agregar el campo JSON
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
$postData .= $jsonData . "\r\n";

// Agregar el archivo de certificado
$fileContents = file_get_contents($certificadoPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Agregar el archivo de folios
$fileContents = file_get_contents($foliosPath);
$postData .= "--" . $delimiter . "\r\n";
$postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($foliosPath) . '"' . "\r\n";
$postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$postData .= $fileContents . "\r\n";

// Cerrar el cuerpo del mensaje
$postData .= "--" . $delimiter . "--\r\n";

// Configuración de cURL
$apiKey = $_POST['apiKey'] ?? '2037-N680-6391-2493-5987'; // Token de autorización
curl_setopt_array($ch, [
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_HTTPHEADER => [
        "Authorization: $apiKey",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($postData)
    ]
]);

// Añadir antes de curl_exec para verificar la solicitud
$requestInfo = [
    'url' => 'https://api.simpleapi.cl/api/v1/dte/generar',
    'json_length' => strlen($jsonData),
    'certificado_size' => filesize($certificadoPath),
    'folios_size' => filesize($foliosPath),
    'boundary' => $boundary,
    'tipo_dte' => $tipoDTE
];

// Agregar logging adicional para Notas de Crédito
if ($tipoDTE == 61) {
    error_log("=== ENVIANDO SOLICITUD PARA NOTA DE CRÉDITO ===");
    error_log("JSON enviado para Nota de Crédito: " . $jsonData);

    // Verificar estructura del JSON
    $jsonObj = json_decode($jsonData, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        // Verificar sección de Referencias
        if (isset($jsonObj['Documento']['Referencias'])) {
            error_log("Referencias en JSON: " . json_encode($jsonObj['Documento']['Referencias']));
        } else {
            error_log("ERROR: No se encontró la sección 'Referencias' en el JSON");
        }
    } else {
        error_log("ERROR al decodificar JSON: " . json_last_error_msg());
    }
}

error_log("Enviando solicitud: " . json_encode($requestInfo));

// Ejecutar la solicitud
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
$curlInfo = curl_getinfo($ch);
curl_close($ch);

// Registrar información de la respuesta para diagnóstico
error_log("Respuesta API: Código=$httpCode, Tamaño=" . strlen($response));
error_log("Primeros 200 bytes: " . substr($response, 0, 200));

// Verificar si la respuesta parece ser XML
$isXml = strpos($response, '<?xml') !== false || strpos($response, '<DTE') !== false;
error_log("¿Parece XML? " . ($isXml ? 'Sí' : 'No'));

// Si hubo algún error con cURL
if ($curlError) {
    echo json_encode(['error' => 'Error al conectar con la API: ' . $curlError]);
    exit;
}

// Si la respuesta no fue exitosa
if ($httpCode !== 200) {
    // Intentar decodificar la respuesta para ver el mensaje de error detallado
    $errorDetails = json_decode($response, true);

    error_log("ERROR API SimpleAPI: Código HTTP $httpCode");
    error_log("Respuesta completa: " . $response);

    $errorMessage = 'Error al enviar el DTE. Código HTTP: ' . $httpCode;

    // Extraer mensaje de error más detallado si está disponible
    if ($errorDetails && isset($errorDetails['message'])) {
        $errorMessage .= ' - ' . $errorDetails['message'];
    } elseif ($errorDetails && isset($errorDetails['error'])) {
        $errorMessage .= ' - ' . $errorDetails['error'];
    }

    echo json_encode([
        'error' => $errorMessage,
        'response_details' => $errorDetails ?: $response,
        'request_info' => [
            'certificado_path' => $certificadoPath,
            'folios_path' => $foliosPath,
            'json_length' => strlen($jsonData)
        ]
    ]);
    exit;
}

// Si la respuesta fue exitosa, guardar el XML y registrar en la base de datos
require_once 'procesar_xml.php';

// Procesar la respuesta XML
$resultado = procesarRespuestaXML($response, $jsonData);

if ($resultado['success']) {
    // Extraer el tipo de documento del JSON enviado
    $jsonObj = json_decode($jsonData, true);
    $tipoDTE = null;

    if (isset($jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'])) {
        $tipoDTE = $jsonObj['Documento']['Encabezado']['IdentificacionDTE']['TipoDTE'];
    }

    // Obtener el siguiente folio disponible para este tipo de documento
    $siguientefolio = null;
    $rutaArchivo = null;

    if ($tipoDTE !== null) {
        require_once 'db_connection.php';
        $conn = getConnection();

        // Primero, verificar el estado actual del rango de folios
        $stmtCheck = $conn->prepare("
            SELECT siguiente_folio, rango_final
            FROM folios_caf
            WHERE tipo_documento = ? AND activo = true
            ORDER BY rango_inicial
            LIMIT 1
        ");
        $stmtCheck->execute([$tipoDTE]);
        $folioInfo = $stmtCheck->fetch(PDO::FETCH_ASSOC);

        // Llamar al stored procedure para obtener el siguiente folio
        error_log("Obteniendo siguiente folio para tipo DTE: " . $tipoDTE);

        // Primero, verificar manualmente si es el último folio
        $stmtManual = $conn->prepare("
            SELECT id, siguiente_folio, rango_final, activo
            FROM folios_caf
            WHERE tipo_documento = ? AND activo = true
            ORDER BY rango_inicial
            LIMIT 1
        ");
        $stmtManual->execute([$tipoDTE]);
        $folioManual = $stmtManual->fetch(PDO::FETCH_ASSOC);

        error_log("Verificación manual de folios: " . json_encode($folioManual));

        $esUltimoFolioManual = false;
        if ($folioManual && $folioManual['siguiente_folio'] >= $folioManual['rango_final']) {
            $esUltimoFolioManual = true;
            error_log("VERIFICACIÓN MANUAL: Este es el último folio disponible para tipo DTE: $tipoDTE");

            // Actualizar manualmente el campo activo a 0
            try {
                $stmtUpdate = $conn->prepare("
                    UPDATE folios_caf
                    SET activo = 0
                    WHERE id = ? AND tipo_documento = ?
                ");
                $stmtUpdate->execute([$folioManual['id'], $tipoDTE]);
                $rowsAffected = $stmtUpdate->rowCount();

                error_log("Actualización manual del campo activo: $rowsAffected filas afectadas");

                // Verificar si la actualización fue exitosa
                if ($rowsAffected > 0) {
                    error_log("ACTUALIZACIÓN MANUAL EXITOSA: Campo activo establecido a 0 para tipo DTE: $tipoDTE, ID: {$folioManual['id']}");
                } else {
                    error_log("ADVERTENCIA: No se pudo actualizar manualmente el campo activo para tipo DTE: $tipoDTE, ID: {$folioManual['id']}");
                }
            } catch (Exception $e) {
                error_log("ERROR al actualizar manualmente el campo activo: " . $e->getMessage());
            }

            // Forzar la solicitud de folios automáticamente
            $solicitarFoliosAutomaticamente = true;
        }

        // Llamar al stored procedure
        $stmt = $conn->prepare("CALL obtener_siguiente_folio(?)");
        $stmt->bindParam(1, $tipoDTE, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        error_log("Resultado de obtener_siguiente_folio: " . json_encode($result));

        if ($result && isset($result['folio'])) {
            $siguientefolio = $result['folio'];
            $rutaArchivo = $result['ruta_archivo'];
            $esUltimoFolio = isset($result['es_ultimo_folio']) ? (bool)$result['es_ultimo_folio'] : false;

            error_log("Folio obtenido: $siguientefolio, Es último folio: " . ($esUltimoFolio ? 'Sí' : 'No'));

            // Verificar si este es el último folio del rango
            $isLastFolio = $esUltimoFolio;
            $warningMessage = "";

            if ($isLastFolio || ($folioInfo && $folioInfo['siguiente_folio'] >= $folioInfo['rango_final'])) {
                $isLastFolio = true;
                $warningMessage = "ATENCIÓN: Este es el último folio disponible para el tipo de documento " . $tipoDTE .
                                ". Por favor, cargue un nuevo rango de folios.";

                // Log para el administrador
                error_log("WARNING: Último folio utilizado para tipo DTE: " . $tipoDTE);

                // Intentar solicitar folios automáticamente
                $solicitarFoliosAutomaticamente = true;
                error_log("Se ha configurado la solicitud automática de folios para tipo DTE: $tipoDTE");
            }
        } else {
            // Para todos los tipos de documentos, permitir continuar pero marcar que no hay folios disponibles
            error_log("ALERTA - Se ha alcanzado el límite de folios para el tipo de documento " . $tipoDTE . ", pero se continuará con la generación del PDF");
            $isLastFolio = true;
            $noFoliosDisponibles = true;

            // Mapeo de tipos de DTE a descripciones para mensajes más claros
            $dteDescriptions = [
                '33' => 'Factura Electrónica',
                '39' => 'Boleta Electrónica',
                '56' => 'Nota de Débito',
                '61' => 'Nota de Crédito'
            ];

            $dteDescription = isset($dteDescriptions[$tipoDTE]) ? $dteDescriptions[$tipoDTE] : "Documento tipo $tipoDTE";
            $warningMessage = "ATENCIÓN: Se ha alcanzado el límite del rango de folios para $dteDescription. Por favor, solicite un nuevo rango de folios.";

            // Intentar solicitar folios automáticamente
            $solicitarFoliosAutomaticamente = true;
        }

        $stmt->closeCursor();
    }

    // NUEVO: Almacenar información del receptor en la base de datos
    try {
        // Extraer información del receptor del JSON
        if (isset($jsonObj['Documento']['Encabezado']['Receptor'])) {
            $receptor = $jsonObj['Documento']['Encabezado']['Receptor'];

            // Verificar si el receptor ya existe (usando el RUT como identificador único)
            $stmt = $conn->prepare("SELECT id FROM tb_receptores WHERE rut = ?");
            $stmt->execute([$receptor['Rut']]);
            $receptorExistente = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($receptorExistente) {
                // Actualizar receptor existente
                $stmt = $conn->prepare(
                    "UPDATE tb_receptores
                     SET razon_social = ?, direccion = ?, comuna = ?,
                         giro = ?, contacto = ?
                     WHERE rut = ?"
                );
                $stmt->execute([
                    $receptor['RazonSocial'],
                    $receptor['Direccion'],
                    $receptor['Comuna'],
                    $receptor['Giro'] ?? null,
                    $receptor['Contacto'] ?? null,
                    $receptor['Rut']
                ]);
                $receptorId = $receptorExistente['id'];
                $receptorAccion = 'actualizado';
            } else {
                // Insertar nuevo receptor
                $stmt = $conn->prepare(
                    "INSERT INTO tb_receptores
                     (rut, razon_social, direccion, comuna, giro, contacto)
                     VALUES (?, ?, ?, ?, ?, ?)"
                );
                $stmt->execute([
                    $receptor['Rut'],
                    $receptor['RazonSocial'],
                    $receptor['Direccion'],
                    $receptor['Comuna'],
                    $receptor['Giro'] ?? null,
                    $receptor['Contacto'] ?? null
                ]);
                $receptorId = $conn->lastInsertId();
                $receptorAccion = 'registrado';
            }

            // Agregar información del receptor al resultado
            $resultado['receptor'] = [
                'id' => $receptorId,
                'rut' => $receptor['Rut'],
                'accion' => $receptorAccion
            ];
        }
    } catch (Exception $e) {
        error_log("Error al guardar información del receptor: " . $e->getMessage());
        // No detener el proceso si falla el guardado del receptor
        $resultado['receptor_error'] = $e->getMessage();
    }

    // NUEVO: Generar PDF a partir del XML
    error_log("Iniciando generación de PDF para el documento XML: " . $resultado['ruta']);
    try {
        // Asegurar que el directorio para PDFs exista
        $pdfDir = 'Documents/PDF_88/';
        if (!file_exists($pdfDir)) {
            if (!mkdir($pdfDir, 0755, true)) {
                error_log("ERROR: No se pudo crear el directorio para PDFs: " . $pdfDir);
            } else {
                error_log("Directorio para PDFs creado exitosamente: " . $pdfDir);
            }
        }

        // Asegurar que el directorio para Notas de Crédito exista
        if ($tipoDTE == 61) {
            error_log("=== PROCESANDO NOTA DE CRÉDITO (61) ===");

            // Verificar la estructura del JSON para Nota de Crédito
            if (isset($jsonObj['Documento']['Referencias'])) {
                error_log("Referencias encontradas en el JSON: " . json_encode($jsonObj['Documento']['Referencias']));
            } else {
                error_log("ADVERTENCIA: No se encontró la sección 'Referencias' en el JSON de la Nota de Crédito");
            }

            // Verificar la estructura del Emisor
            if (isset($jsonObj['Documento']['Encabezado']['Emisor'])) {
                error_log("Estructura del Emisor: " . json_encode($jsonObj['Documento']['Encabezado']['Emisor']));
            }

            // Verificar la estructura del Receptor
            if (isset($jsonObj['Documento']['Encabezado']['Receptor'])) {
                error_log("Estructura del Receptor: " . json_encode($jsonObj['Documento']['Encabezado']['Receptor']));
            }

            // Crear directorio para Notas de Crédito si no existe
            $notasCreditoDir = 'Documents/DTE/NotasCredito/';
            if (!file_exists($notasCreditoDir)) {
                if (!mkdir($notasCreditoDir, 0755, true)) {
                    error_log("ERROR: No se pudo crear el directorio para Notas de Crédito: " . $notasCreditoDir);
                } else {
                    error_log("Directorio para Notas de Crédito creado exitosamente: " . $notasCreditoDir);
                }
            } else {
                error_log("Directorio para Notas de Crédito ya existe: " . $notasCreditoDir);
            }
        }

        $pdfResult = generarPDFDesdeXML($resultado['ruta']);
        error_log("Resultado de generación de PDF: " . json_encode($pdfResult));
    } catch (Exception $e) {
        error_log("ERROR GRAVE al generar PDF: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        $pdfResult = [
            'success' => false,
            'error' => 'Error inesperado al generar el PDF: ' . $e->getMessage()
        ];
    }

    // Solicitar folios automáticamente si es necesario
    $foliosSolicitados = false;
    $foliosSolicitadosInfo = null;

    // Configuración para la solicitud de folios
    $forzarSolicitudFolios = false; // Cambiar a false en producción
    error_log("Configuración de solicitud de folios: forzar = $forzarSolicitudFolios");

    // Verificar si debemos solicitar folios automáticamente
    if (!isset($solicitarFoliosAutomaticamente)) {
        $solicitarFoliosAutomaticamente = false;
        error_log("Variable solicitarFoliosAutomaticamente no estaba definida, establecida a: false");
    }

    // Forzar la solicitud de folios si es el último folio disponible
    if (isset($isLastFolio) && $isLastFolio) {
        $solicitarFoliosAutomaticamente = true;
        error_log("Forzando solicitud automática de folios porque es el último folio disponible");
    }

    // Usar la variable forzada para pruebas
    if ($forzarSolicitudFolios) {
        $solicitarFoliosAutomaticamente = true;
        error_log("Forzando solicitud automática de folios para pruebas");
    }

    error_log("Estado de solicitud automática de folios: " . ($solicitarFoliosAutomaticamente ? 'ACTIVADA' : 'DESACTIVADA'));

    if ($solicitarFoliosAutomaticamente) {
        error_log("\n\n=== INICIANDO SOLICITUD AUTOMÁTICA DE FOLIOS ===\nTipo DTE: $tipoDTE\n");
        try {
            // Solicitar folios
            error_log("Llamando a la función solicitarFoliosCaf para tipo DTE: $tipoDTE");

            // Llamar directamente a la función para evitar problemas de alcance
            $foliosSolicitadosInfo = solicitarFoliosCaf($tipoDTE);
            $foliosSolicitados = $foliosSolicitadosInfo['success'] ?? false;

            error_log("Resultado de solicitarFoliosCaf: " . json_encode($foliosSolicitadosInfo));

            if ($foliosSolicitados) {
                error_log("SOLICITUD EXITOSA - Folios obtenidos: " .
                    $foliosSolicitadosInfo['rango_inicial'] . " - " .
                    $foliosSolicitadosInfo['rango_final'] . " (" .
                    $foliosSolicitadosInfo['total_folios'] . " folios)");

                // Mostrar mensaje de éxito en la respuesta
                $mensajeFolios = "Se han solicitado nuevos folios automáticamente. " .
                    "Rango: {$foliosSolicitadosInfo['rango_inicial']} - {$foliosSolicitadosInfo['rango_final']}";
            } else {
                error_log("SOLICITUD FALLIDA - Error: " . ($foliosSolicitadosInfo['error'] ?? 'Error desconocido'));
                if (isset($foliosSolicitadosInfo['response'])) {
                    error_log("Detalles de la respuesta: " . json_encode($foliosSolicitadosInfo['response']));
                }

                // Mostrar mensaje de error en la respuesta
                $mensajeFolios = "No se pudieron solicitar nuevos folios automáticamente. " .
                    "Error: " . ($foliosSolicitadosInfo['error'] ?? 'Error desconocido');
            }
        } catch (Exception $e) {
            error_log("\n\nEXCEPCIÓN EN SOLICITUD AUTOMÁTICA DE FOLIOS: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $foliosSolicitados = false;
            $foliosSolicitadosInfo = [
                'success' => false,
                'error' => 'Error inesperado: ' . $e->getMessage()
            ];

            // Mostrar mensaje de error en la respuesta
            $mensajeFolios = "Error al solicitar folios: " . $e->getMessage();
        }
    } else {
        error_log("No se solicitaron folios automáticamente porque la variable solicitarFoliosAutomaticamente es false");
    }

    // Preparar la respuesta incluyendo información sobre el último folio si corresponde
    $response = [
        'success' => true,
        'mensaje' => isset($noFoliosDisponibles) && $noFoliosDisponibles ? 'DTE procesado pero se ha alcanzado el límite de folios' : 'DTE procesado y guardado correctamente',
        'archivo' => $resultado['archivo'],
        'ruta' => $resultado['ruta'],
        'siguiente_folio' => $siguientefolio,
        'ruta_caf' => $rutaArchivo,
        'receptor' => $resultado['receptor'] ?? null,
        'receptor_error' => $resultado['receptor_error'] ?? null,
        'no_folios_disponibles' => isset($noFoliosDisponibles) && $noFoliosDisponibles,
        'dte_id' => $resultado['dte_id'] ?? null // Incluir el ID del DTE en la respuesta
    ];

    // Incluir información sobre la solicitud de folios si corresponde
    if (isset($foliosSolicitados) && $foliosSolicitados) {
        $response['folios_solicitados'] = true;
        $response['folios_info'] = $foliosSolicitadosInfo;
        $response['mensaje_folios'] = "Se han solicitado nuevos folios automáticamente. " .
            "Rango: {$foliosSolicitadosInfo['rango_inicial']} - {$foliosSolicitadosInfo['rango_final']}";
    } else {
        $response['folios_solicitados'] = false;
        if (isset($mensajeFolios)) {
            $response['mensaje_folios'] = $mensajeFolios;
        }
    }

    // Incluir información del PDF generado
    if (isset($pdfResult)) {
        if ($pdfResult['success']) {
            $response['pdf'] = $pdfResult;
            $response['pdf_generado'] = true;
        } else {
            // Si hubo un error en la generación del PDF, incluirlo como advertencia pero no afectar el éxito general
            $response['pdf_generado'] = false;
            $response['pdf_error'] = $pdfResult['error'] ?? 'Error desconocido al generar el PDF';
            $response['pdf_detalles'] = $pdfResult['detalles'] ?? null;
            error_log("ADVERTENCIA: El DTE se procesó correctamente pero hubo un error al generar el PDF: " . ($pdfResult['error'] ?? 'Error desconocido'));
        }
    } else {
        $response['pdf_generado'] = false;
        $response['pdf_error'] = 'No se intentó generar el PDF';
        error_log("ADVERTENCIA: No se intentó generar el PDF para el DTE");
    }

    // Agregar información sobre el último folio si corresponde
    if (isset($isLastFolio) && $isLastFolio) {
        $response['lastFolio'] = true;
        $response['warning'] = $warningMessage;
    }

    echo json_encode($response);
} else {
    // Verificar si es un error de duplicación
    $esDuplicado = strpos($resultado['mensaje'], 'Ya existe un registro con el mismo tipo de documento y folio') !== false;

    if ($esDuplicado) {
        error_log("ERROR DE DUPLICACIÓN: " . $resultado['mensaje'] . " - Detalles: " . ($resultado['detalles'] ?? 'No hay detalles'));

        echo json_encode([
            'error' => 'Documento duplicado',
            'mensaje' => 'Ya existe un documento con el mismo tipo y folio en la base de datos.',
            'detalles' => $resultado['detalles'] ?? null,
            'tipo_error' => 'duplicado',
            'respuesta_api_es_xml' => $isXml
        ]);
    } else {
        echo json_encode([
            'error' => $resultado['mensaje'],
            'detalles' => $resultado['detalles'] ?? null,
            'respuesta_api_tamano' => strlen($response),
            'respuesta_api_inicio' => substr($response, 0, 100),
            'respuesta_api_es_xml' => $isXml
        ]);
    }
}

/**
 * Función para generar PDF a partir del archivo XML del DTE
 *
 * @param string $xmlPath Ruta al archivo XML
 * @return array Información sobre el PDF generado o error
 */
function generarPDFDesdeXML($xmlPath) {
    error_log("=== Iniciando generación de PDF ===");
    error_log("Ruta XML: " . $xmlPath);

    if (!file_exists($xmlPath)) {
        error_log("ERROR: Archivo XML no encontrado en: " . $xmlPath);
        return [
            'success' => false,
            'error' => 'Archivo XML no encontrado: ' . $xmlPath
        ];
    }

    // Extraer el tipo de documento del XML para log adicional
    try {
        $xmlContent = file_get_contents($xmlPath);
        $xml = new SimpleXMLElement($xmlContent);
        $tipoDTE = '';

        if (isset($xml->Documento->Encabezado->IdDoc->TipoDTE)) {
            $tipoDTE = (string)$xml->Documento->Encabezado->IdDoc->TipoDTE;
            error_log("Tipo de documento detectado en XML: " . $tipoDTE);

            // Log adicional para notas de crédito
            if ($tipoDTE == '61') {
                error_log("Procesando generación de PDF para Nota de Crédito");
            }
        }
    } catch (Exception $e) {
        error_log("Advertencia: No se pudo extraer el tipo de documento del XML: " . $e->getMessage());
        // Continuar con el proceso a pesar del error
    }

    // Preparar los datos para la API
    $apiKey = '2037-N680-6391-2493-5987';
    $apiUrl = 'https://api.simpleapi.cl/api/v1/impresion/pdf/80mm';

    // Datos dinámicos para el input
    $fechaActual = date('Y-m-d');
    $horaActual = date('H:i');

    $jsonInput = json_encode([
        'NumeroResolucion' => 0,
        'UnidadSII' => 'TEMUCO',
        'FechaResolucion' => $fechaActual,
        'Ejecutivo' => 'Matias Guajardo',
        'Hora' => $horaActual
    ]);
    error_log("JSON Input preparado: " . $jsonInput);

    // Directorio PDF
    $pdfDir = 'Documents/PDF_88/';

    // Preparar nombres de archivos
    $xmlFilename = basename($xmlPath);
    $pdfFilename = str_replace('.xml', '.pdf', $xmlFilename);
    $pdfPath = $pdfDir . $pdfFilename;

    // Crear URL relativa para acceso web
    $pdfWebPath = '/' . $pdfDir . $pdfFilename; // Asegúrate de que esta ruta sea accesible desde el navegador

    error_log("Verificando directorio PDF: " . $pdfDir);

    // Asegurar que el directorio exista y tenga permisos adecuados
    if (!file_exists($pdfDir)) {
        error_log("Intentando crear directorio PDF: " . $pdfDir);
        if (!mkdir($pdfDir, 0755, true)) {
            error_log("ERROR: No se pudo crear el directorio PDF: " . $pdfDir);
            error_log("Permisos del directorio padre: " . decoct(fileperms(dirname($pdfDir))));

            // Intentar crear directorios intermedios si es necesario
            $parentDir = dirname($pdfDir);
            if (!file_exists($parentDir)) {
                error_log("Intentando crear directorio padre: " . $parentDir);
                if (mkdir($parentDir, 0755, true)) {
                    error_log("Directorio padre creado, intentando crear directorio PDF nuevamente");
                    if (mkdir($pdfDir, 0755, true)) {
                        error_log("Directorio PDF creado exitosamente en segundo intento");
                    } else {
                        return [
                            'success' => false,
                            'error' => 'No se pudo crear el directorio para PDFs: ' . $pdfDir,
                            'detalles' => 'Verifique los permisos del directorio padre: ' . dirname($pdfDir)
                        ];
                    }
                } else {
                    return [
                        'success' => false,
                        'error' => 'No se pudo crear el directorio padre: ' . $parentDir,
                        'detalles' => 'Verifique los permisos del sistema'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'error' => 'No se pudo crear el directorio para PDFs: ' . $pdfDir,
                    'detalles' => 'Verifique los permisos del directorio padre: ' . dirname($pdfDir)
                ];
            }
        }
        error_log("Directorio PDF creado exitosamente: " . $pdfDir);
    } else {
        error_log("Directorio PDF ya existe: " . $pdfDir);
        // Verificar permisos de escritura
        if (!is_writable($pdfDir)) {
            error_log("ERROR: El directorio PDF no tiene permisos de escritura: " . $pdfDir);
            error_log("Permisos actuales: " . decoct(fileperms($pdfDir)));

            // Intentar corregir permisos
            error_log("Intentando corregir permisos del directorio PDF");
            if (chmod($pdfDir, 0755)) {
                error_log("Permisos corregidos exitosamente");
            } else {
                return [
                    'success' => false,
                    'error' => 'El directorio PDF no tiene permisos de escritura: ' . $pdfDir,
                    'detalles' => 'Permisos actuales: ' . decoct(fileperms($pdfDir))
                ];
            }
        }
        error_log("Directorio PDF tiene permisos de escritura: " . $pdfDir);
    }

    error_log("Nombre archivo PDF a generar: " . $pdfFilename);

    // Preparar cURL
    $ch = curl_init($apiUrl);
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;

    // Construir el cuerpo multipart
    error_log("Preparando datos para envío a API");
    try {
        $fileContents = file_get_contents($xmlPath);
        error_log("XML leído correctamente, tamaño: " . strlen($fileContents) . " bytes");
        error_log("Primeros 200 caracteres del XML: " . substr($fileContents, 0, 200));
    } catch (Exception $e) {
        error_log("ERROR al leer archivo XML: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Error al leer el archivo XML: ' . $e->getMessage()
        ];
    }

    $postData = '';
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonInput . "\r\n";
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="fileEnvio"; filename="' . basename($xmlPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/xml' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";
    $postData .= "--" . $delimiter . "--\r\n";

    error_log("Tamaño total de datos a enviar: " . strlen($postData) . " bytes");
    error_log("Headers de la solicitud:");
    $headers = [
        "Authorization: $apiKey",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($postData)
    ];
    foreach ($headers as $header) {
        error_log($header);
    }

    // Configurar cURL con opciones adicionales para debugging
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_VERBOSE => true,
        CURLOPT_HEADER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);

    // Capturar información detallada de cURL
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);

    error_log("Enviando solicitud a API de PDF...");
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $curlInfo = curl_getinfo($ch);

    // Obtener información detallada de debug
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    error_log("Debug cURL: " . $verboseLog);

    error_log("Respuesta API PDF - Código HTTP: " . $httpCode);
    error_log("Información cURL completa: " . json_encode($curlInfo, JSON_PRETTY_PRINT));

    if ($curlError) {
        error_log("ERROR cURL: " . $curlError);
        curl_close($ch);
        return [
            'success' => false,
            'error' => 'Error en la solicitud cURL: ' . $curlError,
            'curl_info' => $curlInfo,
            'debug_log' => $verboseLog
        ];
    }

    // Separar headers y body de la respuesta
    $headerSize = $curlInfo['header_size'];
    $responseHeaders = substr($response, 0, $headerSize);
    $responseBody = substr($response, $headerSize);

    error_log("Headers de respuesta: " . $responseHeaders);
    error_log("Tamaño del body de respuesta: " . strlen($responseBody));

    curl_close($ch);

    if ($httpCode !== 200) {
        error_log("ERROR: Respuesta no exitosa de la API");
        error_log("Headers de respuesta completos: " . $responseHeaders);
        error_log("Body de respuesta (primeros 500 bytes): " . substr($responseBody, 0, 500));
        return [
            'success' => false,
            'error' => 'Error al generar el PDF. Código HTTP: ' . $httpCode,
            'response_headers' => $responseHeaders,
            'response_preview' => substr($responseBody, 0, 500)
        ];
    }

    // Verificar si la respuesta parece un PDF válido
    $isPDF = strpos($responseBody, '%PDF-') === 0;
    error_log("¿La respuesta parece un PDF válido? " . ($isPDF ? 'Sí' : 'No'));
    if (!$isPDF) {
        error_log("ERROR: La respuesta no parece ser un PDF válido");
        error_log("Primeros 100 bytes de la respuesta: " . bin2hex(substr($responseBody, 0, 100)));
        return [
            'success' => false,
            'error' => 'La respuesta no es un PDF válido',
            'response_preview' => bin2hex(substr($responseBody, 0, 100))
        ];
    }

    // Intentar guardar el PDF
    error_log("Intentando guardar PDF en: " . $pdfPath);
    if (file_put_contents($pdfPath, $responseBody) === false) {
        error_log("ERROR: No se pudo guardar el archivo PDF");
        error_log("Permisos del directorio: " . decoct(fileperms($pdfDir)));
        error_log("Espacio libre en disco: " . disk_free_space($pdfDir));
        return [
            'success' => false,
            'error' => 'Error al guardar el archivo PDF',
            'dir_perms' => decoct(fileperms($pdfDir)),
            'disk_space' => disk_free_space($pdfDir)
        ];
    }

    // Verificar el archivo guardado
    if (file_exists($pdfPath)) {
        $pdfSize = filesize($pdfPath);
        error_log("PDF guardado exitosamente. Tamaño: " . $pdfSize . " bytes");

        // Verificar que el archivo sea legible
        if ($pdfSize > 0) {
            $pdfCheck = file_get_contents($pdfPath, false, null, 0, 4);
            error_log("Primeros 4 bytes del PDF guardado: " . bin2hex($pdfCheck));
        }
    } else {
        error_log("ERROR: El archivo PDF no existe después de intentar guardarlo");
    }

    // Actualizar la base de datos
    try {
        require_once 'db_connection.php';
        $conn = getConnection();

        // Extraer el tipo de documento y folio del XML para log adicional
        $tipoDTE = '';
        $folio = '';
        try {
            $xmlContent = file_get_contents($xmlPath);
            $xml = new SimpleXMLElement($xmlContent);

            if (isset($xml->Documento->Encabezado->IdDoc->TipoDTE)) {
                $tipoDTE = (string)$xml->Documento->Encabezado->IdDoc->TipoDTE;
            }

            if (isset($xml->Documento->Encabezado->IdDoc->Folio)) {
                $folio = (string)$xml->Documento->Encabezado->IdDoc->Folio;
            }

            error_log("Actualizando registro en base de datos para tipo_dte=$tipoDTE, folio=$folio");
        } catch (Exception $e) {
            error_log("Advertencia: No se pudo extraer tipo_dte/folio del XML: " . $e->getMessage());
        }

        // Primero verificar si existe el registro
        $checkSql = "SELECT id FROM tb_facturas_dte WHERE nombre_archivo = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute([$xmlPath]);
        $existingRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRecord) {
            // Actualizar registro existente
            $sql = "UPDATE tb_facturas_dte
                    SET archivo_pdf = ?
                    WHERE nombre_archivo = ?";

            $stmt = $conn->prepare($sql);
            $stmt->execute([$pdfPath, $xmlPath]); // Ahora usamos rutas completas
            error_log("Base de datos actualizada correctamente");
            error_log("Archivo PDF: " . $pdfPath);
            error_log("Archivo XML relacionado: " . $xmlPath);
        } else {
            // Si no existe el registro pero tenemos tipo_dte y folio, intentar actualizar por esos campos
            if ($tipoDTE && $folio) {
                $sql = "UPDATE tb_facturas_dte
                        SET archivo_pdf = ?
                        WHERE tipo_dte = ? AND folio = ?";

                $stmt = $conn->prepare($sql);
                $stmt->execute([$pdfPath, $tipoDTE, $folio]);

                if ($stmt->rowCount() > 0) {
                    error_log("Base de datos actualizada por tipo_dte y folio");
                } else {
                    error_log("No se encontró registro para actualizar con tipo_dte=$tipoDTE, folio=$folio");
                }
            } else {
                error_log("No se pudo actualizar la base de datos: No se encontró el registro y no se tienen tipo_dte/folio");
            }
        }
    } catch (Exception $e) {
        error_log("ERROR al actualizar la base de datos: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
    }

    error_log("=== Proceso de generación de PDF completado ===");
    return [
        'success' => true,
        'archivo_pdf' => $pdfFilename,
        'ruta_pdf' => $pdfWebPath,  // Esta es la ruta que usará el JavaScript
        'tamaño_pdf' => filesize($pdfPath)
    ];
}

/**
 * Función para solicitar folios CAF automáticamente
 *
 * @param int $tipoDTE Tipo de documento para el que se solicitan folios
 * @param int $cantidad Cantidad de folios a solicitar (por defecto 19)
 * @return array Información sobre el resultado de la solicitud
 */
function solicitarFoliosCaf($tipoDTE, $cantidad = null) {
    // Determinar la cantidad de folios según el tipo de documento
    if ($cantidad === null) {
        switch ($tipoDTE) {
            case '33': // Facturas
                $cantidad = 19;
                break;
            case '39': // Boletas
                $cantidad = 500;
                break;
            case '61': // Notas de Crédito
                $cantidad = 1;
                break;
            case '56': // Notas de Débito
                $cantidad = 1;
                break;
            default:
                $cantidad = 19;
        }
    }

    error_log("=== INICIANDO SOLICITUD AUTOMÁTICA DE FOLIOS CAF ===\nTipo DTE: $tipoDTE\nCantidad: $cantidad");

    // Verificar si el tipo de documento es válido
    $tiposValidos = ['33', '39', '56', '61'];
    if (!in_array($tipoDTE, $tiposValidos)) {
        error_log("ERROR: Tipo de documento no válido: $tipoDTE");
        return [
            'success' => false,
            'error' => "Tipo de documento no válido: $tipoDTE",
            'tipos_validos' => $tiposValidos
        ];
    }

    // Mapeo de tipos de DTE a nombres de carpetas
    $folderNames = [
        '33' => 'Facturas',
        '39' => 'Boletas',
        '56' => 'NotasDebito',
        '61' => 'NotasCredito'
    ];

    $folderName = $folderNames[$tipoDTE];
    error_log("Carpeta destino seleccionada: $folderName");

    // Crear un nombre de archivo único para los folios
    $timestamp = date('Ymd_His');
    $fileName = "folios_{$tipoDTE}_{$timestamp}.pfx";
    error_log("Nombre de archivo generado: $fileName");

    // Preparar los datos para la solicitud
    error_log("Preparando datos para la solicitud...");
    $requestData = [
        'rutCertificado' => "17365958-K",
        'password' => "1569",
        'rutEmpresa' => "78078979-4",
        'ambiente' => 1,
        'tipoDTE' => $tipoDTE,
        'cantidad' => $cantidad,
        'certificadoPath' => "Documents/17365958-K.pfx",
        'folderDestino' => "Documents/folios/$folderName",
        'fileName' => $fileName
    ];

    // Realizar la solicitud a la API
    try {
        error_log("Realizando solicitud a la API de SimpleAPI...");

        // Preparar la URL de la API
        $apiUrl = 'https://api.simpleapi.cl/api/v1/folios/get';
        $apiKey = '2037-N680-6391-2493-5987';

        // Configurar cURL
        $ch = curl_init($apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($requestData),
            CURLOPT_HTTPHEADER => [
                "Authorization: $apiKey",
                "Content-Type: application/json"
            ],
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 30
        ]);

        // Capturar información detallada de cURL
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        curl_setopt($ch, CURLOPT_VERBOSE, true);

        // Ejecutar la solicitud
        error_log("Ejecutando solicitud cURL a: $apiUrl");
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlInfo = curl_getinfo($ch);

        // Obtener información detallada de debug
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        fclose($verbose);
        error_log("Debug cURL: " . $verboseLog);
        error_log("Información cURL: " . json_encode($curlInfo));

        curl_close($ch);

        // Verificar errores de cURL
        if ($curlError) {
            error_log("ERROR cURL en solicitud de folios: $curlError");
            return [
                'success' => false,
                'error' => "Error de conexión: $curlError",
                'curl_info' => $curlInfo,
                'debug_log' => $verboseLog
            ];
        }

        // Verificar código de respuesta HTTP
        if ($httpCode !== 200) {
            error_log("ERROR HTTP en solicitud de folios: $httpCode");
            error_log("Respuesta: $response");

            // Intentar decodificar la respuesta para obtener más información
            $responseData = null;
            try {
                $responseData = json_decode($response, true);
                error_log("Respuesta decodificada: " . json_encode($responseData));
            } catch (Exception $e) {
                error_log("No se pudo decodificar la respuesta: " . $e->getMessage());
            }

            return [
                'success' => false,
                'error' => "Error HTTP $httpCode",
                'response' => $response,
                'response_data' => $responseData,
                'curl_info' => $curlInfo
            ];
        }

        // Decodificar la respuesta JSON
        error_log("Decodificando respuesta JSON...");
        $responseData = json_decode($response, true);
        error_log("Respuesta decodificada: " . json_encode($responseData));

        if (!$responseData) {
            error_log("ERROR: No se pudo decodificar la respuesta JSON");
            error_log("Respuesta completa: $response");
            return [
                'success' => false,
                'error' => 'No se pudo decodificar la respuesta JSON',
                'response' => $response
            ];
        }

        // Verificar si la respuesta contiene un error
        if (isset($responseData['error'])) {
            error_log("ERROR en respuesta de API: " . $responseData['error']);
            return [
                'success' => false,
                'error' => $responseData['error'],
                'response' => $responseData
            ];
        }

        // Extraer información de los folios
        $rangoInicial = null;
        $rangoFinal = null;
        $rutaCompleta = null;

        if (isset($responseData['folios'])) {
            $folios = $responseData['folios'];
            $rangoInicial = $folios['desde'] ?? null;
            $rangoFinal = $folios['hasta'] ?? null;
            $rutaCompleta = $responseData['filePath'] ?? null;
        }

        if (!$rangoInicial || !$rangoFinal) {
            error_log("ERROR: No se pudieron extraer los rangos de folios de la respuesta");
            return [
                'success' => false,
                'error' => 'No se pudieron extraer los rangos de folios',
                'response' => $responseData
            ];
        }

        // Registrar en la base de datos
        try {
            require_once 'db_connection.php';
            $conn = getConnection();

            $stmt = $conn->prepare("
                INSERT INTO folios_caf (
                    tipo_documento,
                    rango_inicial,
                    rango_final,
                    siguiente_folio,
                    ruta_archivo,
                    activo,
                    created_at
                ) VALUES (
                    :tipo_documento,
                    :rango_inicial,
                    :rango_final,
                    :siguiente_folio,
                    :ruta_archivo,
                    1,
                    NOW()
                )
            ");

            $stmt->bindParam(':tipo_documento', $tipoDTE);
            $stmt->bindParam(':rango_inicial', $rangoInicial);
            $stmt->bindParam(':rango_final', $rangoFinal);
            $stmt->bindParam(':siguiente_folio', $rangoInicial); // siguiente_folio = rango_inicial
            $stmt->bindParam(':ruta_archivo', $rutaCompleta);

            $stmt->execute();
            $folioId = $conn->lastInsertId();

            error_log("Folios registrados exitosamente en la base de datos. ID: $folioId");

            return [
                'success' => true,
                'folio_id' => $folioId,
                'rango_inicial' => $rangoInicial,
                'rango_final' => $rangoFinal,
                'total_folios' => ($rangoFinal - $rangoInicial + 1),
                'ruta_archivo' => $rutaCompleta,
                'archivo_xml' => $fileName
            ];

        } catch (Exception $e) {
            error_log("ERROR al registrar folios en la base de datos: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error al registrar folios en la base de datos: ' . $e->getMessage(),
                'folios' => [
                    'rango_inicial' => $rangoInicial,
                    'rango_final' => $rangoFinal,
                    'ruta_archivo' => $rutaCompleta
                ]
            ];
        }

    } catch (Exception $e) {
        error_log("EXCEPCIÓN en solicitud de folios: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Error inesperado: ' . $e->getMessage()
        ];
    }
}
?>
