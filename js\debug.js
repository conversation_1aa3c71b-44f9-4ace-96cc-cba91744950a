/**
 * Archivo de depuración para diagnosticar problemas con los event listeners de los botones
 */

// Ejecutar cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== Debug script cargado ===');
    
    // Verificar todos los botones relevantes
    const buttonsToCheck = [
        'generateAndSendBtn',
        'enviarDTEBtn',
        'copyJsonBtn',
        'closeDTEBtn'
    ];
    
    buttonsToCheck.forEach(btnId => {
        const btn = document.getElementById(btnId);
        if (btn) {
            console.log(`✅ Botón '${btnId}' encontrado`);
            
            // Agregar un event listener temporal para confirmar funcionalidad
            btn.addEventListener('click', function(e) {
                console.log(`Click en botón '${btnId}' detectado por debug.js`);
            });
            
            // Mostrar estilos relevantes
            const styles = window.getComputedStyle(btn);
            console.log(`Estilos de '${btnId}':`, {
                display: styles.display,
                visibility: styles.visibility,
                opacity: styles.opacity,
                position: styles.position,
                zIndex: styles.zIndex
            });
        } else {
            console.error(`❌ Botón '${btnId}' NO encontrado`);
        }
    });
    
    // Verificar que podemos acceder a funciones importantes
    const criticalFunctions = [
        'generateAndSendDirect',
        'generarJSON',
        'enviarDTE'
    ];
    
    criticalFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ Función '${funcName}' definida y accesible`);
        } else {
            console.error(`❌ Función '${funcName}' NO definida o no accesible`);
        }
    });
    
    // Agregar un botón de emergencia para probar la función directamente
    const emergencyButton = document.createElement('button');
    emergencyButton.textContent = 'BOTÓN DE EMERGENCIA - GENERAR Y ENVIAR DTE';
    emergencyButton.style.position = 'fixed';
    emergencyButton.style.top = '10px';
    emergencyButton.style.right = '10px';
    emergencyButton.style.zIndex = '9999';
    emergencyButton.style.backgroundColor = 'red';
    emergencyButton.style.color = 'white';
    emergencyButton.style.padding = '10px';
    emergencyButton.style.border = 'none';
    emergencyButton.style.borderRadius = '5px';
    emergencyButton.style.cursor = 'pointer';
    
    emergencyButton.addEventListener('click', function() {
        console.log('Botón de emergencia clickeado');
        alert('Botón de emergencia activado');
        
        if (typeof window.generateAndSendDirect === 'function') {
            try {
                window.generateAndSendDirect();
            } catch (error) {
                console.error('Error al llamar generateAndSendDirect:', error);
                alert('Error: ' + error.message);
            }
        } else {
            alert('La función generateAndSendDirect no está definida');
        }
    });
    
    document.body.appendChild(emergencyButton);
    
    console.log('=== Fin de la carga del script de depuración ===');
});
