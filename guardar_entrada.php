<?php
// Activar logging de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Crear archivo de log específico para este script
$logFile = __DIR__ . '/entrada_log.txt';

function logMessage($message, $type = 'INFO') {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp][$type] $message\n";
    error_log($logEntry, 3, $logFile);
}

// Log de inicio de solicitud
logMessage("Nueva solicitud recibida");
logMessage("Método HTTP: " . $_SERVER['REQUEST_METHOD']);
logMessage("Headers recibidos: " . json_encode(getallheaders()));

require_once 'db_connection.php';

header('Content-Type: application/json');

try {
    // Obtener y decodificar los datos JSON
    $rawData = file_get_contents('php://input');
    logMessage("Datos recibidos: " . $rawData);

    $datos = json_decode($rawData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Error al decodificar JSON: ' . json_last_error_msg());
    }

    logMessage("Datos decodificados: " . json_encode($datos));

    // Validar la estructura de los datos
    if (!isset($datos['repuestos']) || !is_array($datos['repuestos']) || empty($datos['repuestos'])) {
        throw new Exception('No se recibieron repuestos para procesar');
    }

    // Obtener la conexión a la base de datos
    $conn = getConnection();
    logMessage("Conexión a base de datos establecida");

    $conn->beginTransaction();
    logMessage("Iniciando transacción");

    // Preparar la consulta para insertar en la tabla de movimientos
    $query = "INSERT INTO movimiento_inventario
              (repuesto_id, almacen_id, tipo_movimiento, cantidad,
               referencia_documento, usuario, notas)
              VALUES (:repuesto_id, :almacen_id, :tipo_movimiento, :cantidad,
                      :referencia_documento, :usuario, :notas)";

    $stmt = $conn->prepare($query);
    logMessage("Query preparada: " . $query);

    // Procesar cada repuesto
    foreach ($datos['repuestos'] as $repuesto) {
        logMessage("Procesando repuesto: " . json_encode($repuesto));

        // Validar que los campos requeridos existan
        if (!isset($repuesto['repuesto_id']) || !is_numeric($repuesto['repuesto_id'])) {
            throw new Exception('ID de repuesto inválido');
        }
        if (!isset($repuesto['cantidad']) || !is_numeric($repuesto['cantidad'])) {
            throw new Exception('Cantidad inválida');
        }

        // Ejecutar la consulta
        $result = $stmt->execute([
            ':repuesto_id' => (int)$repuesto['repuesto_id'],
            ':almacen_id' => (int)$datos['almacen_id'],
            ':tipo_movimiento' => $datos['tipo_movimiento'],
            ':cantidad' => (int)$repuesto['cantidad'],
            ':referencia_documento' => $datos['referencia_documento'] ?? '',
            ':usuario' => 'USUARIO_ACTUAL', // Esto debería venir de la sesión
            ':notas' => $datos['notas'] ?? ''
        ]);

        if ($result) {
            logMessage("Movimiento insertado correctamente para repuesto ID: " . $repuesto['repuesto_id']);
        } else {
            logMessage("Error al insertar movimiento para repuesto ID: " . $repuesto['repuesto_id'], "ERROR");
            throw new Exception('Error al insertar: ' . implode(', ', $stmt->errorInfo()));
        }

        // No actualizamos manualmente el stock porque el trigger se encarga de eso
        logMessage("El trigger se encargará de actualizar el stock para repuesto ID: " . $repuesto['repuesto_id']);
    }

    // Confirmar la transacción
    $conn->commit();
    logMessage("Transacción completada exitosamente");

    // Devolver respuesta exitosa
    echo json_encode([
        'status' => 'success',
        'message' => 'Entrada registrada exitosamente'
    ]);

} catch (Exception $e) {
    logMessage("Error: " . $e->getMessage(), "ERROR");
    logMessage("Stack trace: " . $e->getTraceAsString(), "ERROR");

    // Revertir la transacción en caso de error
    if (isset($conn)) {
        $conn->rollBack();
        logMessage("Transacción revertida", "WARNING");
    }

    // Devolver respuesta de error
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} finally {
    // Cerrar la conexión
    if (isset($conn)) {
        $conn = null;
        logMessage("Conexión cerrada");
    }
}
?>
