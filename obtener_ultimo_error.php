<?php
// Archivo obtener_ultimo_error.php
header('Content-Type: application/json');
require_once 'db_connection.php';
require_once 'logger.php';

try {
    $conn = getConnection();
    
    // Obtener el último error registrado (suponiendo que tienes una tabla de errores)
    // Si no tienes una tabla de errores, puedes usar los logs del sistema
    $logfile = 'php_errors.log'; // Ajusta la ruta según tu configuración
    $lastLines = [];
    
    if (file_exists($logfile) && is_readable($logfile)) {
        // Leer las últimas 50 líneas del archivo de log
        if (function_exists('shell_exec')) {
            $lastLines = explode("\n", shell_exec("tail -n 50 " . escapeshellarg($logfile)));
        } else {
            // Alternativa si shell_exec no está disponible
            $file = new SplFileObject($logfile, 'r');
            $file->seek(PHP_INT_MAX); // Ir al final del archivo
            $totalLines = $file->key(); // Obtener el número total de líneas
            
            // Leer las últimas 50 líneas o todas si hay menos de 50
            $linesToRead = min(50, $totalLines);
            $startLine = max(0, $totalLines - $linesToRead);
            
            $file->seek($startLine);
            while (!$file->eof()) {
                $lastLines[] = $file->fgets();
            }
        }
    }
    
    // Buscar errores específicos en los logs
    $errorDetails = [];
    foreach ($lastLines as $line) {
        if (strpos($line, '[error]') !== false && 
            (strpos($line, 'generar_sobre.php') !== false || 
             strpos($line, 'procesar_sobres_pendientes.php') !== false)) {
            $errorDetails[] = $line;
        }
    }
    
    // También buscar en error_log de PHP
    $phpErrorLog = ini_get('error_log');
    if ($phpErrorLog && file_exists($phpErrorLog) && is_readable($phpErrorLog)) {
        if (function_exists('shell_exec')) {
            $phpErrors = explode("\n", shell_exec("tail -n 20 " . escapeshellarg($phpErrorLog)));
            foreach ($phpErrors as $line) {
                if (strpos($line, 'generar_sobre.php') !== false || 
                    strpos($line, 'procesar_sobres_pendientes.php') !== false) {
                    $errorDetails[] = $line;
                }
            }
        }
    }
    
    // También buscar en la base de datos, si tienes una tabla de logs
    $dbErrors = [];
    try {
        // Verificar si existe la tabla tb_logs
        $stmt = $conn->query("SHOW TABLES LIKE 'tb_logs'");
        if ($stmt->rowCount() > 0) {
            $stmt = $conn->query("
                SELECT * FROM tb_logs 
                WHERE type = 'error' 
                ORDER BY created_at DESC 
                LIMIT 10
            ");
            $dbErrors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        // Si la tabla no existe, simplemente continuamos
    }
    
    // Verificar si hay archivos XML faltantes en la base de datos
    $xmlFaltantes = [];
    try {
        $stmt = $conn->query("
            SELECT id, tipo_dte, nombre_archivo 
            FROM tb_facturas_dte 
            WHERE estado_sobre = 0
            LIMIT 10
        ");
        $pendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($pendientes as $doc) {
            if (!file_exists($doc['nombre_archivo'])) {
                $xmlFaltantes[] = [
                    'id' => $doc['id'],
                    'tipo_dte' => $doc['tipo_dte'],
                    'ruta' => $doc['nombre_archivo'],
                    'existe' => 'no'
                ];
            }
        }
    } catch (Exception $e) {
        // Si hay error, continuamos
    }
    
    echo json_encode([
        'success' => true,
        'error_details' => $errorDetails,
        'db_errors' => $dbErrors,
        'xml_faltantes' => $xmlFaltantes,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'Error al obtener detalles: ' . $e->getMessage(),
        'details' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
