[2025-04-08 03:02:30] Iniciando proceso de envío de sobre
[2025-04-08 03:02:30] Parámetros recibidos
Data: {
    "apiKey": "8131****",
    "rutCertificado": "17365958-K",
    "ambiente": 1
}
[2025-04-08 03:02:30] Certificado encontrado correctamente
[2025-04-08 03:02:30] Conectando a base de datos
[2025-04-08 03:02:30] Conexión a base de datos exitosa
[2025-04-08 03:02:30] Consultando sobres pendientes de envío
[2025-04-08 03:02:30] Sobre encontrado
Data: {
    "id": 5,
    "nombre_archivo": "Documents\/sobreEnvio\/sobreEnvio_DTE39_20250408_015939_10docs.xml",
    "tipoEnvio": "ENVIO_BOLETA",
    "ruta_archivo": null
}
[2025-04-08 03:02:30] Archivo de sobre encontrado correctamente
[2025-04-08 03:02:30] Preparando solicitud a API
[2025-04-08 03:02:30] JSON de entrada preparado
Data: {
    "Certificado": {
        "Password": "1569",
        "Rut": "17365958-K"
    },
    "Ambiente": 1,
    "Tipo": 2
}
[2025-04-08 03:02:30] Configurando solicitud cURL
[2025-04-08 03:02:30] Tamaño del archivo de sobre
Data: {
    "size": 59343
}
[2025-04-08 03:02:30] Ejecutando solicitud a API
[2025-04-08 03:02:30] Respuesta recibida
Data: {
    "httpCode": 401,
    "responseLength": 0,
    "curlError": "None"
}
[2025-04-08 03:02:30] Respuesta completa de API
[2025-04-08 03:02:30] Análisis de respuesta
Data: {
    "esExitoso": false,
    "trackId": null,
    "glosa": "Env\u00edo procesado",
    "httpCode": 401
}
[2025-04-08 03:02:30] Error en el envío
Data: {
    "errorMsg": "Error en el env\u00edo del sobre",
    "response": null
}
