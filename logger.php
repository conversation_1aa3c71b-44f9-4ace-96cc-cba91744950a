<?php
class Logger {
    private static $logs = [];
    private static $startTime;

    public static function init() {
        self::$startTime = microtime(true);
        self::$logs = [];
    }

    public static function log($message, $type = 'info', $data = null) {
        $time = microtime(true) - self::$startTime;
        $log = [
            'timestamp' => date('Y-m-d H:i:s'),
            'time' => round($time, 4),
            'type' => $type,
            'message' => $message,
            'data' => $data
        ];
        self::$logs[] = $log;

        if (isset($_SESSION['debug_mode']) && $_SESSION['debug_mode']) {
            echo self::formatLogEntry($log);
            ob_flush();
            flush();
        }
    }

    public static function formatLogEntry($log) {
        $typeColors = [
            'info' => '#2196F3',
            'success' => '#4CAF50',
            'error' => '#F44336',
            'warning' => '#FF9800',
            'debug' => '#9C27B0'
        ];

        $color = $typeColors[$log['type']] ?? '#757575';
        $dataHtml = '';
        
        if ($log['data'] !== null) {
            if (is_array($log['data']) || is_object($log['data'])) {
                $dataHtml = '<pre>' . htmlspecialchars(json_encode($log['data'], JSON_PRETTY_PRINT)) . '</pre>';
            } else {
                $dataHtml = '<pre>' . htmlspecialchars($log['data']) . '</pre>';
            }
        }

        return sprintf(
            '<div class="log-entry" style="margin: 5px 0; padding: 5px; border-left: 4px solid %s; background: #f5f5f5;">
                <span style="color: #666;">[%s] (+%ss)</span>
                <span style="color: %s; font-weight: bold;">%s</span>
                <span>%s</span>
                %s
            </div>',
            $color,
            $log['timestamp'],
            $log['time'],
            $color,
            strtoupper($log['type']),
            htmlspecialchars($log['message']),
            $dataHtml
        );
    }

    public static function getLogs() {
        return self::$logs;
    }

    public static function clear() {
        self::$logs = [];
        self::$startTime = microtime(true);
    }
}
?>
