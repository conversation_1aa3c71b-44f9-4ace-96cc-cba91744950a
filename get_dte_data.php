<?php
require_once 'auth_check.php';
require_once 'db_connection.php';

header('Content-Type: application/json');

// Obtener el mes del parámetro GET
$mes = isset($_GET['mes']) ? $_GET['mes'] : 'current';

try {
    $conn = getConnection();
    
    // Construir la consulta SQL base
    $sql = "
        SELECT DTE.*
        , ENVIOS.estado_envio
        , ENVIOS.glosa
        , ENVIOS.trackid
        , ENVIOS.fecha_envio
        , COALESCE(JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Totales.MontoTotal')), 0) as monto_total
        , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.Rut')) as rut_receptor
        , JSON_UNQUOTE(JSON_EXTRACT(DTE.json_enviado, '$.Documento.Encabezado.Receptor.RazonSocial')) as razon_social_receptor
        , DTE.archivo_pdf
        , DTE.id_voucher
        FROM tb_facturas_dte DTE
        LEFT JOIN tb_sobre_envios ENVIOS
        ON DTE.id_sobre = ENVIOS.id
    ";
    
    // Agregar filtro según el mes seleccionado
    if ($mes === 'current') {
        // Últimos 30 registros
        $sql .= " ORDER BY fecha_generacion DESC LIMIT 30";
        $stmt = $conn->prepare($sql);
    } else {
        // Filtrar por mes específico
        $sql .= " WHERE DATE_FORMAT(DTE.fecha_generacion, '%Y-%m') = :mes
                  ORDER BY fecha_generacion DESC";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':mes', $mes);
    }
    
    $stmt->execute();
    $documentos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formatear los datos para enviar como JSON
    $response = array(
        'success' => true,
        'data' => array()
    );
    
    foreach ($documentos as $doc) {
        $row = array(
            'id' => $doc['id'],
            'tipo_dte' => $doc['tipo_dte'],
            'folio' => $doc['folio'],
            'fecha_generacion' => $doc['fecha_generacion'],
            'rut_receptor' => $doc['rut_receptor'],
            'razon_social_receptor' => $doc['razon_social_receptor'],
            'monto_total' => $doc['monto_total'],
            'estado_sobre' => $doc['estado_sobre'],
            'id_sobre' => $doc['id_sobre'],
            'trackid' => $doc['trackid'] ?? 'N/A',
            'id_voucher' => $doc['id_voucher'] ?? 'N/A',
            'archivo_pdf' => $doc['archivo_pdf'],
            'nombre_archivo' => $doc['nombre_archivo']
        );
        $response['data'][] = $row;
    }
    
    echo json_encode($response);
    
} catch(Exception $e) {
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage()
    ));
}
?>