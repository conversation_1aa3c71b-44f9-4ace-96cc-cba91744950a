<?php
// Habilitar todos los errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_connection.php';

echo "<h1>Prueba de Subcategorías</h1>";

try {
    // Obtener conexión a la base de datos
    $conn = getConnection();
    echo "<p>Conexión a la base de datos establecida correctamente.</p>";
    
    // Obtener todas las categorías principales
    $stmt = $conn->query("SELECT id, nombre FROM categoria_repuesto WHERE categoria_padre_id IS NULL ORDER BY nombre");
    $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Categorías Principales</h2>";
    echo "<ul>";
    foreach ($categorias as $categoria) {
        echo "<li>ID: " . htmlspecialchars($categoria['id']) . " - Nombre: " . htmlspecialchars($categoria['nombre']) . "</li>";
    }
    echo "</ul>";
    
    // Obtener subcategorías para cada categoría principal
    echo "<h2>Subcategorías por Categoría</h2>";
    foreach ($categorias as $categoria) {
        echo "<h3>Subcategorías de " . htmlspecialchars($categoria['nombre']) . " (ID: " . htmlspecialchars($categoria['id']) . ")</h3>";
        
        $stmt = $conn->prepare("SELECT id, nombre FROM categoria_repuesto WHERE categoria_padre_id = ? ORDER BY nombre");
        $stmt->execute([$categoria['id']]);
        $subcategorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($subcategorias)) {
            echo "<p>No hay subcategorías para esta categoría.</p>";
        } else {
            echo "<ul>";
            foreach ($subcategorias as $subcategoria) {
                echo "<li>ID: " . htmlspecialchars($subcategoria['id']) . " - Nombre: " . htmlspecialchars($subcategoria['nombre']) . "</li>";
            }
            echo "</ul>";
        }
    }
    
    // Probar el endpoint get_subcategorias.php
    echo "<h2>Prueba del Endpoint get_subcategorias.php</h2>";
    echo "<p>Seleccione una categoría para probar el endpoint:</p>";
    
    echo "<form id='testForm'>";
    echo "<select id='categoria_id'>";
    echo "<option value=''>Seleccione una categoría</option>";
    foreach ($categorias as $categoria) {
        echo "<option value='" . htmlspecialchars($categoria['id']) . "'>" . htmlspecialchars($categoria['nombre']) . "</option>";
    }
    echo "</select>";
    echo "<button type='button' onclick='testEndpoint()'>Probar</button>";
    echo "</form>";
    
    echo "<div id='result'></div>";
    
    echo "<script>
    function testEndpoint() {
        const categoriaId = document.getElementById('categoria_id').value;
        if (!categoriaId) {
            alert('Por favor seleccione una categoría');
            return;
        }
        
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = 'Cargando...';
        
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'get_subcategorias.php?categoria_id=' + categoriaId, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('Cache-Control', 'no-cache');
        
        xhr.onload = function() {
            resultDiv.innerHTML = '<h3>Respuesta del Endpoint</h3>';
            resultDiv.innerHTML += '<p>Status: ' + xhr.status + '</p>';
            resultDiv.innerHTML += '<pre>' + xhr.responseText + '</pre>';
            
            try {
                const data = JSON.parse(xhr.responseText);
                resultDiv.innerHTML += '<h3>Datos Parseados</h3>';
                resultDiv.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                resultDiv.innerHTML += '<p>Error al parsear JSON: ' + e.message + '</p>';
            }
        };
        
        xhr.onerror = function() {
            resultDiv.innerHTML = '<p>Error de red</p>';
        };
        
        xhr.send();
    }
    </script>";
    
} catch(Exception $e) {
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
