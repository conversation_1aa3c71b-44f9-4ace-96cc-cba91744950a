/**
 * image-viewer.js - Funcionalidad para visualizar imágenes ampliadas
 */

// Función para inicializar el visor de imágenes
function initImageViewer() {
    // Verificar si el modal ya existe en el DOM
    if (!document.getElementById('imageViewerModal')) {
        // Crear el modal del visor de imágenes
        const modal = document.createElement('div');
        modal.id = 'imageViewerModal';
        modal.className = 'image-viewer-modal';
        
        // Contenido del modal
        modal.innerHTML = `
            <div class="image-viewer-content">
                <span class="image-viewer-close">&times;</span>
                <img class="image-viewer-img" id="viewerImage">
            </div>
        `;
        
        // Agregar el modal al body
        document.body.appendChild(modal);
        
        // Agregar evento para cerrar el modal
        const closeBtn = modal.querySelector('.image-viewer-close');
        closeBtn.addEventListener('click', closeImageViewer);
        
        // Cerrar el modal al hacer clic fuera de la imagen
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                closeImageViewer();
            }
        });
        
        // Cerrar el modal con la tecla Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageViewer();
            }
        });
    }
    
    // Agregar estilos CSS si no existen
    if (!document.getElementById('imageViewerStyles')) {
        const styles = document.createElement('style');
        styles.id = 'imageViewerStyles';
        styles.textContent = `
            .image-viewer-modal {
                display: none;
                position: fixed;
                z-index: 9999;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                background-color: rgba(0, 0, 0, 0.9);
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            .image-viewer-content {
                position: relative;
                margin: auto;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                width: 100%;
                max-width: 1200px;
            }
            
            .image-viewer-img {
                max-width: 90%;
                max-height: 90%;
                object-fit: contain;
                border: 2px solid white;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
                animation: zoomIn 0.3s;
            }
            
            @keyframes zoomIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
            
            .image-viewer-close {
                position: absolute;
                top: 15px;
                right: 25px;
                color: white;
                font-size: 40px;
                font-weight: bold;
                cursor: pointer;
                z-index: 10000;
                text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            }
            
            .image-viewer-close:hover {
                color: #ccc;
            }
            
            /* Estilos para el icono de lupa */
            .image-zoom-icon {
                position: absolute;
                top: 10px;
                right: 10px;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 10;
            }
            
            .product-card:hover .image-zoom-icon,
            .table-image-container:hover .image-zoom-icon {
                opacity: 1;
            }
            
            /* Contenedor para la imagen en la tabla */
            .table-image-container {
                position: relative;
                display: inline-block;
                cursor: pointer;
            }
        `;
        document.head.appendChild(styles);
    }
}

// Función para abrir el visor de imágenes
function openImageViewer(imageSrc) {
    // Si la imagen es la de "no-image.png", no hacer nada
    if (imageSrc.includes('no-image.png') || !imageSrc) {
        return;
    }
    
    const modal = document.getElementById('imageViewerModal');
    const modalImg = document.getElementById('viewerImage');
    
    // Establecer la imagen
    modalImg.src = imageSrc;
    
    // Mostrar el modal
    modal.style.display = 'block';
    
    // Prevenir scroll en el body
    document.body.style.overflow = 'hidden';
}

// Función para cerrar el visor de imágenes
function closeImageViewer() {
    const modal = document.getElementById('imageViewerModal');
    modal.style.display = 'none';
    
    // Restaurar scroll en el body
    document.body.style.overflow = 'auto';
}

// Inicializar el visor de imágenes cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    initImageViewer();
    
    // Agregar eventos a las imágenes en la vista de tarjetas (para index.php)
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        const imgContainer = card.querySelector('.product-image-container');
        if (imgContainer) {
            const img = imgContainer.querySelector('img');
            const zoomIcon = imgContainer.querySelector('.image-zoom-icon');
            
            if (zoomIcon) {
                zoomIcon.addEventListener('click', function(e) {
                    e.stopPropagation(); // Evitar que el clic se propague a la tarjeta
                    if (img && img.src) {
                        openImageViewer(img.src);
                    }
                });
            }
        }
    });
    
    // Agregar eventos a las imágenes en la vista de tabla (para inventory.php)
    const tableImages = document.querySelectorAll('.table-image-container');
    tableImages.forEach(container => {
        container.addEventListener('click', function() {
            const img = container.querySelector('img');
            if (img && img.src && !img.src.includes('no-image.png')) {
                openImageViewer(img.src);
            }
        });
    });
});
