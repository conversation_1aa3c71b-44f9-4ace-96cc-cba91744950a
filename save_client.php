
<?php
require_once 'db_connection.php';

try {
    $data = json_decode(file_get_contents('php://input'), true);
    $conn = getConnection();
    
    $stmt = $conn->prepare("INSERT INTO cliente (nombre, direccion, rut, telefono) VALUES (?, ?, ?, ?)");
    $stmt->execute([
        $data['nombre'],
        $data['direccion'],
        $data['rut'],
        $data['telefono']
    ]);

    $cliente_id = $conn->lastInsertId();
    echo json_encode([
        'status' => 'success', 
        'message' => 'Cliente guardado exitosamente',
        'cliente_id' => $cliente_id
    ]);
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>
