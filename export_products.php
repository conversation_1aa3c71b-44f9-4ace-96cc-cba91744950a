<?php
ob_start();
require_once 'db_connection.php';

try {
    $conn = getConnection();
    $stmt = $conn->query("SELECT
    r.id,
    r.sku,
    r.nombre,
    r.descripcion,
    r.categoria_id,
    r.id_subcategoria,
    r.precio_compra,
    r.precio_venta,
    r.stock_minimo,
    r.stock_maximo,
    r.unidad_medida,
    r.ubicacion_almacen,
    r.es_original,
    r.fabricante,
    r.pais_origen,
    r.codigo_fabricante,
    r.activo,
    c.nombre as categoria_nombre,
    sc.nombre as subcategoria_nombre
FROM
    repuesto r
    LEFT JOIN categoria_repuesto c ON r.categoria_id = c.id
    LEFT JOIN categoria_repuesto sc ON r.id_subcategoria = sc.id
WHERE
    r.activo = 1");

    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    ob_end_clean();

    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename=repuestos.csv');
    header('Pragma: public');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    $output = fopen('php://output', 'w');
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    fputcsv($output, [
        'SKU', 'Nombre', 'Categoría', 'Subcategoría', 'Descripción', 'Precio Venta', 'Stock Mínimo',
        'Stock Máximo', 'Es Original', 'Fabricante', 'País Origen'
    ]);

    foreach ($products as $product) {
        fputcsv($output, [
            $product['sku'] ?? '',
            $product['nombre'] ?? '',
            $product['categoria_nombre'] ?? 'N/A',
            $product['subcategoria_nombre'] ?? 'N/A',
            $product['descripcion'] ?? '',
            $product['precio_venta'] ?? '',
            $product['stock_minimo'] ?? '',
            $product['stock_maximo'] ?? '',
            $product['es_original'] ? 'Sí' : 'No',
            $product['fabricante'] ?? '',
            $product['pais_origen'] ?? ''
        ]);
    }

    fclose($output);
    exit();

} catch (Exception $e) {
    ob_end_clean();
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
    exit();
}
