<?php
require_once 'db_connection.php';
header('Content-Type: application/json');

try {
    error_log("Recibiendo petición para marca_id: " . $_GET['marca_id'] ?? 'no definido');

    if (!isset($_GET['marca_id']) || !is_numeric($_GET['marca_id'])) {
        throw new Exception('ID de marca inválido');
    }

    $marca_id = (int)$_GET['marca_id'];
    $conn = getConnection();
    
    $stmt = $conn->prepare("SELECT id, nombre FROM modelo WHERE marca_id = ? ORDER BY nombre ASC");
    $stmt->execute([$marca_id]);
    
    $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("Resultados encontrados: " . count($resultados));
    
    if (empty($resultados)) {
        echo json_encode([]);
    } else {
        echo json_encode($resultados);
    }

} catch(Exception $e) {
    error_log("Error en get_modelos.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}