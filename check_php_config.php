<?php
/**
 * Script para verificar la configuración de PHP relacionada con sesiones
 * Ejecutar este script para verificar si las configuraciones locales son respetadas
 */

// Establecer cabeceras para evitar caché
header('Content-Type: text/plain; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');

// Mostrar configuración actual
echo "===== CONFIGURACIÓN ACTUAL DE SESIONES =====\n\n";

// Valores relacionados con duraciones de sesión
echo "session.gc_maxlifetime = " . ini_get('session.gc_maxlifetime') . " segundos (" . formatSeconds(ini_get('session.gc_maxlifetime')) . ")\n";
echo "session.cookie_lifetime = " . ini_get('session.cookie_lifetime') . " segundos (" . formatSeconds(ini_get('session.cookie_lifetime')) . ")\n\n";

// Valores relacionados con la seguridad de la sesión
echo "session.cookie_httponly = " . ini_get('session.cookie_httponly') . " (1=Activado, 0=Desactivado)\n";
echo "session.cookie_secure = " . ini_get('session.cookie_secure') . " (1=Activado, 0=Desactivado)\n";
echo "session.cookie_samesite = " . ini_get('session.cookie_samesite') . "\n";
echo "session.use_strict_mode = " . ini_get('session.use_strict_mode') . " (1=Activado, 0=Desactivado)\n";
echo "session.use_cookies = " . ini_get('session.use_cookies') . " (1=Activado, 0=Desactivado)\n";
echo "session.use_only_cookies = " . ini_get('session.use_only_cookies') . " (1=Activado, 0=Desactivado)\n";
echo "session.use_trans_sid = " . ini_get('session.use_trans_sid') . " (1=Activado, 0=Desactivado)\n\n";

// Valores relacionados con el almacenamiento de la sesión
echo "session.save_handler = " . ini_get('session.save_handler') . "\n";
echo "session.save_path = " . ini_get('session.save_path') . "\n";
echo "session.gc_probability = " . ini_get('session.gc_probability') . "\n";
echo "session.gc_divisor = " . ini_get('session.gc_divisor') . "\n";
echo "session.cache_expire = " . ini_get('session.cache_expire') . " minutos\n";
echo "session.cache_limiter = " . ini_get('session.cache_limiter') . "\n\n";

// Verificar si las configuraciones son las esperadas
echo "===== VERIFICACIÓN DE CONFIGURACIÓN =====\n\n";

// Cargar nuestra configuración para compararla
require_once 'session_config.php';

// Verificar configuraciones críticas
$expected_gc_maxlifetime = 30 * 24 * 60 * 60; // 30 días
$expected_cookie_lifetime = 30 * 24 * 60 * 60; // 30 días

echo "Comparación session.gc_maxlifetime:\n";
echo "- Esperado: " . $expected_gc_maxlifetime . " segundos (" . formatSeconds($expected_gc_maxlifetime) . ")\n";
echo "- Actual: " . ini_get('session.gc_maxlifetime') . " segundos (" . formatSeconds(ini_get('session.gc_maxlifetime')) . ")\n";
echo "- Coincide: " . (ini_get('session.gc_maxlifetime') == $expected_gc_maxlifetime ? "SÍ" : "NO") . "\n\n";

echo "Comparación session.cookie_lifetime:\n";
echo "- Esperado: " . $expected_cookie_lifetime . " segundos (" . formatSeconds($expected_cookie_lifetime) . ")\n";
echo "- Actual: " . ini_get('session.cookie_lifetime') . " segundos (" . formatSeconds(ini_get('session.cookie_lifetime')) . ")\n";
echo "- Coincide: " . (ini_get('session.cookie_lifetime') == $expected_cookie_lifetime ? "SÍ" : "NO") . "\n\n";

// Verificar configuraciones de seguridad
echo "Verificación de configuraciones de seguridad:\n";
checkConfig('session.cookie_httponly', 1);
checkConfig('session.cookie_secure', 1);
checkConfig('session.cookie_samesite', 'Strict');
checkConfig('session.use_only_cookies', 1);
checkConfig('session.use_trans_sid', 0);

// Información sobre la sesión actual
echo "\n===== INFORMACIÓN DE LA SESIÓN ACTUAL =====\n\n";

// Iniciar sesión si no está iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "ID de sesión: " . session_id() . "\n";
echo "Nombre de sesión: " . session_name() . "\n";
echo "Ubicación del archivo de sesión: " . session_save_path() . '/' . 'sess_' . session_id() . "\n";
echo "Parámetros de cookie: " . print_r(session_get_cookie_params(), true) . "\n";

// Información sobre almacenamiento físico de sesiones
echo "\n===== VERIFICACIÓN DE ALMACENAMIENTO DE SESIONES =====\n\n";

$save_path = ini_get('session.save_path');
if (empty($save_path)) {
    echo "La ruta de almacenamiento de sesiones no está configurada.\n";
} else {
    echo "Ruta de almacenamiento de sesiones: " . $save_path . "\n";
    
    if (is_dir($save_path)) {
        echo "La ruta existe y es un directorio.\n";
        
        if (is_writable($save_path)) {
            echo "El directorio tiene permisos de escritura.\n";
            
            // Contar archivos de sesión
            $session_files = glob($save_path . '/sess_*');
            echo "Número de archivos de sesión encontrados: " . count($session_files) . "\n";
            
            // Verificar espacio en disco
            $disk_free = disk_free_space($save_path);
            $disk_total = disk_total_space($save_path);
            $disk_used = $disk_total - $disk_free;
            $disk_used_percent = ($disk_used / $disk_total) * 100;
            
            echo "Espacio en disco:\n";
            echo "- Total: " . formatBytes($disk_total) . "\n";
            echo "- Libre: " . formatBytes($disk_free) . "\n";
            echo "- Usado: " . formatBytes($disk_used) . " (" . number_format($disk_used_percent, 2) . "%)\n";
        } else {
            echo "ADVERTENCIA: El directorio NO tiene permisos de escritura. Esto puede causar problemas con las sesiones.\n";
        }
    } else {
        echo "ADVERTENCIA: La ruta no existe o no es un directorio. Esto puede causar problemas con las sesiones.\n";
    }
}

/**
 * Formatea segundos en un formato legible (días, horas, minutos, segundos)
 * @param int $seconds Número de segundos
 * @return string Tiempo formateado
 */
function formatSeconds($seconds) {
    $seconds = (int)$seconds;
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    $result = '';
    if ($days > 0) $result .= "$days días, ";
    if ($hours > 0) $result .= "$hours horas, ";
    if ($minutes > 0) $result .= "$minutes minutos, ";
    $result .= "$secs segundos";
    
    return $result;
}

/**
 * Formatea bytes en un formato legible (KB, MB, GB, TB)
 * @param int $bytes Número de bytes
 * @return string Bytes formateados
 */
function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Verifica si una configuración coincide con el valor esperado
 * @param string $name Nombre de la configuración
 * @param mixed $expected Valor esperado
 */
function checkConfig($name, $expected) {
    $actual = ini_get($name);
    $matches = ($actual == $expected);
    
    echo "- $name: " . ($matches ? "OK" : "FALLO") . " (Esperado: $expected, Actual: $actual)\n";
}
?>