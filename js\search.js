document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Inicializando funcionalidad de búsqueda...');

// Variable global para almacenar el término de búsqueda actual
window.currentSearchTerm = '';

function filterTable(tableId) {
    const table = document.getElementById(tableId);
    if (!table) {
        console.log(`Tabla con ID ${tableId} no encontrada`);
        return;
    }

    const rows = table.getElementsByTagName('tr');
    const filters = table.getElementsByClassName('column-search');

    // Add event listeners to all search inputs in this table
    Array.from(filters).forEach((filter, index) => {
        filter.addEventListener('keyup', function() {
            filterRows();
        });
    });

    function filterRows() {
        for (let i = 2; i < rows.length; i++) {
            let row = rows[i];
            let showRow = true;

            for (let j = 0; j < filters.length; j++) {
                let cell = row.cells[j];
                let filter = filters[j].value.toUpperCase();

                if (cell && filter) {
                    let text = cell.textContent || cell.innerText;
                    if (text.toUpperCase().indexOf(filter) === -1) {
                        showRow = false;
                        break;
                    }
                }
            }

            row.style.display = showRow ? '' : 'none';
        }
    }
}

// Call filterTable for each table that needs filtering
// Solo intentar filtrar la tabla si existe
const tablaRepuestos = document.getElementById('tabla_repuestos');
if (tablaRepuestos) {
    filterTable('tabla_repuestos');
}

// Funcionalidad de búsqueda para el header
const headerSearchInput = document.getElementById('search-input');
const headerSearchButton = document.getElementById('search-icon');
const headerResetButton = document.getElementById('reset-filters');

// Verificar si los elementos existen antes de agregar event listeners
console.log('Elementos de búsqueda:', {
    input: headerSearchInput ? 'Encontrado' : 'No encontrado',
    searchBtn: headerSearchButton ? 'Encontrado' : 'No encontrado',
    resetBtn: headerResetButton ? 'Encontrado' : 'No encontrado'
});

if (headerSearchInput && headerSearchButton && headerResetButton) {
    console.log('Inicializando funcionalidad de búsqueda en el header');

    // Función para aplicar filtro a la vista de tabla
    function applyTableFilter(searchTerm) {
        try {
            console.log('Aplicando filtro a la tabla con término:', searchTerm);
            const tableRows = document.querySelectorAll('.products-table tbody tr');
            let foundCount = 0;

            if (tableRows.length === 0) {
                console.warn('No se encontraron filas en la tabla');
                return 0;
            }

            tableRows.forEach((row, index) => {
                try {
                    // Buscar en todo el texto de la fila
                    const rowText = row.textContent.toLowerCase();
                    const matchFound = rowText.includes(searchTerm.toLowerCase());

                    if (matchFound) {
                        // Forzar visibilidad con !important y establecer attribute para compatibilidad
                        row.style.cssText = 'display: table-row !important';
                        row.setAttribute('data-matches-filter', 'true');
                        foundCount++;
                        console.log(`Fila ${index + 1}: COINCIDENCIA ENCONTRADA - Mostrando`);
                    } else {
                        // Forzar ocultamiento con !important
                        row.style.cssText = 'display: none !important';
                        row.setAttribute('data-matches-filter', 'false');
                        console.log(`Fila ${index + 1}: Sin coincidencia - Ocultando`);
                    }
                } catch (rowError) {
                    console.error(`Error al procesar fila ${index}:`, rowError);
                }
            });

            // Forzar repintado del DOM
            const tableContainer = document.querySelector('.products-table');
            if (tableContainer) {
                tableContainer.style.opacity = '0.99';
                setTimeout(() => {
                    tableContainer.style.opacity = '1';
                }, 10);
            }

            console.log(`Filtro de tabla completado. Resultados encontrados: ${foundCount}`);
            return foundCount;
        } catch (error) {
            console.error('Error al aplicar filtro a la tabla:', error);
            return 0;
        }
    }

    // Función para aplicar filtro a la vista de tarjetas
    function applyCardFilter(searchTerm) {
        try {
            console.log('Aplicando filtro a las tarjetas con término:', searchTerm);
            const cards = document.querySelectorAll('.product-card');
            let foundCount = 0;

            if (cards.length === 0) {
                console.warn('No se encontraron tarjetas');
                return 0;
            }

            cards.forEach((card, index) => {
                try {
                    // Buscar en todo el texto de la tarjeta
                    const cardText = card.textContent.toLowerCase();
                    const matchFound = cardText.includes(searchTerm.toLowerCase());

                    if (matchFound) {
                        // Forzar visibilidad con !important
                        card.style.cssText = 'display: flex !important';
                        card.setAttribute('data-matches-filter', 'true');
                        foundCount++;
                        console.log(`Tarjeta ${index + 1}: COINCIDENCIA ENCONTRADA - Mostrando`);
                    } else {
                        // Forzar ocultamiento con !important
                        card.style.cssText = 'display: none !important';
                        card.setAttribute('data-matches-filter', 'false');
                        console.log(`Tarjeta ${index + 1}: Sin coincidencia - Ocultando`);
                    }
                } catch (cardError) {
                    console.error(`Error al procesar tarjeta ${index}:`, cardError);
                }
            });

            console.log(`Filtro de tarjetas completado. Resultados encontrados: ${foundCount}`);
            return foundCount;
        } catch (error) {
            console.error('Error al aplicar filtro a las tarjetas:', error);
            return 0;
        }
    }

    // Función para realizar la búsqueda en el header
    function performHeaderSearch() {
        try {
            console.log('Realizando búsqueda...');
            const searchTerm = headerSearchInput.value.toLowerCase().trim();
            console.log('Término de búsqueda:', searchTerm);

            // Guardar el término de búsqueda actual globalmente
            window.currentSearchTerm = searchTerm;

            if (!searchTerm) {
                // Si no hay término de búsqueda, mostrar todos los productos
                resetHeaderSearch();
                return;
            }

            // Obtener la vista actual
            const currentView = window.currentView || 'table';
            console.log('Vista actual:', currentView);

            // Aplicar filtros a ambas vistas, independientemente de cuál esté visible
            const foundInCards = applyCardFilter(searchTerm);
            const foundInTable = applyTableFilter(searchTerm);

            console.log(`Resultados encontrados - Tarjetas: ${foundInCards}, Tabla: ${foundInTable}`);

            // Mostrar mensaje si no se encontraron resultados en la vista actual
            let noResultsInCurrentView = false;

            if (currentView === 'grid') {
                noResultsInCurrentView = foundInCards === 0;
            } else if (currentView === 'table') {
                noResultsInCurrentView = foundInTable === 0;
            }

            if (noResultsInCurrentView) {
                showNoResultsMessage();
            } else {
                hideNoResultsMessage();
            }
        } catch (error) {
            console.error('Error en performHeaderSearch:', error);
        }
    }

    // Función para resetear la búsqueda del header
    function resetHeaderSearch() {
        try {
            console.log('Reseteando búsqueda...');
            // Limpiar el input de búsqueda
            headerSearchInput.value = '';
            window.currentSearchTerm = '';

            // Obtener la vista actual
            const currentView = window.currentView || 'table';

            // Mostrar todas las tarjetas
            const cards = document.querySelectorAll('.product-card');
            cards.forEach(card => {
                card.style.cssText = 'display: flex !important';
                card.removeAttribute('data-matches-filter');
            });

            // Mostrar todas las filas de la tabla
            const tableRows = document.querySelectorAll('.products-table tbody tr');
            tableRows.forEach(row => {
                row.style.cssText = 'display: table-row !important';
                row.removeAttribute('data-matches-filter');
            });

            // Forzar repintado del DOM para la tabla
            const tableContainer = document.querySelector('.products-table');
            if (tableContainer) {
                tableContainer.style.opacity = '0.99';
                setTimeout(() => {
                    tableContainer.style.opacity = '1';
                }, 10);
            }

            // Ocultar mensaje de no resultados
            hideNoResultsMessage();

            console.log('Búsqueda reseteada correctamente');
        } catch (error) {
            console.error('Error en resetHeaderSearch:', error);
        }
    }

    // Función para mostrar mensaje de no resultados
    function showNoResultsMessage() {
        console.log('Mostrando mensaje de no resultados');
        // Verificar si ya existe el mensaje
        let noResultsMsg = document.getElementById('no-results-message');

        if (!noResultsMsg) {
            // Crear el mensaje si no existe
            noResultsMsg = document.createElement('div');
            noResultsMsg.id = 'no-results-message';
            noResultsMsg.className = 'no-results';
            noResultsMsg.innerHTML = `
                <div style="text-align: center; padding: 20px; margin: 20px auto; max-width: 500px; background-color: #f8f9fa; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <i class="fas fa-search" style="font-size: 3rem; color: #95a5a6; margin-bottom: 15px;"></i>
                    <h3 style="color: #2c3e50; margin-bottom: 10px;">No se encontraron resultados</h3>
                    <p style="color: #7f8c8d;">No hay productos que coincidan con tu búsqueda. Intenta con otros términos o restablece los filtros.</p>
                </div>
            `;

            // Insertar después del contenedor de productos
            const productsContainer = document.getElementById('products-container');
            console.log('Contenedor de productos:', productsContainer ? 'Encontrado' : 'No encontrado');

            if (productsContainer) {
                // Insertar después del contenedor de productos
                productsContainer.insertAdjacentElement('afterend', noResultsMsg);
            } else {
                // Si no se encuentra el contenedor, insertar en el body
                document.body.appendChild(noResultsMsg);
            }
        } else {
            // Mostrar el mensaje si ya existe
            noResultsMsg.style.display = 'block';
        }
    }

    // Función para ocultar mensaje de no resultados
    function hideNoResultsMessage() {
        const noResultsMsg = document.getElementById('no-results-message');
        if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    // Event listeners para la búsqueda del header
    console.log('Agregando event listeners para la búsqueda');

    headerSearchButton.addEventListener('click', function(e) {
        e.preventDefault(); // Prevenir comportamiento por defecto
        console.log('Botón de búsqueda clickeado');
        performHeaderSearch();
    });

    headerSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault(); // Prevenir comportamiento por defecto
            console.log('Tecla Enter presionada en el input de búsqueda');
            performHeaderSearch();
        }
    });

    headerResetButton.addEventListener('click', function(e) {
        e.preventDefault(); // Prevenir comportamiento por defecto
        console.log('Botón de reset clickeado');
        resetHeaderSearch();
    });

    // Inicializar la búsqueda al cargar la página
    console.log('Inicialización completa de la funcionalidad de búsqueda');

    // Aplicar filtro si hay un término de búsqueda guardado
    if (headerSearchInput.value.trim()) {
        console.log('Hay un término de búsqueda al cargar la página, aplicando filtro...');
        performHeaderSearch();
    }
}

});