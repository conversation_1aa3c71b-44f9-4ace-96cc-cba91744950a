<?php
// Script de diagnóstico para verificar la estructura de la base de datos
header('Content-Type: application/json');

// Incluir la conexión a la base de datos
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Obtener la estructura de la tabla tb_facturas_dte
    $stmt = $conn->query("DESCRIBE tb_facturas_dte");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Verificar todas las tablas del sistema
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $tableInfo = [];
    foreach ($tables as $table) {
        $stmt = $conn->query("DESCRIBE $table");
        $tableInfo[$table] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Verificar si las columnas esperadas existen
    $expectedColumns = ['id', 'nombre_archivo', 'tipo_dte', 'estado_sobre'];
    $missingColumns = [];
    
    foreach ($expectedColumns as $column) {
        $found = false;
        foreach ($columns as $col) {
            if ($col['Field'] === $column) {
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            $missingColumns[] = $column;
        }
    }
    
    // Verificar si la columna 'fecha' existe
    $fechaExists = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'fecha') {
            $fechaExists = true;
            break;
        }
    }
    
    // Obtener información del sistema
    $systemInfo = [
        'php_version' => PHP_VERSION,
        'db_version' => $conn->getAttribute(PDO::ATTR_SERVER_VERSION),
        'db_driver' => $conn->getAttribute(PDO::ATTR_DRIVER_NAME)
    ];
    
    // Verificar si hay referencias a la columna 'fecha' en algún script PHP
    $referencesToFecha = []; 
    $phpFiles = glob('*.php');
    
    foreach ($phpFiles as $phpFile) {
        $content = file_get_contents($phpFile);
        if (strpos($content, 'fecha') !== false) {
            // Buscar específicamente referencias a tb_facturas_dte...fecha
            if (preg_match('/tb_facturas_dte.*fecha|fecha.*tb_facturas_dte/i', $content)) {
                $lines = explode("\n", $content);
                $matches = [];
                foreach ($lines as $index => $line) {
                    if (strpos($line, 'fecha') !== false && (
                        strpos($line, 'tb_facturas_dte') !== false || 
                        strpos($line, 'SELECT') !== false
                    )) {
                        $matches[] = [
                            'line_number' => $index + 1,
                            'text' => trim($line)
                        ];
                    }
                }
                
                if (!empty($matches)) {
                    $referencesToFecha[] = [
                        'file' => $phpFile,
                        'matches' => $matches
                    ];
                }
            }
        }
    }
    
    // Respuesta
    echo json_encode([
        'success' => true,
        'tb_facturas_dte_columns' => $columns,
        'missing_columns' => $missingColumns,
        'fecha_exists' => $fechaExists,
        'all_tables' => array_values($tables),
        'table_structures' => $tableInfo,
        'system_info' => $systemInfo,
        'references_to_fecha' => $referencesToFecha,
        'diagnostic' => [
            'has_structure_issue' => !$fechaExists && !empty($referencesToFecha),
            'possible_solution' => !$fechaExists ? 'La columna "fecha" no existe en la tabla tb_facturas_dte pero hay referencias a ella en el código. Considere agregar esta columna a la tabla.' : 'La estructura parece correcta.'
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'error_code' => $e->getCode(),
        'error_file' => $e->getFile(),
        'error_line' => $e->getLine()
    ]);
}
