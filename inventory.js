// Función para exportar productos

async function exportToExcel() {
    try {
        const response = await fetch('export_products.php');
        const data = await response.json();

        const headers = [
            "SKU", "Nombre", "Descripción", "Marca", "Modelo",
            "Versión", "Combustible", "Cilindrada", "Precio Venta",
            "Stock Mínimo", "Stock Máximo", "Es Original", 
            "Fabricante", "País Origen"
        ];

        const wb = XLSX.utils.book_new();
        const ws_data = [headers];

        data.forEach(row => {
            const processedRow = [
                row.sku || '',
                row.nombre || '',
                row.descripcion || '',
                row.marca_nombre || 'N/A',
                row.modelo_nombre || 'N/A',
                row.version || 'N/A',
                row.combustible || 'N/A',
                row.cilindrada || 'N/A',
                row.precio_venta || '',
                row.stock_minimo || '',
                row.stock_maximo || '',
                row.es_original ? 'Sí' : 'No',
                row.fabricante || '',
                row.pais_origen || ''
            ];
            ws_data.push(processedRow);
        });

        const ws = XLSX.utils.aoa_to_sheet(ws_data);
        XLSX.utils.book_append_sheet(wb, ws, "Repuestos");
        XLSX.writeFile(wb, `repuestos_${new Date().toISOString().split('T')[0]}.xlsx`);

    } catch (error) {
        console.error('Error en la exportación:', error);
        alert('Error al exportar: ' + error.message);
    }
}




// Función para abrir el modal de edición
async function openEditModal(productId) {
    try {
        // 1. Obtener y mostrar el modal
        const modal = document.getElementById('editModal');
        if (!modal) {
            throw new Error('Modal no encontrado');
        }
        modal.style.display = 'block';

        // 2. Deshabilitar el formulario mientras se cargan los datos
        const form = document.getElementById('editForm');
        if (form) {
            form.querySelectorAll('input, select, textarea').forEach(element => {
                element.disabled = true;
            });
        }

        // 3. Realizar la petición al servidor
        const baseUrl = window.location.origin;
        const url = `${baseUrl}/get_product.php?id=${productId}`;
        console.log('Consultando:', url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            },
            mode: 'same-origin',
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 4. Obtener y validar la respuesta
        const text = await response.text();
        console.log('Respuesta raw:', text);

        let data;
        try {
            data = JSON.parse(text);
        } catch (e) {
            console.error('Error al parsear JSON:', e);
            throw new Error('La respuesta del servidor no es un JSON válido');
        }

        console.log('Datos recibidos:', data);

        // 5. Validar la estructura de los datos
        if (data.status === 'success' && data.product) {
            // 6. Definir los campos a cargar
            const fields = [
                'productId',
                'nombre', 
                'descripcion',
                'categoria_id',
                'precio_compra',
                'precio_venta',
                'stock_minimo',
                'stock_maximo',
                'unidad_medida',
                'ubicacion_almacen',
                'es_original',
                'fabricante',
                'pais_origen',
                'codigo_fabricante'
            ];

            // 7. Cargar cada campo en el formulario
            fields.forEach(field => {
                const element = document.getElementById(field);
                if (element && data.product[field] !== undefined) {
                    element.value = data.product[field];
                }
            });

            // 8. Habilitar los campos después de cargar
            if (form) {
                form.querySelectorAll('input, select, textarea').forEach(element => {
                    element.disabled = false;
                });
            }

            console.log('Datos del producto cargados exitosamente');
        } else {
            throw new Error(data.message || 'Error al cargar datos del producto');
        }

    } catch (error) {
        console.error('Error:', error);
        alert('Error al cargar los datos del producto: ' + error.message);

        // Cerrar el modal en caso de error
        const modal = document.getElementById('editModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
}

// Función para cerrar el modal de edición
function closeModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.style.display = 'none';
        const form = document.getElementById('editForm');
        if (form) form.reset();
    }
}

// Abrir modal de nuevo producto o modelo
// Inicializar modales
// Función para abrir modal de nuevo producto
// function openNewProductModal() {
//     const modal = document.getElementById('newProductModal');
//     if (modal) {
//         console.log('Abriendo modal de nuevo producto');
//         modal.style.display = 'block';
//     }
// }

// Función para abrir modal de nuevo modelo


// Función para abrir modal de importación
function openImportModal() {
    const modal = document.getElementById('importModal');
    if (modal) {
        console.log('Abriendo modal de importación');
        modal.style.display = 'block';
    }
}

// Función para actualizar la lista de productos
function refreshProductList() {
    console.log('Actualizando lista de productos');
    location.reload();
}





// Inicializar eventos de modales
function initializeModals() {
    // Event listeners para botones de acción
    const newProductBtn = document.querySelector('[data-action="new-product"]');
    const newModelBtn = document.querySelector('[data-action="new-model"]');
    const importBtn = document.querySelector('[data-action="import"]');
    const refreshBtn = document.querySelector('[data-action="refresh"]');

    if (importBtn) importBtn.addEventListener('click', openImportModal);
    if (refreshBtn) refreshBtn.addEventListener('click', refreshProductList);

    // Inicializar botones de cerrar para todos los modales
    document.addEventListener('click', function(e) {
        if (e.target.matches('.modal .close') || e.target.matches('.modal .cancel-btn')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                const form = modal.querySelector('form');
                if (form) form.reset();
            }
        }
    });
}

// Asegurarse de que el DOM esté cargado antes de inicializar
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeModals);
} else {
    initializeModals();
}

function closeModelModal() {
    const modal = document.getElementById('newModelModal');
    if (modal) {
        modal.style.display = 'none';
        const form = document.getElementById('newModelForm');
        if (form) form.reset();
    }
}

// Manejar el formulario de nuevo modelo
document.getElementById('newModelForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    try {
        const formData = new FormData(this);
        const response = await fetch('create_model.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.status === 'success') {
            alert('Modelo creado exitosamente');
            closeModelModal();
            location.reload();
        } else {
            throw new Error(data.message || 'Error al crear el modelo');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error al crear el modelo: ' + error.message);
    }
});



// Función centralizada para manejar el cierre de modales
// Función centralizada para manejar el cierre de modales
function initializeModalClosers() {
    console.log('Inicializando cerrado de modales');
    
    // Registrar evento global para cerrar modales
    document.addEventListener('click', function(event) {
        // Si se hace clic en el botón X (close)
        if (event.target.classList.contains('close')) {
            const modal = event.target.closest('.modal');
            if (modal) {
                console.log('Cerrando modal por botón X:', modal.id);
                closeSpecificModal(modal);
            }
        }
        
        // Si se hace clic fuera del modal (en el fondo)
        if (event.target.classList.contains('modal')) {
            console.log('Cerrando modal por clic fuera:', event.target.id);
            closeSpecificModal(event.target);
        }
        
        // Si se hace clic en el botón cancelar
        if (event.target.classList.contains('cancel-btn')) {
            const modal = event.target.closest('.modal');
            if (modal) {
                console.log('Cerrando modal por botón cancelar:', modal.id);
                closeSpecificModal(modal);
            }
        }
    });
}




function openNewModelModal() {
    const modal = new bootstrap.Modal(document.getElementById('newModelModal'));
    modal.show();
}


document.getElementById('editForm').onsubmit = async function(e) {
   e.preventDefault();
   try {
       const formData = new FormData(e.target);
       const productId = document.getElementById('productId').value;

       if (!productId) {
           throw new Error('ID del producto no encontrado');
       }

       formData.append('id', productId);

       // Obtener la URL base del entorno actual
       const baseUrl = window.location.origin;
       const url = `${baseUrl}/update_product.php`;
       console.log('Enviando actualización a:', url);

       const response = await fetch(url, {
           method: 'POST',
           body: formData,
           headers: {
               'Accept': 'application/json',
               'Cache-Control': 'no-cache'
           },
           mode: 'same-origin',
           credentials: 'same-origin'
       });

       if (!response.ok) {
           throw new Error(`HTTP error! status: ${response.status}`);
       }

       const data = await response.json();
       console.log('Respuesta recibida:', data);

       if (data.status === 'success') {
           console.log('Producto actualizado exitosamente');
           closeModal();

           // Mostrar mensaje de éxito antes de recargar
           alert('Producto actualizado exitosamente');

           // Recargar la página para mostrar los datos actualizados
           location.reload();
       } else {
           throw new Error(data.message || 'Error al actualizar el producto');
       }

   } catch (error) {
       console.error('Error en la actualización:', error);
       alert('Error al actualizar el producto: ' + error.message);
   } finally {
       // Habilitar el formulario si estaba deshabilitado
       e.target.querySelectorAll('input, select, textarea, button').forEach(element => {
           element.disabled = false;
       });
   }
};

// Asegurarse que el formulario existe antes de añadir el evento
document.addEventListener('DOMContentLoaded', function() {




   const editForm = document.getElementById('editForm');
   if (!editForm) {
       console.error('Formulario de edición no encontrado');
       return;
   }

   // Añadir validación antes del envío
   editForm.addEventListener('submit', function(e) {


       // Deshabilitar el formulario mientras se procesa
       this.querySelectorAll('input, select, textarea, button').forEach(element => {
           element.disabled = true;
       });
   });

   // Remover clase de error al escribir
   editForm.querySelectorAll('input, select, textarea').forEach(element => {
       element.addEventListener('input', function() {
           this.classList.remove('error');
       });
   });
});


function openEntradaModal() {
    const modal = new bootstrap.Modal(document.getElementById('entradaModal'));
    modal.show();
}


// Event listener para el formulario de entrada
document.getElementById('entradaForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    try {
        const formData = new FormData(this);
        const response = await fetch('create_entrada.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.status === 'success') {
            alert('Entrada registrada exitosamente');
            closeEntradaModal();
            location.reload();
        } else {
            throw new Error(data.message || 'Error al registrar la entrada');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error al registrar la entrada: ' + error.message);
    }
});

// Event listener para el formulario de asignación
document.getElementById('assignForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    try {
        const response = await fetch('assign_product.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();
        if (data.status === 'success') {
            alert('Asignación guardada exitosamente');
           
            location.reload();
        } else {
            throw new Error(data.message || 'Error al guardar la asignación');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
});

// Función para inicializar el formulario de nuevo producto
function handleSaveProduct() {
    const form = document.getElementById('newProductForm');
    const formData = new FormData(form);

    fetch('create_product.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Respuesta del servidor:', data);
        
        if(data.status === 'success') {
            alert(data.message);
            const modal = bootstrap.Modal.getInstance(document.getElementById('newProductModal'));
            modal.hide();
            location.reload();
        } else {
            alert('Error: ' + JSON.stringify(data.message, null, 2));
        }
    })
    .catch(error => {
        console.error('Error completo:', error);
        alert('Error en la solicitud: ' + error.toString());
    });
}




function openNewProductModal() {
    const modal = new bootstrap.Modal(document.getElementById('newProductModal'));
    modal.show();
}




// // Función para cerrar el modal
// function closeNewProductModal() {
//     console.log('Cerrando modal de nuevo producto');
//     const modal = document.getElementById('newProductModal');
//     if (modal) {
//         modal.style.display = 'none';
//         const form = document.getElementById('newProductForm');
//         if (form) {
//             form.reset();
//         }
//     }
// }



function toggleAllCheckboxes() {
    const mainCheckbox = document.getElementById('selectAll');
    const checkboxes = document.getElementsByClassName('compat-check');
    for (let checkbox of checkboxes) {
        checkbox.checked = mainCheckbox.checked;
    }
}

function saveSelectedCompatibilities() {
    const selectedIds = [];
    const checkboxes = document.getElementsByClassName('compat-check');
    for (let checkbox of checkboxes) {
        if (checkbox.checked) {
            selectedIds.push(checkbox.value);
        }
    }
    
    if (selectedIds.length > 0) {
        // Here you can implement the save logic
        console.log('Selected IDs:', selectedIds);
    }
}

// ...existing code...





function openAssignModal(productId) {
    // Obtener el elemento modal
    const modalElement = document.getElementById('assignModal');
    
    // Asignar el ID del producto
    document.getElementById('assignProductId').value = productId;
    
    // Inicializar el modal de Bootstrap
    const modal = new bootstrap.Modal(modalElement);
    
    // Inicializar DataTable si aún no está inicializada
    if (!$.fn.DataTable.isDataTable('#compatibilityTable')) {
        $('#compatibilityTable').DataTable({
            language: {
                search: "Buscar:",
                lengthMenu: "Mostrar _MENU_ registros por página",
                zeroRecords: "No se encontraron registros",
                info: "Mostrando página _PAGE_ de _PAGES_",
                infoEmpty: "No hay registros disponibles",
                infoFiltered: "(filtrado de _MAX_ registros totales)",
                paginate: {
                    first: "Primero",
                    last: "Último",
                    next: "Siguiente",
                    previous: "Anterior"
                }
            },
            columnDefs: [{
                targets: 0,
                orderable: false,
                searchable: false
            }],
            order: [[1, 'asc']], 
            pageLength: 10,
            dom: 'frtip'
        });
    }

    // Agregar manejador para el botón de cierre
    const closeButton = modalElement.querySelector('.btn-close');
    closeButton.addEventListener('click', () => {
        modal.hide();
    });

    // Agregar manejador para el botón "Cerrar" en el footer
    const closeFooterButton = modalElement.querySelector('.btn-secondary');
    closeFooterButton.addEventListener('click', () => {
        modal.hide();
    });

    // Mostrar el modal
    modal.show();
}



function saveSelectedCompatibilities() {
    const selectedRows = [];
    const checkboxes = document.getElementsByClassName('compat-check');
    const productId = document.getElementById('assignProductId').value;

    for (let checkbox of checkboxes) {
        if (checkbox.checked) {
            const row = checkbox.closest('tr');
            selectedRows.push({
                vehiculo_id: checkbox.value,
                repuesto_id: productId
            });
        }
    }
    
    if (selectedRows.length > 0) {
        const requestData = {
            compatibilities: selectedRows
        };
        console.log('Datos enviados:', requestData);

        fetch('save_compatibilities.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Compatibilidades guardadas exitosamente');
                location.reload();
            } else {
                throw new Error(data.message);
            }
        })
        .catch(error => {
            alert('Error al guardar: ' + error.message);
        });
    }
}


document.getElementById('newModelForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('save_modelos.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if(data.status === 'success') {
            alert('Modelo guardado exitosamente');
            const modal = bootstrap.Modal.getInstance(document.getElementById('newModelModal'));
            modal.hide();
            location.reload();
        } else {
            alert('Error al guardar: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar el modelo');
    });
});

// Event listener para el botón de exportar
document.querySelector('button[data-action="export"]').addEventListener('click', exportToExcel);

