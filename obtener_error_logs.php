<?php
header('Content-Type: application/json');
require_once 'db_connection.php';

try {
    $conn = getConnection();

    // Verificar si existe la tabla tb_error_logs
    $stmt = $conn->query("SHOW TABLES LIKE 'tb_error_logs'");
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        // Crear la tabla si no existe
        $conn->exec("
            CREATE TABLE IF NOT EXISTS tb_error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tipo VARCHAR(50) NOT NULL,
                mensaje TEXT NOT NULL,
                detalles TEXT,
                fecha DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");

        echo json_encode([
            'success' => true,
            'message' => 'Tabla de logs creada, no hay registros aún',
            'logs' => []
        ]);
        exit;
    }

    // Obtener los últimos logs de error (aumentamos a 20 para ver más historial)
    $stmt = $conn->query("SELECT * FROM tb_error_logs ORDER BY fecha DESC LIMIT 20");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Contar errores por tipo para estadísticas
    $stmt = $conn->query("
        SELECT tipo, COUNT(*) as total
        FROM tb_error_logs
        GROUP BY tipo
        ORDER BY total DESC
    ");
    $errorStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Verificar si hay archivos XML faltantes en la base de datos
    $xmlFaltantes = [];
    try {
        $stmt = $conn->query("
            SELECT id, tipo_dte, nombre_archivo
            FROM tb_facturas_dte
            WHERE estado_sobre = 0
            LIMIT 10
        ");
        $pendientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($pendientes as $doc) {
            if (!file_exists($doc['nombre_archivo'])) {
                $xmlFaltantes[] = [
                    'id' => $doc['id'],
                    'tipo_dte' => $doc['tipo_dte'],
                    'ruta' => $doc['nombre_archivo'],
                    'existe' => 'no'
                ];
            }
        }
    } catch (Exception $e) {
        // Si hay error, continuamos
    }

    // Verificar el estado de los directorios críticos
    $directorios = [
        'Documents/sobreEnvio/' => verificarDirectorio('Documents/sobreEnvio/'),
        'Documents/folios/' => verificarDirectorio('Documents/folios/'),
        'Documents/' => verificarDirectorio('Documents/')
    ];

    echo json_encode([
        'success' => true,
        'logs' => $logs,
        'error_stats' => $errorStats,
        'xml_faltantes' => $xmlFaltantes,
        'directorios' => $directorios,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

// Función para verificar el estado de un directorio
function verificarDirectorio($directorio) {
    return [
        'existe' => file_exists($directorio) ? 'sí' : 'no',
        'permisos' => file_exists($directorio) ? decoct(fileperms($directorio) & 0777) : 'N/A',
        'is_readable' => is_readable($directorio) ? 'sí' : 'no',
        'is_writable' => is_writable($directorio) ? 'sí' : 'no'
    ];
}

} catch (Exception $e) {
    echo json_encode([
        'error' => 'Error al obtener logs: ' . $e->getMessage(),
        'details' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
