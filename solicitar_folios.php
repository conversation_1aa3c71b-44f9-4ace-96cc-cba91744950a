<?php
// Archivo: solicitar_folios.php
// Propósito: Solicitar folios CAF a SimpleAPI y guardar el archivo XML resultante

header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener parámetros del POST, con valores por defecto
$tipoDTE = $_POST['tipoDTE'] ?? 33; // Por defecto, solicitar folios para facturas (33)
$cantidad = $_POST['cantidad'] ?? 19; // Cantidad de folios a solicitar

// Configuración
$apiKey = '2037-N680-6391-2493-5987';
$rutCertificado = '17365958-K';
$password = '1569';
$rutEmpresa = '78078979-4';
$ambiente = 1; // 1: Certificación, 0: Producción

// Ruta al certificado
$certificadoPath = 'Documents/17365958-K.pfx';
if (!file_exists($certificadoPath)) {
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

// Crear directorio de destino si no existe
$foliosDir = 'Documents/folios/Facturas';
if (!is_dir($foliosDir)) {
    if (!mkdir($foliosDir, 0755, true)) {
        echo json_encode(['error' => 'No se pudo crear el directorio para guardar los folios']);
        exit;
    }
}

// Preparar datos para la solicitud
$input = json_encode([
    'RutCertificado' => $rutCertificado,
    'Password' => $password,
    'RutEmpresa' => $rutEmpresa,
    'Ambiente' => $ambiente
]);

// Crear el nombre del archivo de destino
$fechaActual = date('Ymd_His');
$nombreArchivo = "folios_{$tipoDTE}_{$fechaActual}.xml";
$rutaCompleta = "{$foliosDir}/{$nombreArchivo}";

// Inicializar cURL
$curl = curl_init();

// Preparar el archivo para la solicitud
$cfile = new CURLFile($certificadoPath, 'application/x-pkcs12', basename($certificadoPath));

// Configurar cURL
curl_setopt_array($curl, [
    CURLOPT_URL => "https://servicios.simpleapi.cl/api/folios/get/{$tipoDTE}/{$cantidad}",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => [
        'input' => $input,
        'files' => $cfile
    ],
    CURLOPT_HTTPHEADER => [
        'Authorization: ' . $apiKey
    ],
]);

// Registrar inicio de la solicitud
$tiempoInicio = microtime(true);

// Ejecutar la solicitud
$response = curl_exec($curl);
$err = curl_error($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$tiempoTotal = round((microtime(true) - $tiempoInicio) * 1000); // Tiempo en milisegundos

curl_close($curl);

// Verificar si hubo errores
if ($err) {
    echo json_encode(['error' => 'Error en la solicitud cURL: ' . $err]);
    exit;
}

// Verificar el código de respuesta HTTP
if ($httpCode != 200) {
    echo json_encode([
        'error' => 'Error en la respuesta del servidor: Código ' . $httpCode,
        'response' => $response
    ]);
    exit;
}

// Guardar la respuesta en un archivo XML
if (file_put_contents($rutaCompleta, $response) === false) {
    echo json_encode(['error' => 'No se pudo guardar el archivo XML']);
    exit;
}

// Extraer el rango de folios del XML
$xml = simplexml_load_string($response);
if ($xml === false) {
    echo json_encode(['error' => 'No se pudo analizar la respuesta XML']);
    exit;
}

// Extraer los valores de rango inicial y final
$rangoInicial = null;
$rangoFinal = null;

if (isset($xml->CAF->DA->RNG->D)) {
    $rangoInicial = (int)$xml->CAF->DA->RNG->D;
}

if (isset($xml->CAF->DA->RNG->H)) {
    $rangoFinal = (int)$xml->CAF->DA->RNG->H;
}

if ($rangoInicial === null || $rangoFinal === null) {
    echo json_encode([
        'error' => 'No se pudieron extraer los rangos de folios del XML',
        'xml_content' => $response
    ]);
    exit;
}

// Registrar en la base de datos
require_once 'db_connection.php';

try {
    $conn = getConnection();

    $stmt = $conn->prepare("
        INSERT INTO folios_caf (
            tipo_documento,
            rango_inicial,
            rango_final,
            siguiente_folio,
            ruta_archivo,
            activo,
            created_at
        ) VALUES (
            :tipo_documento,
            :rango_inicial,
            :rango_final,
            :siguiente_folio,
            :ruta_archivo,
            1,
            NOW()
        )
    ");

    $stmt->bindParam(':tipo_documento', $tipoDTE);
    $stmt->bindParam(':rango_inicial', $rangoInicial);
    $stmt->bindParam(':rango_final', $rangoFinal);
    $stmt->bindParam(':siguiente_folio', $rangoInicial); // siguiente_folio = rango_inicial
    $stmt->bindParam(':ruta_archivo', $rutaCompleta);

    $stmt->execute();
    $folioId = $conn->lastInsertId();

    // Preparar logs detallados para el cliente
    $logs = [
        [
            'type' => 'info',
            'message' => 'Conexión con SimpleAPI establecida',
            'data' => [
                'tiempo_respuesta' => $tiempoTotal . ' ms',
                'codigo_http' => $httpCode
            ]
        ],
        [
            'type' => 'info',
            'message' => 'Solicitud de folios procesada por el servidor',
            'data' => [
                'tipo_dte' => $tipoDTE,
                'cantidad' => $cantidad,
                'ambiente' => $ambiente == 1 ? 'Certificación' : 'Producción',
                'fecha_hora' => date('Y-m-d H:i:s')
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Archivo XML recibido y guardado',
            'data' => [
                'ruta' => $rutaCompleta,
                'nombre_archivo' => $nombreArchivo,
                'tamano' => filesize($rutaCompleta) . ' bytes'
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Rango de folios obtenido',
            'data' => [
                'rango_inicial' => $rangoInicial,
                'rango_final' => $rangoFinal,
                'total_folios' => ($rangoFinal - $rangoInicial + 1)
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Registro en base de datos completado',
            'data' => [
                'folio_id' => $folioId,
                'tipo_documento' => $tipoDTE,
                'tabla' => 'folios_caf'
            ]
        ]
    ];

    // Devolver respuesta exitosa con logs detallados
    echo json_encode([
        'success' => true,
        'message' => 'Folios solicitados y registrados correctamente',
        'folio_id' => $folioId,
        'rango_inicial' => $rangoInicial,
        'rango_final' => $rangoFinal,
        'ruta_archivo' => $rutaCompleta,
        'archivo_xml' => $nombreArchivo,
        'logs' => $logs
    ]);

} catch (PDOException $e) {
    echo json_encode(['error' => 'Error al registrar en la base de datos: ' . $e->getMessage()]);
    exit;
}
?>
