<?php
// Script para probar la funcionalidad de subida de imágenes

// Mostrar todos los errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar extensión GD
echo "<h2>Verificación de la extensión GD</h2>";
if (extension_loaded('gd')) {
    echo "<p style='color:green'>✓ La extensión GD está habilitada</p>";
    
    // Mostrar información de la versión de GD
    $gdInfo = gd_info();
    echo "<pre>";
    print_r($gdInfo);
    echo "</pre>";
} else {
    echo "<p style='color:red'>✗ La extensión GD NO está habilitada. Por favor, habilítela en php.ini</p>";
}

// Verificar directorio de imágenes
echo "<h2>Verificación del directorio de imágenes</h2>";
$uploadDir = 'images/fotos_repuestos/';
$absoluteUploadDir = __DIR__ . '/' . $uploadDir;

echo "<p>Directorio a verificar: <code>$absoluteUploadDir</code></p>";

// Verificar si el directorio existe
if (file_exists($absoluteUploadDir)) {
    echo "<p style='color:green'>✓ El directorio existe</p>";
    
    // Verificar permisos
    if (is_writable($absoluteUploadDir)) {
        echo "<p style='color:green'>✓ El directorio tiene permisos de escritura</p>";
        echo "<p>Permisos actuales: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</p>";
    } else {
        echo "<p style='color:red'>✗ El directorio NO tiene permisos de escritura</p>";
        echo "<p>Permisos actuales: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</p>";
        
        // Intentar cambiar permisos
        echo "<p>Intentando cambiar permisos a 0777...</p>";
        if (chmod($absoluteUploadDir, 0777)) {
            echo "<p style='color:green'>✓ Permisos cambiados correctamente</p>";
            echo "<p>Nuevos permisos: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</p>";
        } else {
            echo "<p style='color:red'>✗ No se pudieron cambiar los permisos</p>";
        }
    }
} else {
    echo "<p style='color:red'>✗ El directorio NO existe</p>";
    
    // Intentar crear el directorio
    echo "<p>Intentando crear el directorio...</p>";
    if (mkdir($absoluteUploadDir, 0777, true)) {
        echo "<p style='color:green'>✓ Directorio creado correctamente</p>";
        
        // Verificar permisos después de crear
        if (is_writable($absoluteUploadDir)) {
            echo "<p style='color:green'>✓ El directorio tiene permisos de escritura</p>";
            echo "<p>Permisos: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</p>";
        } else {
            echo "<p style='color:red'>✗ El directorio NO tiene permisos de escritura después de crearlo</p>";
            echo "<p>Permisos: " . decoct(fileperms($absoluteUploadDir) & 0777) . "</p>";
        }
    } else {
        echo "<p style='color:red'>✗ No se pudo crear el directorio</p>";
    }
}

// Formulario de prueba para subir una imagen
echo "<h2>Prueba de subida de imagen</h2>";
?>

<form action="test_image_upload_process.php" method="post" enctype="multipart/form-data">
    <div>
        <label for="imagen">Selecciona una imagen:</label>
        <input type="file" name="imagen" id="imagen" accept="image/*" required>
    </div>
    <div style="margin-top: 10px;">
        <button type="submit">Subir imagen</button>
    </div>
</form>

<?php
// Mostrar información del sistema
echo "<h2>Información del sistema</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Current Script: " . __FILE__ . "</p>";

// Mostrar límites de PHP relevantes para la subida de archivos
echo "<h2>Límites de PHP para subida de archivos</h2>";
echo "<ul>";
echo "<li>upload_max_filesize: " . ini_get('upload_max_filesize') . "</li>";
echo "<li>post_max_size: " . ini_get('post_max_size') . "</li>";
echo "<li>memory_limit: " . ini_get('memory_limit') . "</li>";
echo "<li>max_execution_time: " . ini_get('max_execution_time') . " segundos</li>";
echo "<li>max_file_uploads: " . ini_get('max_file_uploads') . "</li>";
echo "</ul>";
?>
