// Variables globales para el canvas de cotización
let quoteItems = [];
let currentQuoteId = 0;
let currentQuoteLevel = 1; // 1: formulario, 2: lista, 3: detalle

// Elementos del DOM
const quoteCanvas = document.getElementById('quoteCanvas');
const quoteOverlay = document.getElementById('quoteOverlay');
const openQuoteBtn = document.getElementById('openQuoteBtn');
const closeQuoteBtn = document.getElementById('closeQuoteBtn');
const quoteItemsContainer = document.getElementById('quoteItemsContainer');
const quoteTotals = document.getElementById('quoteTotals');
const slideInner = document.querySelector('.quote-slide-inner');
const backQuoteBtn = document.getElementById('backQuoteBtn');
const quoteTitle = document.getElementById('quoteTitle');

// Manejo de apertura y cierre del canvas
if (openQuoteBtn) {
    openQuoteBtn.addEventListener('click', () => {
        quoteCanvas.classList.add('active');
        quoteOverlay.classList.add('active');
    });
}

if (closeQuoteBtn) {
    closeQuoteBtn.addEventListener('click', () => {
        closeQuoteCanvas();
    });
}

if (quoteOverlay) {
    quoteOverlay.addEventListener('click', () => {
        closeQuoteCanvas();
    });
}

// Botón de volver
if (backQuoteBtn) {
    backQuoteBtn.addEventListener('click', () => {
        navigateToLevel(currentQuoteLevel - 1);
    });
}

// Función para cerrar el canvas
function closeQuoteCanvas() {
    quoteCanvas.classList.remove('active');
    quoteOverlay.classList.remove('active');
    // Volver al primer nivel cuando se cierra
    setTimeout(() => {
        navigateToLevel(1);
    }, 300);
}

// Función para navegar entre niveles
function navigateToLevel(level) {
    currentQuoteLevel = level;
    
    switch(level) {
        case 1:
            slideInner.classList.remove('show-list', 'show-detail');
            backQuoteBtn.style.display = 'none';
            quoteTitle.textContent = 'Generar Cotización';
            break;
        case 2:
            slideInner.classList.add('show-list');
            slideInner.classList.remove('show-detail');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.textContent = 'Lista de Cotizaciones';
            break;
        case 3:
            slideInner.classList.add('show-detail');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.textContent = 'Detalle de Cotización';
            break;
    }
}

// Manejo de tabs
const tabButtons = document.querySelectorAll('.tab-button');
const tabContents = document.querySelectorAll('.tab-content');

tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        const tab = button.getAttribute('data-tab');
        
        // Remover clase active de todos los botones y contenidos
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Agregar clase active al botón y contenido seleccionado
        button.classList.add('active');
        document.getElementById(`${tab}-tab`).classList.add('active');
    });
});

// Función para agregar producto manualmente
function addManualProduct() {
    const name = document.getElementById('manualProductName').value.trim();
    const price = parseFloat(document.getElementById('manualProductPrice').value) || 0;
    const quantity = parseInt(document.getElementById('manualProductQuantity').value) || 1;
    
    if (!name || price <= 0) {
        alert('Por favor ingrese un nombre y precio válido para el producto.');
        return;
    }
    
    const product = {
        id: `manual_${Date.now()}`,
        name: name,
        description: '',
        price: price,
        quantity: quantity,
        isManual: true
    };
    
    addProductToQuote(product);
    
    // Limpiar campos
    document.getElementById('manualProductName').value = '';
    document.getElementById('manualProductPrice').value = '';
    document.getElementById('manualProductQuantity').value = '1';
}

// Función para agregar producto a la cotización
function addProductToQuote(product) {
    // Verificar si el producto ya existe
    const existingIndex = quoteItems.findIndex(item => item.id === product.id);
    
    if (existingIndex !== -1) {
        // Si existe, aumentar cantidad
        quoteItems[existingIndex].quantity += product.quantity;
    } else {
        // Si no existe, agregarlo
        quoteItems.push(product);
    }
    
    updateQuoteDisplay();
    updateQuoteCounter();
}

// Función para actualizar la visualización de la cotización
function updateQuoteDisplay() {
    if (quoteItems.length === 0) {
        quoteItemsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <p>No hay productos agregados a la cotización</p>
            </div>
        `;
        quoteTotals.style.display = 'none';
    } else {
        let itemsHTML = '';
        let subtotal = 0;
        
        quoteItems.forEach((item, index) => {
            const itemTotal = item.price * item.quantity;
            subtotal += itemTotal;
            
            itemsHTML += `
                <div class="quote-item" data-index="${index}">
                    <div class="quote-item-info">
                        <div class="quote-item-name">${item.name}</div>
                        <div class="quote-item-price">$${item.price.toLocaleString('es-CL')} x ${item.quantity} = $${itemTotal.toLocaleString('es-CL')}</div>
                    </div>
                    <div class="quote-item-actions">
                        <div class="quote-item-quantity">
                            <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, -1)">-</button>
                            <span>${item.quantity}</span>
                            <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, 1)">+</button>
                        </div>
                        <button class="remove-item-btn" onclick="removeQuoteItem(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        quoteItemsContainer.innerHTML = itemsHTML;
        
        // Calcular totales (sin IVA)
        const total = subtotal; // Total igual al subtotal
        
        document.getElementById('quoteSubtotal').textContent = `$${subtotal.toLocaleString('es-CL')}`;
        document.getElementById('quoteTotal').textContent = `$${Math.round(total).toLocaleString('es-CL')}`;
        
        quoteTotals.style.display = 'block';
    }
}

// Función para actualizar cantidad de un item
function updateQuoteItemQuantity(index, change) {
    if (quoteItems[index]) {
        quoteItems[index].quantity += change;
        
        if (quoteItems[index].quantity <= 0) {
            quoteItems.splice(index, 1);
        }
        
        updateQuoteDisplay();
        updateQuoteCounter();
    }
}

// Función para eliminar un item
function removeQuoteItem(index) {
    if (confirm('¿Está seguro de eliminar este producto de la cotización?')) {
        quoteItems.splice(index, 1);
        updateQuoteDisplay();
        updateQuoteCounter();
    }
}

// Función para actualizar el contador del botón
function updateQuoteCounter() {
    const counter = document.querySelector('.quote-count');
    if (counter) {
        const totalItems = quoteItems.reduce((sum, item) => sum + item.quantity, 0);
        counter.textContent = totalItems.toString();
        
        // Animar el contador
        counter.classList.add('updated');
        setTimeout(() => counter.classList.remove('updated'), 300);
    }
}

// Función para limpiar la cotización
function clearQuote() {
    if (quoteItems.length > 0 && confirm('¿Está seguro de limpiar toda la cotización?')) {
        quoteItems = [];
        updateQuoteDisplay();
        updateQuoteCounter();
        
        // Limpiar campos del cliente
        document.getElementById('quoteClientName').value = '';
        document.getElementById('quoteClientRut').value = '';
        document.getElementById('quoteClientEmail').value = '';
        document.getElementById('quoteClientPhone').value = '';
        document.getElementById('quoteNotes').value = '';
    }
}

// Función para generar la cotización
async function generateQuote() {
    var originalText;
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización.');
        return;
    }
    
    // Validar información del cliente
    const clientName = document.getElementById('quoteClientName').value.trim();
    const clientRut = document.getElementById('quoteClientRut').value.trim();
    
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente.');
        return;
    }
    
    // Preparar datos para enviar
    const quoteData = {
        client: {
            name: clientName,
            rut: clientRut,
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        date: new Date().toISOString(),
        timezone: 'America/Santiago'
    };
    
    try {
        // Mostrar indicador de carga
        const generateBtn = document.querySelector('.action-button.primary');
        originalText = generateBtn.innerHTML;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generando...';
        generateBtn.disabled = true;
        
        // Usar el archivo HTML existente que no requiere dependencias externas
        const response = await fetch('generate_quote_html.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(quoteData),
            credentials: 'include'
        });
        
        if (response.ok) {
            const html = await response.text();
            
            // Verificar si la respuesta contiene un error HTML
            if (html.includes('Error 401') || html.includes('No autorizado')) {
                throw new Error('Sesión expirada. Por favor, recargue la página e inicie sesión nuevamente.');
            }
            
            // Abrir en nueva ventana para previsualizar/imprimir
            const printWindow = window.open('', '_blank', 'width=600,height=800,scrollbars=yes');
            printWindow.document.write(html);
            printWindow.document.close();
            // No llamar a print() automáticamente para permitir previsualización
            
            // Mostrar notificación de éxito
            showNotification('Cotización generada exitosamente', 'success');
        } else {
            if (response.status === 401) {
                throw new Error('Sesión expirada. Por favor, recargue la página e inicie sesión nuevamente.');
            }
            throw new Error(`Error al generar la cotización (${response.status})`);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error al generar la cotización. Por favor, intente nuevamente.');
    } finally {
        // Restaurar botón siempre
        const generateBtn = document.querySelector('.action-button.primary');
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    }
}

// Función auxiliar para mostrar notificaciones
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = 'quote-notification';
    notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i> ${message}`;
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#dc3545'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Búsqueda de productos desde inventario
const searchInput = document.getElementById('quoteProductSearch');
const searchResults = document.getElementById('quoteSearchResults');

if (searchInput) {
    let searchTimeout;
    
    searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        const searchTerm = e.target.value.trim();
        
        if (searchTerm.length < 2) {
            searchResults.innerHTML = '';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            searchProducts(searchTerm);
        }, 300);
    });
}

// Función para buscar productos
async function searchProducts(term) {
    console.log('searchProducts function called with term:', term);
    try {
        const response = await fetch(`search_products.php?term=${encodeURIComponent(term)}`, {
            credentials: 'include', // Incluir cookies para mantener la sesión
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // Verificar que la respuesta sea JSON válido
        const text = await response.text();
        let data;
        
        try {
            data = JSON.parse(text);
        } catch (parseError) {
            console.error('Error parsing JSON:', parseError);
            console.error('Response text:', text);
            throw new Error('Respuesta no válida del servidor');
        }
        
        // Manejar la nueva estructura de respuesta
        if (data.status === 'error') {
            console.error('Error del servidor:', data.message);
            searchResults.innerHTML = `<div class="search-result-item">Error: ${data.message}</div>`;
            return;
        }
        
        // Verificar que tengamos datos válidos
        const products = (data.status === 'success' && Array.isArray(data.data)) ? data.data : [];
        
        if (products.length === 0) {
            searchResults.innerHTML = '<div class="search-result-item">No se encontraron productos</div>';
            return;
        }
        
        let resultsHTML = '';
        products.forEach(product => {
            resultsHTML += `
                <div class="search-result-item" onclick="addProductFromSearch(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1; padding-right: 10px;">
                            <strong>${product.nombre}</strong>
                            ${product.sku ? `<span style="color: #666; font-size: 0.85rem;"> - SKU: ${product.sku}</span>` : ''}
                            ${product.descripcion ? `<div style="color: #666; font-size: 0.85rem;">${product.descripcion}</div>` : ''}
                        </div>
                        <div style="text-align: right; min-width: 100px;">
                            <div style="color: #007bff; font-weight: bold;">$${product.precio_venta.toLocaleString('es-CL')}</div>
                            <div style="color: #666; font-size: 0.85rem;">Stock: ${product.stock_actual || 0}</div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        searchResults.innerHTML = resultsHTML;
    } catch (error) {
        console.error('Error al buscar productos:', error);
        searchResults.innerHTML = '<div class="search-result-item">Error al buscar productos. Por favor, intente nuevamente.</div>';
    }
}

// Función para agregar producto desde búsqueda
function addProductFromSearch(product) {
    const quoteProduct = {
        id: product.id.toString(),
        name: product.nombre,
        description: product.descripcion || '',
        price: product.precio_venta,
        quantity: 1,
        isManual: false,
        sku: product.sku,
        stock: product.stock_actual
    };
    
    addProductToQuote(quoteProduct);
    
    // Limpiar búsqueda
    document.getElementById('quoteProductSearch').value = '';
    searchResults.innerHTML = '';
}

// Función para agregar producto a cotización desde la tabla principal
function addToQuote(productId, productName, productPrice) {
    // Crear objeto producto
    const product = {
        id: productId.toString(),
        name: productName,
        description: '',
        price: parseFloat(productPrice),
        quantity: 1,
        isManual: false
    };
    
    // Agregar a la cotización
    addProductToQuote(product);
    
    // Mostrar notificación
    const notification = document.createElement('div');
    notification.className = 'quote-notification';
    notification.innerHTML = `<i class="fas fa-check-circle"></i> Producto agregado a la cotización`;
    notification.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 2000);
}

// Implementar búsqueda en la lista de cotizaciones
const quoteSearchInput = document.getElementById('quoteSearchInput');
if (quoteSearchInput) {
    let searchTimeout;
    quoteSearchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        const searchTerm = e.target.value.toLowerCase();
        
        searchTimeout = setTimeout(() => {
            const cards = document.querySelectorAll('.quote-card');
            cards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }, 300);
    });
}

// Inicializar al cargar la página
// Función para guardar la cotización en la base de datos
async function saveQuote() {
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización.');
        return;
    }
    
    // Validar información del cliente
    const clientName = document.getElementById('quoteClientName').value.trim();
    
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente.');
        return;
    }
    
    // Preparar datos para enviar
    const quoteData = {
        client: {
            name: clientName,
            rut: document.getElementById('quoteClientRut').value.trim(),
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        date: new Date().toISOString(),
        timezone: 'America/Santiago'
    };
    
    try {
        // Mostrar indicador de carga
        const saveBtn = document.querySelector('.action-button.save');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveBtn.disabled = true;
        
        // Enviar datos al servidor
        const response = await fetch('guardar_cotizacion.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(quoteData)
        });
        
        // Restaurar botón
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        
        const result = await response.json();
        
        if (response.ok && result.status === 'success') {
            // Mostrar notificación de éxito
            showNotification(`Cotización #${result.numero_cotizacion} guardada exitosamente`, 'success');
        } else {
            throw new Error(result.message || 'Error al guardar la cotización');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error al guardar la cotización. Por favor, intente nuevamente.');
        
        // Restaurar botón
        const saveBtn = document.querySelector('.action-button.save');
        saveBtn.innerHTML = '<i class="fas fa-save"></i> Guardar';
        saveBtn.disabled = false;
    }
}

// Función para abrir la lista de cotizaciones
function openQuotesList() {
    navigateToLevel(2);
    loadQuotesList();
}

// Función para cargar la lista de cotizaciones
async function loadQuotesList() {
    console.log('loadQuotesList() se está ejecutando');
    try {
        const response = await fetch('list_quotes.php', { credentials: 'include' });
        const data = await response.json();
        
        if (data.status === 'success') {
            renderQuotesList(data.quotes || []);
        } else {
            console.error('Error:', data.message);
            const container = document.getElementById('quotesListContainer');
            container.innerHTML = '<p style="text-align: center; color: #666;">No se pudieron cargar las cotizaciones</p>';
        }
    } catch (error) {
        console.error('Error al cargar cotizaciones:', error);
        const container = document.getElementById('quotesListContainer');
        container.innerHTML = '<p style="text-align: center; color: #666;">Error al cargar las cotizaciones</p>';
    }
}

// Función para renderizar la lista de cotizaciones
function renderQuotesList(quotes) {
    const container = document.getElementById('quotesListContainer');
    
    if (!quotes || quotes.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">No hay cotizaciones guardadas</p>';
        return;
    }
    
    let html = '';
    quotes.forEach(quote => {
        html += `
            <div class="quote-card" onclick="showQuoteDetail(${quote.id})">
                <div class="quote-header-row">
                    <span>${quote.numero}</span>
                    <span>${quote.fecha}</span>
                </div>
                <div class="quote-client">${quote.cliente_nombre || 'Cliente sin nombre'}</div>
                <div class="quote-total">$${Number(quote.total).toLocaleString('es-CL')}</div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Función para mostrar el detalle de una cotización
async function showQuoteDetail(id) {
    try {
        console.log(`[DEBUG] Solicitando detalle de cotización para ID: ${id}`);
        const url = `get_quote_detail.php?id=${id}`;
        console.log(`[DEBUG] URL de solicitud: ${url}`);

        const response = await fetch(url, {
            credentials: 'include' // Asegurar que se envíen las cookies de sesión
        });

        console.log(`[DEBUG] Respuesta recibida - Status: ${response.status}, StatusText: ${response.statusText}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[ERROR] Error en la respuesta del servidor (${response.status}): ${errorText}`);
            alert(`Error al cargar el detalle de la cotización. Código: ${response.status}. Mensaje: ${errorText.substring(0, 100)}...`); // Show part of the error text
            return; // Stop execution if response is not OK
        }

        const data = await response.json();
        console.log('[DEBUG] Datos de respuesta JSON:', data);

        if (data.status === 'success') {
            renderQuoteDetail(data.quote);
            navigateToLevel(3);
            console.log('[DEBUG] Detalle de cotización renderizado y nivel de navegación actualizado.');
        } else {
            console.warn('[WARN] La carga del detalle de la cotización no fue exitosa:', data.message || 'Mensaje no proporcionado');
            alert('No se pudo cargar el detalle de la cotización: ' + (data.message || 'Error desconocido'));
        }
    } catch (error) {
        console.error('[CRITICAL ERROR] Excepción al cargar el detalle de la cotización:', error);
        alert('Error crítico al cargar el detalle de la cotización. Revise la consola para más detalles.');
    }
}

// Función para renderizar el detalle de la cotización
function renderQuoteDetail(quote) {
    const container = document.getElementById('quoteDetailContent');
    
    let itemsHtml = '';
    if (quote.items && quote.items.length > 0) {
        quote.items.forEach(item => {
            const subtotal = item.cantidad * item.precio_unitario;
            itemsHtml += `
                <li>
                    <div>
                        <strong>${item.cantidad}</strong> × ${item.nombre}
                        <br>
                        <small style="color: #666;">Precio unitario: $${Number(item.precio_unitario).toLocaleString('es-CL')}</small>
                    </div>
                    <div style="text-align: right;">
                        <strong>$${Number(subtotal).toLocaleString('es-CL')}</strong>
                    </div>
                </li>
            `;
        });
    }
    
    container.innerHTML = `
        <div class="quote-detail">
            <h3>Información de la Cotización</h3>
            <p><strong>Número:</strong> ${quote.numero}</p>
            <p><strong>Fecha:</strong> ${quote.fecha}</p>
            <p><strong>Usuario:</strong> ${quote.usuario || 'No especificado'}</p>
            
            <h3>Información del Cliente</h3>
            <p><strong>Nombre:</strong> ${quote.cliente_nombre || 'No especificado'}</p>
            ${quote.cliente_rut ? `<p><strong>RUT:</strong> ${quote.cliente_rut}</p>` : ''}
            ${quote.cliente_email ? `<p><strong>Email:</strong> ${quote.cliente_email}</p>` : ''}
            ${quote.cliente_telefono ? `<p><strong>Teléfono:</strong> ${quote.cliente_telefono}</p>` : ''}
            
            <h3>Productos</h3>
            <ul class="quote-items">
                ${itemsHtml}
            </ul>
            
            <h3>Totales</h3>
            <p><strong>Subtotal:</strong> $${Number(quote.subtotal).toLocaleString('es-CL')}</p>
            <p style="font-size: 1.2rem; color: #4e73df;"><strong>Total:</strong> $${Number(quote.total).toLocaleString('es-CL')}</p>
            
            ${quote.notas ? `
                <h3>Notas</h3>
                <p>${quote.notas}</p>
            ` : ''}
        </div>
    `;
}

document.addEventListener('DOMContentLoaded', () => {
    updateQuoteDisplay();
    updateQuoteCounter();
});