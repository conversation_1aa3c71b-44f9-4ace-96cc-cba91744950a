<?php
session_start();
require_once 'db_connection.php';

// Configurar headers para respuesta JSON
header('Content-Type: application/json');

// Verificar si el usuario está autenticado
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

// Verificar si existe el ID del usuario en la sesión
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'ID de usuario no encontrado']);
    exit;
}

$usuario_id = $_SESSION['user_id'];

try {
    // Primero verificar si la tabla de notificaciones existe
    $conn = getConnection();
    
    // Verificar si la tabla notificaciones existe
    $checkTable = "SHOW TABLES LIKE 'notificaciones'";
    $tableExists = $conn->query($checkTable)->fetch();
    
    if (!$tableExists) {
        // Si la tabla no existe, devolver array vacío
        echo json_encode([
            'success' => true,
            'notificaciones' => [],
            'total_no_leidas' => 0,
            'mensaje' => 'Sistema de notificaciones no configurado'
        ]);
        exit;
    }
    
    // Obtener notificaciones no leídas del usuario
    $query = "
        SELECT 
            n.id,
            n.tipo,
            n.mensaje,
            n.fecha_creacion,
            n.leida,
            n.url_referencia
        FROM notificaciones n
        WHERE n.usuario_id = :usuario_id 
        AND n.leida = 0
        ORDER BY n.fecha_creacion DESC
        LIMIT 50
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute(['usuario_id' => $usuario_id]);
    $notificaciones = [];
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $notificaciones[] = [
            'id' => $row['id'],
            'tipo' => $row['tipo'],
            'mensaje' => $row['mensaje'],
            'fecha' => $row['fecha_creacion'],
            'leida' => $row['leida'],
            'url' => $row['url_referencia']
        ];
    }
    
    // Contar total de notificaciones no leídas
    $count_query = "SELECT COUNT(*) as total FROM notificaciones WHERE usuario_id = :usuario_id AND leida = 0";
    $count_stmt = $conn->prepare($count_query);
    $count_stmt->execute(['usuario_id' => $usuario_id]);
    $count_row = $count_stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'notificaciones' => $notificaciones,
        'total_no_leidas' => $count_row['total']
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => true,
        'notificaciones' => [],
        'total_no_leidas' => 0,
        'mensaje' => 'Sistema de notificaciones no disponible'
    ]);
}
?>