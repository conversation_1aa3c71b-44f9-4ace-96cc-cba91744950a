<?php
require_once 'db_connection.php';

// Función para registrar mensajes de depuración
function debugLog($message) {
    error_log("[eliminar_upgrade.php] " . $message);
}

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Verificar si se proporcionó un ID
if (!isset($_POST['id']) || empty($_POST['id'])) {
    debugLog("Error: ID no proporcionado");
    echo json_encode(['status' => 'error', 'message' => 'ID no proporcionado']);
    exit;
}

$id = intval($_POST['id']);
debugLog("Procesando eliminación del registro con ID: " . $id);

try {
    $conn = getConnection();
    debugLog("Conexión a la base de datos establecida");

    // Primero, obtener información del registro para eliminar el archivo si existe
    $stmt = $conn->prepare("SELECT ruta_archivo FROM tb_upgrades WHERE id = ?");
    $stmt->execute([$id]);
    $registro = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$registro) {
        debugLog("Error: Registro con ID " . $id . " no encontrado");
        echo json_encode(['status' => 'error', 'message' => 'Registro no encontrado']);
        exit;
    }

    debugLog("Registro encontrado: " . print_r($registro, true));

    // Si hay un archivo asociado, eliminarlo
    if (!empty($registro['ruta_archivo'])) {
        $rutaArchivo = __DIR__ . '/' . $registro['ruta_archivo'];
        debugLog("Intentando eliminar archivo: " . $rutaArchivo);

        if (file_exists($rutaArchivo)) {
            debugLog("El archivo existe, procediendo a eliminarlo");
            if (unlink($rutaArchivo)) {
                $fileDeleted = true;
                debugLog("Archivo eliminado correctamente");
            } else {
                $fileDeleted = false;
                debugLog("Error: No se pudo eliminar el archivo, pero se continuará con la eliminación del registro");
                // Continuar con la eliminación del registro aunque el archivo no se pueda eliminar
            }
        } else {
            debugLog("El archivo no existe en la ruta especificada");
        }
    } else {
        debugLog("No hay archivo asociado a este registro");
    }

    // Eliminar el registro de la base de datos
    $stmt = $conn->prepare("DELETE FROM tb_upgrades WHERE id = ?");
    debugLog("Ejecutando consulta DELETE para el registro con ID: " . $id);
    $result = $stmt->execute([$id]);

    if ($result) {
        debugLog("Registro eliminado correctamente de la base de datos");
        $response = [
            'status' => 'success',
            'message' => 'Registro eliminado correctamente'
        ];

        if (isset($fileDeleted)) {
            $response['file_deleted'] = $fileDeleted;
            if (!$fileDeleted) {
                $response['file_message'] = 'No se pudo eliminar el archivo asociado, pero el registro fue eliminado de la base de datos';
            }
        }

        debugLog("Enviando respuesta de éxito: " . json_encode($response));
        echo json_encode($response);
    } else {
        $errorInfo = $stmt->errorInfo();
        debugLog("Error al eliminar el registro: " . print_r($errorInfo, true));
        echo json_encode(['status' => 'error', 'message' => 'Error al eliminar el registro: ' . $errorInfo[2]]);
    }
} catch (Exception $e) {
    debugLog("Excepción capturada: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'Error: ' . $e->getMessage()]);
}
