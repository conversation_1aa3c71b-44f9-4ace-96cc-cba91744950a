<?php
require_once 'db_connection.php';

header('Content-Type: application/json');

// Validar que se recibió un tipo de documento
if (!isset($_GET['tipo_documento']) || empty($_GET['tipo_documento'])) {
    echo json_encode(['error' => 'Tipo de documento no especificado']);
    exit;
}

$tipo_documento = intval($_GET['tipo_documento']);

try {
    $conn = getConnection();
    
    // Consultar el siguiente folio disponible
    $stmt = $conn->prepare("
        SELECT siguiente_folio, ruta_archivo 
        FROM folios_caf 
        WHERE tipo_documento = :tipo_documento 
        AND activo = true 
        AND siguiente_folio <= rango_final
        ORDER BY rango_inicial 
        LIMIT 1
    ");
    
    $stmt->bindParam(':tipo_documento', $tipo_documento, PDO::PARAM_INT);
    $stmt->execute();
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'folio' => $result['siguiente_folio'],
            'ruta_archivo' => $result['ruta_archivo']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No se encontró un folio disponible para este tipo de documento'
        ]);
    }
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
