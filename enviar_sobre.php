<?php
// Archivo: enviar_sobre.php
header('Content-Type: application/json');

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener parámetros del POST, con valores por defecto
$apiKey = $_POST['apiKey'] ?? '2037-N680-6391-2493-5987';
$certificadoPassword = $_POST['certificadoPassword'] ?? '1569';
$rutCertificado = $_POST['rutCertificado'] ?? '17365958-K';
$ambiente = $_POST['ambiente'] ?? 1; // 0: Producción, 1: Certificación

// Ruta al certificado
$certificadoPath = 'Documents/17365958-K.pfx';
if (!file_exists($certificadoPath)) {
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

// Obtener sobre pendiente de la base de datos
require_once 'db_connection.php';
try {
    $conn = getConnection();

    // Consultar sobres pendientes de envío
    $stmt = $conn->prepare("
        SELECT id, nombre_archivo, tipoEnvio, ruta_archivo
        FROM tb_sobre_envios
        WHERE  estado_envio = 0
        LIMIT 1
    ");
    $stmt->execute();
    $sobre = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$sobre) {
        echo json_encode(['error' => 'No hay sobres pendientes de envío']);
        exit;
    }

    // Verificar que el archivo existe
    $sobrePath = $sobre['ruta_archivo'] ?? $sobre['nombre_archivo'];
    if (!file_exists($sobrePath)) {
        echo json_encode(['error' => 'Archivo de sobre no encontrado: ' . $sobrePath]);

        // Actualizar el estado del sobre a error
        $stmt = $conn->prepare("
            UPDATE tb_sobre_envios
            SET estado_envio = 2,
                fecha_envio = NOW(),
                mensaje_error = 'Archivo no encontrado en el servidor'
            WHERE id = ?
        ");
        $stmt->execute([$sobre['id']]);

        exit;
    }

    // Determinar el tipo de envío según el campo tipoEnvio
    $tipoEnvio = 1; // Valor por defecto para todos los documentos (Facturas, Notas de Crédito, Notas de Débito)

    // Solo cambiar a 2 si es específicamente un sobre de boletas
    if ($sobre['tipoEnvio'] === 'ENVIO_BOLETA') {
        $tipoEnvio = 2;
    }

    // Preparar el JSON para la API
    $jsonInput = json_encode([
        'Certificado' => [
            'Password' => $certificadoPassword,
            'Rut' => $rutCertificado
        ],
        'Ambiente' => $ambiente,
        'Tipo' => $tipoEnvio // 1 para DTE (Facturas, Notas), 2 para Boletas
    ]);

    // Configurar la solicitud cURL
    $ch = curl_init('https://api.simpleapi.cl/api/v1/envio/enviar');

    // Construir el formulario multipart
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;
    $postData = '';

    // Agregar el campo JSON
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonInput . "\r\n";

    // Agregar el archivo de certificado
    $fileContents = file_get_contents($certificadoPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Agregar el archivo de sobre
    $fileContents = file_get_contents($sobrePath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($sobrePath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Cerrar el cuerpo del mensaje
    $postData .= "--" . $delimiter . "--\r\n";

    // Configuración de cURL
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => [
            "Authorization: $apiKey",
            "Content-Type: multipart/form-data; boundary=" . $delimiter,
            "Content-Length: " . strlen($postData)
        ]
    ]);

    // Registrar la información de la solicitud
    error_log("Enviando sobre ID: " . $sobre['id'] . ", Archivo: " . $sobre['nombre_archivo']);

    // Ejecutar la solicitud
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // Si hubo error en la solicitud
    if ($curlError) {
        echo json_encode(['error' => 'Error al conectar con la API: ' . $curlError]);

        // Actualizar estado del sobre a error
        $stmt = $conn->prepare("
            UPDATE tb_sobre_envios
            SET estado_envio = 2,
                fecha_envio = NOW(),
                mensaje_error = ?
            WHERE id = ?
        ");
        $stmt->execute([$curlError, $sobre['id']]);

        exit;
    }

    // Procesar la respuesta
    $respuestaData = json_decode($response, true);

    // Verificar si la respuesta es exitosa (código 200 y 'ok' es true)
    if ($httpCode == 200 && isset($respuestaData['ok']) && $respuestaData['ok'] === true) {
        // Obtener trackId (notar que usa 'I' mayúscula)
        $trackId = $respuestaData['trackId'] ?? null;
        $glosa = $respuestaData['glosa'] ?? 'Envío procesado';

        // Actualizar estado del sobre a enviado correctamente
        $stmt = $conn->prepare("
            UPDATE tb_sobre_envios
            SET estado_envio = 1,
                fecha_envio = NOW(),
                trackid = ?,
                respuesta_xml = ?,
                glosa = ?
            WHERE id = ?
        ");
        $stmt->execute([$trackId, $response, $glosa, $sobre['id']]);

        echo json_encode([
            'success' => true,
            'mensaje' => 'Sobre enviado correctamente',
            'trackid' => $trackId,
            'glosa' => $glosa,
            'sobre_id' => $sobre['id'],
            'nombre_archivo' => $sobre['nombre_archivo']
        ]);
    } else {
        // Extraer mensaje de error si está disponible
        $errorMsg = 'Error en el envío del sobre';
        $glosa = null;

        if (isset($respuestaData['glosa'])) {
            $glosa = $respuestaData['glosa'];
        }

        if (isset($respuestaData['message'])) {
            $errorMsg = $respuestaData['message'];
        } elseif (isset($respuestaData['error'])) {
            $errorMsg = $respuestaData['error'];
        }

        // Verificar si a pesar del error hay un trackId (casos donde la respuesta es confusa)
        $trackId = $respuestaData['trackId'] ?? null;

        // Actualizar estado del sobre a error
        $stmt = $conn->prepare("
            UPDATE tb_sobre_envios
            SET estado_envio = 2,
                fecha_envio = NOW(),
                mensaje_error = ?,
                respuesta_xml = ?,
                trackid = ?,
                glosa = ?
            WHERE id = ?
        ");
        $stmt->execute([$errorMsg, $response, $trackId, $glosa, $sobre['id']]);

        echo json_encode([
            'error' => $errorMsg,
            'codigo_http' => $httpCode,
            'sobre_id' => $sobre['id'],
            'respuesta_api' => $respuestaData
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'error' => 'Error en el servidor: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    error_log("Error en enviar_sobre.php: " . $e->getMessage());
}
?>
