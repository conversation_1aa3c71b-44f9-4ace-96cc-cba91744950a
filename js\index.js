// Función para calcular el precio neto a partir del precio total
function calcularPrecioNeto(element) {
    const row = element.closest('.item-row');
    const tasaIVA = parseFloat(document.getElementById('tasaIVA').value) || 19;

    // Obtener el valor del precio total ingresado
    const precioTotal = parseFloat(element.value) || 0;

    // Calcular el precio neto (sin IVA) independientemente del tipo de documento
    const precioNeto = precioTotal / (1 + (tasaIVA / 100));

    // Actualizar el campo de precio con el valor calculado (sin dividir por la cantidad)
    row.querySelector('.item-precio').value = Math.round(precioNeto);

    // Recalcular el monto del ítem
    calcularMontoItem(row.querySelector('.item-precio'));
    actualizarMontoNeto();
}

// Función para copiar el JSON al portapapeles
document.addEventListener('DOMContentLoaded', function() {
    const copyJsonBtn = document.getElementById('copyJsonBtn');
    if (copyJsonBtn) {
        copyJsonBtn.addEventListener('click', function() {
            const jsonText = document.getElementById('jsonResult').textContent;
            navigator.clipboard.writeText(jsonText).then(() => {
                const copyMessage = document.getElementById('copyMessage');
                copyMessage.classList.add('visible');
                setTimeout(() => {
                    copyMessage.classList.remove('visible');
                }, 2000);
            });
        });
    }

    // Inicializar el contador DTE si existe
    if (typeof updateDTECount === 'function') {
        updateDTECount();
    }
});

        // Agregar el evento de submit al formulario DTE
document.getElementById('dteForm').addEventListener('submit', function(e) {
    e.preventDefault();
    generarJSON();
});

function generarJSON() {
    // Check if descuentos section is enabled and validate it if so
    const descuentosHabilitados = document.getElementById('enableDescuentosCheckbox').checked;
    const tipoDTE = parseInt(document.getElementById('tipoDTE').value);

    // Validación específica para Nota de Crédito (61)
    if (tipoDTE === 61) {
        console.log('Validando campos para Nota de Crédito');

        // Validar campos de referencia
        const fechaDocRef = document.getElementById('fechaDocRef').value;
        const tipoDocRef = document.getElementById('tipoDocRef').value;
        const folioRef = document.getElementById('folioRef').value;
        const codigoRef = document.getElementById('codigoRef').value;
        const razonRef = document.getElementById('razonRef').value;

        if (!fechaDocRef || !tipoDocRef || !folioRef || !codigoRef || !razonRef) {
            let emptyFields = [];
            if (!fechaDocRef) emptyFields.push('Fecha Documento Referencia');
            if (!tipoDocRef) emptyFields.push('Tipo Documento Referencia');
            if (!folioRef) emptyFields.push('Folio Referencia');
            if (!codigoRef) emptyFields.push('Código Referencia');
            if (!razonRef) emptyFields.push('Razón Referencia');

            alert('Para generar una Nota de Crédito, debe completar todos los campos de referencia. Faltan los siguientes campos: ' +
                  emptyFields.join(', '));
            return; // Detener ejecución
        }
    }

    if (descuentosHabilitados) {
        const tipoMov = document.getElementById('tipoMov').value;
        const descTipoValor = document.getElementById('descTipoValor').value;
        const descValor = document.getElementById('descValor').value;
        const descDescripcion = document.getElementById('descDescripcion').value;

        // Validate all fields are filled
        if (!tipoMov || !descTipoValor || !descValor || !descDescripcion) {
            // Build error message showing which fields are missing
            let emptyFields = [];
            if (!tipoMov) emptyFields.push('Tipo');
            if (!descTipoValor) emptyFields.push('Tipo de Valor');
            if (!descValor) emptyFields.push('Valor');
            if (!descDescripcion) emptyFields.push('Descripción');

            alert('La sección de Descuentos y Recargos está habilitada, pero faltan los siguientes campos: ' +
                  emptyFields.join(', ') + '.\n' +
                  'Por favor complete todos los campos o desactive la sección.');
            return; // Stop execution and don't generate JSON
        }

        // Validate descValor is not zero
        if (parseFloat(descValor) === 0) {
            alert('El campo "Valor" en Descuentos y Recargos no puede ser cero cuando la sección está habilitada.');
            return; // Stop execution and don't generate JSON
        }
    }

    // Obtain the type of DTE (already defined above)

    // Create the JSON object according to the DTE type
    let dteJSON;

    if (tipoDTE === 39) { // Boleta
        dteJSON = generarJSONBoleta();
    } else { // Factura u otros documentos (33, 34, 61)
        dteJSON = generarJSONFactura();
    }

    // Show the JSON in the results area
    const jsonResultElement = document.getElementById('jsonResult');
    jsonResultElement.textContent = JSON.stringify(dteJSON, null, 2);
    jsonResultElement.style.display = 'block';

    // Scroll to the result
    jsonResultElement.scrollIntoView({ behavior: 'smooth' });

    // Update buttons and UI after generating JSON
    actualizarBotonesPostJSON();
}

function generarJSONFactura() {
    // Obtener los valores del formulario
    const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
    const folio = parseInt(document.getElementById('folioDTE').value);
    const fechaEmision = document.getElementById('fechaEmision').value;
    let fechaVencimiento = document.getElementById('fechaVencimiento').value;
    const formaPago = parseInt(document.getElementById('formaPago').value);

    // Agregar logging específico para Nota de Crédito
    if (tipoDTE === 61) {
        console.log('Generando JSON para Nota de Crédito (61)');
        console.log('- Folio:', folio);
        console.log('- Fecha Emisión:', fechaEmision);

        // Asegurarse de que la fecha de vencimiento tenga un valor válido para Notas de Crédito
        if (!fechaVencimiento) {
            console.log('Fecha de vencimiento no establecida. Estableciendo igual a la fecha de emisión.');
            fechaVencimiento = fechaEmision;
            document.getElementById('fechaVencimiento').value = fechaEmision;
        }

        console.log('- Fecha Vencimiento:', fechaVencimiento);
    }

    // Validar que la fecha de vencimiento no sea menor a la fecha de emisión
    const fechaEmisionObj = new Date(fechaEmision);
    const fechaVencimientoObj = new Date(fechaVencimiento);

    if (fechaVencimientoObj < fechaEmisionObj) {
        console.warn('La fecha de vencimiento es menor a la fecha de emisión. Ajustando a la fecha de emisión.');
        fechaVencimiento = fechaEmision;
        document.getElementById('fechaVencimiento').value = fechaEmision;
    }

    // Datos del emisor
    const rutEmisor = document.getElementById('rutEmisor').value;
    const razonSocialEmisor = document.getElementById('razonSocialEmisor').value;
    const giroEmisor = document.getElementById('giroEmisor').value;
    const direccionEmisor = document.getElementById('direccionEmisor').value;
    const comunaEmisor = document.getElementById('comunaEmisor').value;
    const ciudadEmisor = document.getElementById('ciudadEmisor').value;

    // Datos del receptor
    const rutReceptor = document.getElementById('rutReceptor').value;
    const razonSocialReceptor = document.getElementById('razonSocialReceptor').value;
    const direccionReceptor = document.getElementById('direccionReceptor').value;
    const comunaReceptor = document.getElementById('comunaReceptor').value;
    const giroReceptor = document.getElementById('giroReceptor').value;
    const contactoReceptor = document.getElementById('contactoReceptor').value;

    // Datos de totales
    const montoNeto = parseInt(document.getElementById('montoNeto').value);
    const tasaIVA = parseInt(document.getElementById('tasaIVA').value);
    const iva = parseInt(document.getElementById('ivaCalculado').value);
    const montoTotal = parseInt(document.getElementById('montoTotal').value);

    // Verificar si los descuentos/recargos están habilitados
    const descuentosHabilitados = document.getElementById('enableDescuentosCheckbox').checked;

    // Datos de descuentos/recargos (solo si están habilitados)
    let tipoMov = '';
    let descTipoValor = '';
    let descValor = 0;
    let descDescripcion = '';

    if (descuentosHabilitados) {
        tipoMov = document.getElementById('tipoMov').value;
        descTipoValor = document.getElementById('descTipoValor').value;
        descValor = parseInt(document.getElementById('descValor').value) || 0;
        descDescripcion = document.getElementById('descDescripcion').value;
    }

    // Datos del certificado
    const rutCertificado = document.getElementById('rutCertificado').value;
    const passwordCertificado = document.getElementById('passwordCertificado').value;

    // Crear array de detalles (items)
    const detalles = [];
    document.querySelectorAll('.item-row').forEach((row, index) => {
        const nombre = row.querySelector('.item-nombre').value;
        const descripcion = row.querySelector('.item-descripcion').value;
        const cantidad = parseFloat(row.querySelector('.item-cantidad').value);
        const unidadMedida = row.querySelector('.item-unidad').value;
        const precio = parseFloat(row.querySelector('.item-precio').value);
        const descuento = parseFloat(row.querySelector('.item-descuento').value) || 0;
        const recargo = parseFloat(row.querySelector('.item-recargo').value) || 0;
        const montoItem = parseInt(row.querySelector('.item-monto').value);

        detalles.push({
            "IndicadorExento": 0,
            "Nombre": nombre,
            "Descripcion": descripcion,
            "Cantidad": cantidad,
            "UnidadMedida": unidadMedida,
            "Precio": precio,
            "Descuento": descuento,
            "Recargo": recargo,
            "MontoItem": montoItem
        });
    });

    // Construir el objeto JSON
    let jsonObj;

    // Estructura específica para Nota de Crédito (61)
    if (tipoDTE === 61) {
        console.log('Creando estructura JSON específica para Nota de Crédito');

        // Obtener los datos de referencia
        const fechaDocRef = document.getElementById('fechaDocRef').value;
        const tipoDocRef = parseInt(document.getElementById('tipoDocRef').value);
        const folioRef = parseInt(document.getElementById('folioRef').value);
        const codigoRef = parseInt(document.getElementById('codigoRef').value);
        const razonRef = document.getElementById('razonRef').value;

        // Validar que todos los campos de referencia estén completos
        if (!fechaDocRef || !tipoDocRef || !folioRef || !codigoRef || !razonRef) {
            console.warn('Faltan campos en la sección de Referencias para la Nota de Crédito');
            if (!fechaDocRef) console.warn('- Falta Fecha Documento Referencia');
            if (!tipoDocRef) console.warn('- Falta Tipo Documento Referencia');
            if (!folioRef) console.warn('- Falta Folio Referencia');
            if (!codigoRef) console.warn('- Falta Código Referencia');
            if (!razonRef) console.warn('- Falta Razón Referencia');
        }

        jsonObj = {
            "Documento": {
                "Encabezado": {
                    "IdentificacionDTE": {
                        "TipoDTE": tipoDTE,
                        "Folio": folio,
                        "FechaEmision": fechaEmision,
                        "FechaVencimiento": fechaVencimiento
                    },
                    "Emisor": {
                        "Rut": rutEmisor,
                        "RazonSocial": razonSocialEmisor,
                        "Giro": giroEmisor,
                        "ActividadEconomica": [620200, 631100],
                        "DireccionOrigen": direccionEmisor,
                        "ComunaOrigen": comunaEmisor,
                        "Telefono": []
                    },
                    "Receptor": {
                        "Rut": rutReceptor,
                        "RazonSocial": razonSocialReceptor,
                        "Direccion": direccionReceptor,
                        "Comuna": comunaReceptor,
                        "Giro": giroReceptor,
                        "Ciudad": comunaReceptor // Usar la comuna como ciudad
                    },
                    "Totales": {
                        "MontoNeto": montoNeto,
                        "MontoExento": 0,
                        "TasaIVA": tasaIVA,
                        "IVA": iva,
                        "MontoTotal": montoTotal
                    }
                },
                "Detalles": detalles,
                "Referencias": [{
                    "FechaDocumentoReferencia": fechaDocRef,
                    "TipoDocumento": tipoDocRef,
                    "FolioReferencia": folioRef || 0,
                    "CodigoReferencia": codigoRef || 1,
                    "RazonReferencia": razonRef || "Anula documento de referencia"
                }]
            },
            "Certificado": {
                "Rut": rutCertificado,
                "Password": passwordCertificado
            }
        };

        console.log('JSON para Nota de Crédito generado:', JSON.stringify(jsonObj, null, 2));
    } else {
        // Estructura para otros tipos de documentos (33, 34, etc.)
        jsonObj = {
            "Documento": {
                "Encabezado": {
                    "IdentificacionDTE": {
                        "TipoDTE": tipoDTE,
                        "Folio": folio,
                        "FechaEmision": fechaEmision,
                        "FechaVencimiento": fechaVencimiento,
                        "FormaPago": formaPago
                    },
                    "Emisor": {
                        "Rut": rutEmisor,
                        "RazonSocial": razonSocialEmisor,
                        "Giro": giroEmisor,
                        "ActividadEconomica": [620200, 631100],
                        "DireccionOrigen": direccionEmisor,
                        "ComunaOrigen": comunaEmisor,
                        "Telefono": []
                    },
                    "Receptor": {
                        "Rut": rutReceptor,
                        "RazonSocial": razonSocialReceptor,
                        "Direccion": direccionReceptor,
                        "Comuna": comunaReceptor,
                        "Giro": giroReceptor,
                        "Ciudad": comunaReceptor // Usar la comuna como ciudad
                    },
                    "RutSolicitante": "",
                    "Transporte": null,
                    "Totales": {
                        "MontoNeto": montoNeto,
                        "MontoExento": 0, // Agregar MontoExento con valor 0
                        "TasaIVA": tasaIVA,
                        "IVA": iva,
                        "MontoTotal": montoTotal
                    }
                },
                "Detalles": detalles,
                "Referencias": []
            },
            "Certificado": {
                "Rut": rutCertificado,
                "Password": passwordCertificado
            }
        };
    }

    // Incluir DescuentosRecargos solo si están habilitados
    if (descuentosHabilitados && tipoMov && descTipoValor) {
        jsonObj.Documento.DescuentosRecargos = [
            {
                "TipoMovimiento": tipoMov,
                "Descripcion": descDescripcion,
                "TipoValor": descTipoValor,
                "Valor": descValor
            }
        ];
    }

    // Add special handling for Nota de Crédito (61)
    if (tipoDTE === 61) {
        // Obtener el tipo de documento de referencia
        const tipoDocRef = parseInt(document.getElementById('tipoDocRef').value);
        console.log('Generando JSON para Nota de Crédito con referencia a documento tipo:', tipoDocRef);

        // Validar que todos los campos de referencia estén completos
        const fechaDocRef = document.getElementById('fechaDocRef').value;
        const folioRef = document.getElementById('folioRef').value;
        const codigoRef = document.getElementById('codigoRef').value;
        const razonRef = document.getElementById('razonRef').value;

        if (!fechaDocRef || !folioRef || !codigoRef || !razonRef) {
            console.warn('Faltan campos en la sección de Referencias para la Nota de Crédito');
            if (!fechaDocRef) console.warn('- Falta Fecha Documento Referencia');
            if (!folioRef) console.warn('- Falta Folio Referencia');
            if (!codigoRef) console.warn('- Falta Código Referencia');
            if (!razonRef) console.warn('- Falta Razón Referencia');

            // Mostrar alerta al usuario
            alert('Para generar una Nota de Crédito, debe completar todos los campos de referencia:\n\n' +
                  (!fechaDocRef ? '- Fecha Documento Referencia\n' : '') +
                  (!folioRef ? '- Folio Referencia\n' : '') +
                  (!codigoRef ? '- Código Referencia\n' : '') +
                  (!razonRef ? '- Razón Referencia\n' : ''));

            return; // Detener la generación del JSON si faltan campos
        }

        // Agregar la sección de Referencias al JSON
        jsonObj.Documento.Referencias = [{
            "FechaDocumentoReferencia": fechaDocRef,
            "TipoDocumento": tipoDocRef,
            "FolioReferencia": parseInt(folioRef) || 0,
            "CodigoReferencia": parseInt(codigoRef) || 1,
            "RazonReferencia": razonRef || "Anula documento de referencia"
        }];

        console.log('Sección Referencias generada:', JSON.stringify(jsonObj.Documento.Referencias, null, 2));

        // Verificar que la estructura del JSON sea correcta para Nota de Crédito
        if (!jsonObj.Documento.Referencias || jsonObj.Documento.Referencias.length === 0) {
            console.error('ERROR: No se pudo generar la sección de Referencias para la Nota de Crédito');
            alert('Error al generar la estructura de Referencias para la Nota de Crédito. Por favor, intente nuevamente.');
            return;
        }

        // Verificar que la fecha de vencimiento no sea anterior a la fecha de emisión
        const fechaEmisionObj = new Date(fechaEmision);
        const fechaVencimientoObj = new Date(fechaVencimiento);

        if (fechaVencimientoObj < fechaEmisionObj) {
            console.warn('La fecha de vencimiento es anterior a la fecha de emisión. Ajustando...');
            fechaVencimiento = fechaEmision;
            document.getElementById('fechaVencimiento').value = fechaEmision;
        }
    }

    return jsonObj;
}

function generarJSONBoleta() {
    // Obtener los valores del formulario
    const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
    const folio = parseInt(document.getElementById('folioDTE').value);
    const fechaEmision = document.getElementById('fechaEmision').value;
    const indicadorServicio = parseInt(document.getElementById('indicadorServicio').value);

    // Datos del emisor
    const rutEmisor = document.getElementById('rutEmisor').value;
    const razonSocialBoleta = document.getElementById('razonSocialBoleta').value;
    const giroBoleta = document.getElementById('giroBoleta').value;
    const direccionEmisor = document.getElementById('direccionEmisor').value;
    const comunaEmisor = document.getElementById('comunaEmisor').value;

    // Datos del receptor
    const rutReceptor = document.getElementById('rutReceptor').value;
    const razonSocialReceptor = document.getElementById('razonSocialReceptor').value;
    const direccionReceptor = document.getElementById('direccionReceptor').value;
    const comunaReceptor = document.getElementById('comunaReceptor').value;
    const contactoReceptor = document.getElementById('contactoReceptor').value;

    // Datos de totales
    const montoNeto = parseInt(document.getElementById('montoNeto').value);
    const iva = parseInt(document.getElementById('ivaCalculado').value);
    const montoTotal = parseInt(document.getElementById('montoTotal').value);

    // Datos del certificado
    const rutCertificado = document.getElementById('rutCertificado').value;
    const passwordCertificado = document.getElementById('passwordCertificado').value;

    // Crear array de detalles (items)
    const detalles = [];
    document.querySelectorAll('.item-row').forEach((row, index) => {
        const nombre = row.querySelector('.item-nombre').value;
        const descripcion = row.querySelector('.item-descripcion').value;
        const cantidad = parseFloat(row.querySelector('.item-cantidad').value);
        const unidadMedida = row.querySelector('.item-unidad').value;
        const precio = parseFloat(row.querySelector('.item-precio').value);
        const descuento = parseFloat(row.querySelector('.item-descuento').value) || 0;
        const recargo = parseFloat(row.querySelector('.item-recargo').value) || 0;
        const montoItem = parseInt(row.querySelector('.item-monto').value);

        detalles.push({
            "IndicadorExento": 0,
            "Nombre": nombre,
            "Descripcion": descripcion,
            "Cantidad": cantidad,
            "UnidadMedida": unidadMedida,
            "Precio": precio,
            "Descuento": descuento,
            "Recargo": recargo,
            "MontoItem": montoItem
        });
    });

    // Construir el objeto JSON para boleta
    return {
        "Documento": {
            "Encabezado": {
                "IdentificacionDTE": {
                    "TipoDTE": tipoDTE,
                    "Folio": folio,
                    "FechaEmision": fechaEmision,
                    "IndicadorServicio": indicadorServicio
                },
                "Emisor": {
                    "Rut": rutEmisor,
                    "RazonSocialBoleta": razonSocialBoleta,
                    "GiroBoleta": giroBoleta,
                    "DireccionOrigen": direccionEmisor,
                    "ComunaOrigen": comunaEmisor
                },
                "Receptor": {
                    "Rut": rutReceptor,
                    "RazonSocial": razonSocialReceptor,
                    "Direccion": direccionReceptor,
                    "Comuna": comunaReceptor,
                    "Ciudad": comunaReceptor // Usar la comuna como ciudad
                },
                "Totales": {
                    "MontoNeto": montoNeto,
                    "MontoExento": 0, // Agregar MontoExento con valor 0
                    "IVA": iva,
                    "MontoTotal": montoTotal
                }
            },
            "Detalles": detalles
        },
        "Certificado": {
            "Rut": rutCertificado,
            "Password": passwordCertificado
        }
    };
}



    function printQuote() {
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems.length === 0) {
            alert('No hay productos en el carrito para imprimir.');
            return;
        }

        const styles = `
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #2c3e50; text-align: center; }
                p { text-align: center; margin-bottom: 20px; }
                .logo-container {
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                    margin: 20px 0;
                    max-width: 600px;
                    margin: 20px auto;
                }
                .logo-container img {
                    height: 60px;
                    object-fit: contain;
                    margin: 0 10px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    margin-bottom: 100px; /* Espacio para el footer */
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th { background-color: #2c3e50; color: white; }
                footer {
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-top: 1px solid #ddd;
                    text-align: center;
                }
                .total {
                    font-weight: bold;
                    font-size: 1.4em;
                }
            </style>
        `;

        let totalCotizacion = 0;
        let quoteContent = `
            ${styles}
            <h1>TATA REPUESTOS Temuco</h1>
            <p>Avenida Alemania 1222</p>
            <div class="logo-container">
                <img src="/images/icons/citroen.png" alt="Citroen">
                <img src="/images/icons/peugeot.jpg" alt="Peugeot">
                <img src="/images/icons/renault.png" alt="Renault">
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Producto</th>
                        <th>Cantidad</th>
                        <th>Precio Unitario</th>
                        <th>Descuento</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
        `;

        cart.forEach(item => {
            const subtotal = item.precio * item.quantity;
            const descuento = item.discountType === 'percentage'
                ? (subtotal * (item.discountValue / 100))
                : item.discountValue;
            const totalConDescuento = subtotal - descuento;

            totalCotizacion += totalConDescuento;

            quoteContent += `
                <tr>
                    <td>${item.nombre}</td>
                    <td>${item.quantity}</td>
                    <td>$${item.precio.toLocaleString('es-CL')}</td>
                    <td>${item.discountType === 'percentage' ? item.discountValue + '%' : '$' + item.discountValue}</td>
                    <td>$${totalConDescuento.toLocaleString('es-CL')}</td>
                </tr>
            `;
        });

        quoteContent += `
                </tbody>
            </table>
            <footer>
                <div class="total">Total: $${totalCotizacion.toLocaleString('es-CL')}</div>
            </footer>
        `;

        const quoteWindow = window.open('', '_blank');
        quoteWindow.document.write(`
            <html>
                <head>
                    <title>Cotización - TATA REPUESTOS</title>
                    <meta charset="UTF-8">
                    <base href="${window.location.href.substring(0, window.location.href.lastIndexOf('/'))}/">
                </head>
                <body>
                    ${quoteContent}
                </body>
            </html>
        `);
        quoteWindow.document.close();

        // Esperar a que las imágenes se carguen
        const images = quoteWindow.document.images;
        let loadedImages = 0;

        function tryPrint() {
            loadedImages++;
            if (loadedImages === images.length) {
                setTimeout(() => {
                    quoteWindow.print();
                }, 500);
            }
        }

        // Si no hay imágenes, imprimir directamente
        if (images.length === 0) {
            quoteWindow.print();
        } else {
            // Esperar a que cada imagen se cargue
            Array.from(images).forEach(img => {
                if (img.complete) {
                    tryPrint();
                } else {
                    img.addEventListener('load', tryPrint);
                    img.addEventListener('error', tryPrint);
                }
            });
        }
    }

    function cancelNewClient() {
        document.getElementById('newClientForm').style.display = 'none';
    }

    function updateTotalGeneral() {
        const total = cart.reduce((sum, item) => {
            const subtotal = Number(item.precio) * item.quantity;
            const discount = item.discountType === 'percentage'
                ? (subtotal * (item.discountValue / 100))
                : item.discountValue;
            return sum + (subtotal - discount);
        }, 0);
        document.getElementById('total-general').textContent = Math.round(total).toLocaleString('es-CL');
    }

    function renderCart() {
        const cartItems = document.querySelector('.cart-items');
        const cartHTML = cart.map(item => {
            const subtotal = Number(item.precio) * item.quantity;
            const discountAmount = item.discountType === 'percentage'
                ? (subtotal * (item.discountValue / 100))
                : item.discountValue;
            const finalSubtotal = subtotal - discountAmount;

            return `
            <div class="cart-item" data-id="${item.id}">
                <button class="delete-item-btn"><i class="fas fa-trash"></i></button>
                <div class="cart-item-details">
                    <h4>${item.nombre}</h4>
                    <p>SKU: ${item.sku}</p>
                    <p class="cantidad-destacada">Cantidad: ${item.quantity}</p>
                    <p>Precio unitario: $${Number(item.precio).toFixed(2)}</p>
                    <div class="discount-controls">
                        <select class="discount-type modern-select" onchange="updateDiscount('${item.id}', this.value, ${item.discountValue})">
                            <option value="percentage" ${item.discountType === 'percentage' ? 'selected' : ''}>%</option>
                            <option value="fixed" ${item.discountType === 'fixed' ? 'selected' : ''}>$</option>
                        </select>
                        <input type="number" min="0" class="discount-value modern-input" value="${item.discountValue}"
                            onchange="updateDiscount('${item.id}', '${item.discountType}', this.value)">
                    </div>
                    <p>Subtotal: $${subtotal.toFixed(2)}</p>
                    <p>Descuento: $${discountAmount.toFixed(2)}</p>
                    <p>Total con descuento: $${finalSubtotal.toFixed(2)}</p>
                </div>
            </div>
        `}).join('');

        cartItems.innerHTML = cartHTML;
        updateTotalGeneral();
    }

    function togglePaymentFields() {
        const paymentFields = document.getElementById('payment-fields');
        const button = event.currentTarget;
        const icon = button.querySelector('i');

        if (paymentFields.style.display === 'none') {
            paymentFields.style.display = 'block';
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        } else {
            paymentFields.style.display = 'none';
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        }
    }

    // Funcionalidad para el canvas DTE
    document.addEventListener('DOMContentLoaded', function() {
        // Set default date values to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('fechaEmision').value = today;
        document.getElementById('fechaVencimiento').value = today;

        // Agregar validación para la fecha de vencimiento
        document.getElementById('fechaVencimiento').addEventListener('change', function() {
            const fechaEmision = document.getElementById('fechaEmision').value;
            const fechaVencimiento = this.value;

            const fechaEmisionObj = new Date(fechaEmision);
            const fechaVencimientoObj = new Date(fechaVencimiento);

            if (fechaVencimientoObj < fechaEmisionObj) {
                alert('La fecha de vencimiento no puede ser menor a la fecha de emisión. Se ajustará a la fecha de emisión.');
                this.value = fechaEmision;
            }
        });

        // Agregar validación para la fecha de emisión
        document.getElementById('fechaEmision').addEventListener('change', function() {
            const fechaEmision = this.value;
            const fechaVencimiento = document.getElementById('fechaVencimiento').value;

            const fechaEmisionObj = new Date(fechaEmision);
            const fechaVencimientoObj = new Date(fechaVencimiento);

            if (fechaVencimientoObj < fechaEmisionObj) {
                alert('La fecha de vencimiento no puede ser menor a la fecha de emisión. Se ajustará a la nueva fecha de emisión.');
                document.getElementById('fechaVencimiento').value = fechaEmision;
            }
        });

        // Toggle fields based on DTE type
        window.toggleDTEFields = function() {
            const tipoDTE = document.getElementById('tipoDTE').value;
            const referenciasSection = document.getElementById('referenciasSection');
            const facturaFields = document.querySelectorAll('.factura-field');
            const boletaFields = document.querySelectorAll('.boleta-field');

            // Mostrar/ocultar sección de referencias para Nota de Crédito
            if (tipoDTE === '61') {
                referenciasSection.style.display = 'block';
            } else {
                referenciasSection.style.display = 'none';
            }

            // Resto de la lógica existente para otros campos
            if (tipoDTE === '39') { // Boleta
                facturaFields.forEach(field => field.style.display = 'none');
                boletaFields.forEach(field => field.style.display = 'block');
            } else { // Factura
                facturaFields.forEach(field => field.style.display = 'block');
                boletaFields.forEach(field => field.style.display = 'none');
            }
        };

        // Default receptor checkbox handler
        document.getElementById('defaultReceptorCheckbox').addEventListener('change', function() {
            const tipoDTE = document.getElementById('tipoDTE').value;

            if (this.checked && tipoDTE === '39') { // Only apply readonly for Boleta
                // Fill receptor fields with default data for Boleta
                document.getElementById('rutReceptor').value = '66666666-6';
                document.getElementById('razonSocialReceptor').value = 'Cliente Boleta';
                document.getElementById('direccionReceptor').value = 'Temuco';
                document.getElementById('comunaReceptor').value = 'Temuco';
                document.getElementById('contactoReceptor').value = '';

                // Add readonly attribute to prevent editing (only for boletas)
                document.getElementById('rutReceptor').readOnly = true;
                document.getElementById('razonSocialReceptor').readOnly = true;
                document.getElementById('direccionReceptor').readOnly = true;
                document.getElementById('comunaReceptor').readOnly = true;

                // Add visual indicator that fields are readonly
                document.getElementById('rutReceptor').classList.add('readonly-field');
                document.getElementById('razonSocialReceptor').classList.add('readonly-field');
                document.getElementById('direccionReceptor').classList.add('readonly-field');
                document.getElementById('comunaReceptor').classList.add('readonly-field');
            } else {
                // Remove readonly attribute to allow editing
                document.getElementById('rutReceptor').readOnly = false;
                document.getElementById('razonSocialReceptor').readOnly = false;
                document.getElementById('direccionReceptor').readOnly = false;
                document.getElementById('comunaReceptor').readOnly = false;

                // Remove visual indicator
                document.getElementById('rutReceptor').classList.remove('readonly-field');
                document.getElementById('razonSocialReceptor').classList.remove('readonly-field');
                document.getElementById('direccionReceptor').classList.remove('readonly-field');
                document.getElementById('comunaReceptor').classList.remove('readonly-field');

                // Clear the fields if unchecking the box
                if (!this.checked) {
                    document.getElementById('rutReceptor').value = '';
                    document.getElementById('razonSocialReceptor').value = '';
                    document.getElementById('direccionReceptor').value = '';
                    document.getElementById('comunaReceptor').value = '';
                    document.getElementById('contactoReceptor').value = '';
                }
            }
        });

        // Call toggleDTEFields on initial load to ensure proper visibility
        toggleDTEFields();

        // Función para cargar los almacenes disponibles
        window.cargarAlmacenes = async function() {
            try {
                const response = await fetch('get_almacenes.php', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                });

                const data = await response.json();

                if (data.status === 'success' && data.almacenes && data.almacenes.length > 0) {
                    const almacenSelect = document.getElementById('almacen_id');

                    // Limpiar opciones existentes
                    almacenSelect.innerHTML = '';

                    // Agregar las opciones de almacenes
                    data.almacenes.forEach(almacen => {
                        const option = document.createElement('option');
                        option.value = almacen.id;
                        option.textContent = almacen.nombre;
                        almacenSelect.appendChild(option);
                    });

                    console.log('Almacenes cargados correctamente:', data.almacenes);
                } else {
                    console.error('Error al cargar almacenes:', data);
                }
            } catch (error) {
                console.error('Error al cargar almacenes:', error);
            }
        };

        // Función para manejar el checkbox de actualización de inventario
        window.toggleInventarioFields = function() {
            const enableInventario = document.getElementById('enableInventarioCheckbox').checked;
            const inventarioFields = document.getElementById('inventarioFields');

            if (enableInventario) {
                inventarioFields.style.display = 'block';
            } else {
                inventarioFields.style.display = 'none';
            }
        };

        // Configurar el evento para el checkbox de inventario
        document.getElementById('enableInventarioCheckbox').addEventListener('change', toggleInventarioFields);

        // Inicializar campos de inventario
        toggleInventarioFields();

        // Function to fetch the next folio from the database
        window.fetchFolio = function() {
            const tipoDocumento = document.getElementById('tipoDTE').value;

            // Get document type name for better error messages
            let documentTypeName;
            switch(tipoDocumento) {
                case "33": documentTypeName = "Factura Electrónica"; break;
                case "34": documentTypeName = "Factura Exenta"; break;
                case "39": documentTypeName = "Boleta Electrónica"; break;
                case "61": documentTypeName = "Nota de Crédito"; break;
                default: documentTypeName = `Documento tipo ${tipoDocumento}`;
            }

            console.log(`Solicitando folio para documento tipo ${tipoDocumento} (${documentTypeName})`);

            // Show loading indicator
            const folioInput = document.getElementById('folioDTE');
            const originalValue = folioInput.value || "";
            folioInput.value = "Cargando...";
            folioInput.disabled = true;

            // Make AJAX request to get the folio
            fetch(`get_folio.php?tipo_documento=${tipoDocumento}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log(`Folio obtenido exitosamente: ${data.folio}`);
                        folioInput.value = data.folio;

                        // Mostrar advertencia si es el último folio
                        if (data.lastFolio) {
                            showNotification(data.warning, 'warning');

                            // Opcional: Agregar clase visual para indicar último folio
                            folioInput.classList.add('last-folio');
                        }
                    } else {
                        console.error('Error fetching folio:', data.message || data.error);

                        // Keep the original value if available, otherwise set a placeholder
                        if (originalValue && originalValue !== "Cargando...") {
                            folioInput.value = originalValue;
                        } else {
                            folioInput.value = "0"; // Placeholder value
                            folioInput.classList.add('folio-error');
                        }

                        // Show user-friendly error message
                        const errorMsg = `
No hay folios disponibles para ${documentTypeName}.

Esto puede deberse a:
1. No hay folios CAF cargados para este tipo de documento
2. Todos los folios existentes ya han sido utilizados

Por favor contacte al administrador para cargar nuevos folios.`;

                        showNotification(errorMsg, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error in folio fetch:', error);
                    if (originalValue && originalValue !== "Cargando...") {
                        folioInput.value = originalValue;
                    } else {
                        folioInput.value = "0"; // Placeholder value
                        folioInput.classList.add('folio-error');
                    }
                    showNotification('Error al conectar con el servidor para obtener el folio.', 'error');
                })
                .finally(() => {
                    folioInput.disabled = false;
                });
        };

        // Helper function to show notifications
        window.showNotification = function(message, type = 'info') {
            // Check if we already have a notification container
            let notificationContainer = document.getElementById('notification-container');

            if (!notificationContainer) {
                // Create container for notifications if it doesn't exist
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                notificationContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999;';
                document.body.appendChild(notificationContainer);
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = 'transform: translateX(100%); opacity: 0; transition: transform 0.3s ease, opacity 0.3s ease;';
            notification.innerHTML = `
                <div style="background: ${type === 'error' ? '#ffebee' : '#e8f5e9'};
                            border-left: 4px solid ${type === 'error' ? '#f44336' : '#4caf50'};
                            padding: 10px 15px; margin-bottom: 10px; border-radius: 4px;
                            box-shadow: 0 2px 5px rgba(0,0,0,0.2); position: relative;
                            max-width: 250px; word-wrap: break-word;">
                    ${message}
                </div>
            `;

            // Add to container
            notificationContainer.appendChild(notification);

            // Trigger animation after a small delay (for the browser to process the addition)
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 10);

            // Remove after 2 seconds with exit animation
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.transform = 'translateX(100%)';
                    notification.style.opacity = '0';

                    // Remove from DOM after animation completes
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, 2000); // 2 seconds
        }

        // Add this CSS for the folio error and warning indications
        const style = document.createElement('style');
        style.textContent = `
            .folio-error {
                background-color: #ffebee !important;
                border: 1px solid #f44336 !important;
            }
            .last-folio {
                background-color: #fff3e0 !important;
                border: 1px solid #ff9800 !important;
            }
        `;
        document.head.appendChild(style);

        // Fetch folio when opening the DTE canvas
        document.getElementById('openDTEBtn').addEventListener('click', function() {
            document.getElementById('dteCanvas').classList.add('active');
            document.getElementById('dteOverlay').classList.add('active');
            fetchFolio(); // Obtener el folio al abrir el canvas
            cargarAlmacenes(); // Cargar los almacenes disponibles

            // Inicializar el receptor genérico para boletas
            if (typeof initializeDTECanvas === 'function') {
                initializeDTECanvas();
            }

            // Forzar receptor genérico para boletas
            setTimeout(() => {
                if (typeof forceBoletaReceptorGenerico === 'function') {
                    forceBoletaReceptorGenerico();
                }
            }, 200);
        });

        // Close DTE Canvas
        document.getElementById('closeDTEBtn').addEventListener('click', function() {
            document.getElementById('dteCanvas').classList.remove('active');
            document.getElementById('dteOverlay').classList.remove('active');
        });

        document.getElementById('dteOverlay').addEventListener('click', function() {
            document.getElementById('dteCanvas').classList.remove('active');
            this.classList.remove('active');
        });

        // Add new item
        let itemCount = 1;
        document.getElementById('addItemBtn').addEventListener('click', function() {
            const itemsContainer = document.getElementById('itemsContainer');
            const newItem = document.createElement('div');
            newItem.className = 'item-row';
            newItem.innerHTML = `
                <button type="button" class="remove-item-btn"><i class="fas fa-times"></i></button>
                <div class="form-row">
                    <div class="form-group">
                        <label for="nombre_${itemCount}">Nombre</label>
                        <input type="text" id="nombre_${itemCount}" class="modern-input item-nombre" required>
                    </div>
                    <div class="form-group">
                        <label for="cantidad_${itemCount}">Cantidad</label>
                        <input type="number" id="cantidad_${itemCount}" class="modern-input item-cantidad" required value="1" min="1" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                </div>
                <div class="form-group">
                    <label for="descripcion_${itemCount}">Descripción</label>
                    <input type="text" id="descripcion_${itemCount}" class="modern-input item-descripcion">
                </div>
                <div class="form-row">
                    <div class="form-group" style="width: 15%;">
                        <label for="unidad_${itemCount}">Unidad</label>
                        <input type="text" id="unidad_${itemCount}" class="modern-input item-unidad" value="un" style="width: 100%;">
                    </div>
                    <div class="form-group" style="width: 40%;">
                        <label for="precio_${itemCount}">Precio <span class="precio-tipo-label">${parseInt(document.getElementById('tipoDTE').value) === 39 ? '(Con IVA)' : '(Neto)'}</span></label>
                        <input type="number" id="precio_${itemCount}" class="modern-input item-precio" required value="0" min="0" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                        <div class="precio-info">${parseInt(document.getElementById('tipoDTE').value) === 39 ? 'En boletas, el precio incluye IVA. El monto del ítem pasa al monto total.' : 'En facturas, el precio es neto (sin IVA). El monto del ítem pasa al monto neto.'}</div>
                    </div>
                    <div class="form-group" style="width: 40%;">
                        <label for="precio_total_${itemCount}">Precio Total</label>
                        <input type="number" id="precio_total_${itemCount}" class="modern-input item-precio-total" value="0" min="0" onchange="calcularPrecioNeto(this);" oninput="calcularPrecioNeto(this);">
                        <div class="precio-info">Ingrese el valor total para calcular el precio neto</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="descuento_${itemCount}">Descuento</label>
                        <input type="number" id="descuento_${itemCount}" class="modern-input item-descuento" value="0" min="0" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                    <div class="form-group">
                        <label for="recargo_${itemCount}">Recargo</label>
                        <input type="number" id="recargo_${itemCount}" class="modern-input item-recargo" value="0" min="0" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                    </div>
                    <div class="form-group">
                        <label for="montoItem_${itemCount}">Monto Item</label>
                        <input type="number" id="montoItem_${itemCount}" class="modern-input item-monto" value="0" readonly>
                    </div>
                </div>
            `;
            itemsContainer.appendChild(newItem);
            itemCount++;

            // Add remove functionality to new button
            newItem.querySelector('.remove-item-btn').addEventListener('click', function() {
                if (document.querySelectorAll('.item-row').length > 1) {
                    itemsContainer.removeChild(newItem);
                    actualizarMontoNeto(); // Update total when removing an item
                } else {
                    alert('Debe mantener al menos un ítem en el documento');
                }
            });
        });

        // Initialize item calculations on page load
        document.querySelectorAll('.item-row').forEach(row => {
            calcularMontoItem(row.querySelector('.item-cantidad'));
        });
        actualizarMontoNeto();

        // Inicializar las etiquetas de precio según el tipo de documento
        actualizarEtiquetasPrecio();

        // ...existing code...

    // Añadir evento para el nuevo botón "Generar y Enviar DTE directamente"
    const generateAndSendBtn = document.getElementById('generateAndSendBtn');
    if (generateAndSendBtn) {
        generateAndSendBtn.addEventListener('click', generateAndSendDirect);
    }

    });

    // Function to calculate the amount for a single item
    function calcularMontoItem(element) {
        const row = element.closest('.item-row');
        const tipoDTE = parseInt(document.getElementById('tipoDTE').value);

        // Verificar si es una nota de crédito con datos poblados desde un documento de referencia
        const esNotaCredito = tipoDTE === 61;
        const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

        // Si es una nota de crédito con datos poblados, no recalcular el monto del ítem
        if (esNotaCredito && formularioPoblado) {
            console.log('Nota de crédito con datos poblados: no se recalcula el monto del ítem');
            return; // Salir de la función sin recalcular
        }

        // Get all the necessary input values
        const cantidad = parseFloat(row.querySelector('.item-cantidad').value) || 0;
        const precio = parseFloat(row.querySelector('.item-precio').value) || 0;
        const descuento = parseFloat(row.querySelector('.item-descuento').value) || 0;
        const recargo = parseFloat(row.querySelector('.item-recargo').value) || 0;
        const tasaIVA = parseFloat(document.getElementById('tasaIVA').value) || 19;

        let montoItem;

        // Comportamiento diferenciado según el tipo de documento
        if (tipoDTE === 39) { // Boleta
            // Para boletas: El precio ingresado se considera como precio unitario con IVA incluido
            // El monto del ítem también incluye IVA (es igual al precio * cantidad)
            montoItem = (cantidad * precio) - descuento + recargo;
        } else { // Factura u otros documentos (33, 34, 61)
            // Para facturas: El precio ingresado se considera como precio unitario NETO (sin IVA)
            montoItem = (cantidad * precio) - descuento + recargo;
        }

        // Update the item amount field - siempre redondeamos para evitar decimales
        row.querySelector('.item-monto').value = Math.round(montoItem);
    }

    // Function to update totals from all items
    function actualizarMontoNeto() {
        const tipoDTE = parseInt(document.getElementById('tipoDTE').value);

        // Verificar si es una nota de crédito con datos poblados desde un documento de referencia
        const esNotaCredito = tipoDTE === 61;
        const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

        // Si es una nota de crédito con datos poblados, no recalcular los totales
        if (esNotaCredito && formularioPoblado) {
            console.log('Nota de crédito con datos poblados: no se recalculan los totales');
            return; // Salir de la función sin recalcular
        }

        let montoItems = 0;

        // Sumar todos los montos de ítems
        document.querySelectorAll('.item-row').forEach(row => {
            montoItems += parseFloat(row.querySelector('.item-monto').value) || 0;
        });

        const tasaIVA = parseFloat(document.getElementById('tasaIVA').value) || 19;

        // Comportamiento diferenciado según el tipo de documento
        if (tipoDTE === 39) { // Boleta
            // Para boletas: El monto de los ítems (que ya incluye IVA) pasa directamente al monto total
            const montoTotal = Math.round(montoItems);

            // Calcular hacia atrás para obtener el monto neto y el IVA
            const factor = 1 + (tasaIVA / 100);
            const montoNeto = Math.round(montoTotal / factor);
            const iva = Math.round(montoTotal - montoNeto);

            // Actualizar los campos
            document.getElementById('montoNeto').value = montoNeto;
            document.getElementById('ivaCalculado').value = iva;
            document.getElementById('montoTotal').value = montoTotal;
        } else { // Factura u otros documentos (33, 34, 61)
            // Para facturas: El monto de los ítems pasa directamente al monto neto
            const montoNeto = Math.round(montoItems);

            // Calcular el IVA y el monto total
            const iva = Math.round(montoNeto * (tasaIVA / 100));
            const montoTotal = montoNeto + iva;

            // Actualizar los campos
            document.getElementById('montoNeto').value = montoNeto;
            document.getElementById('ivaCalculado').value = iva;
            document.getElementById('montoTotal').value = montoTotal;
        }
    }

    // Function to calculate totals when changing values directly in the totals section
    function calcularTotales() {
        const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
        const tasaIVA = parseFloat(document.getElementById('tasaIVA').value) || 19;

        // Verificar si es una nota de crédito con datos poblados desde un documento de referencia
        const esNotaCredito = tipoDTE === 61;
        const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

        // Si es una nota de crédito con datos poblados, no recalcular los totales
        if (esNotaCredito && formularioPoblado) {
            console.log('Nota de crédito con datos poblados: no se recalculan los totales');
            return; // Salir de la función sin recalcular
        }

        // Determinar qué campo se modificó (montoNeto o montoTotal)
        const activeElement = document.activeElement;
        const activeId = activeElement ? activeElement.id : null;

        if (tipoDTE === 39) { // Boleta
            if (activeId === 'montoNeto') {
                // Si se modificó el monto neto, calcular hacia adelante
                const montoNeto = parseFloat(document.getElementById('montoNeto').value) || 0;
                const iva = Math.round(montoNeto * (tasaIVA / 100));
                const montoTotal = Math.round(montoNeto + iva);

                document.getElementById('ivaCalculado').value = iva;
                document.getElementById('montoTotal').value = montoTotal;
            } else {
                // Si se modificó el monto total o cualquier otro campo, calcular hacia atrás
                const montoTotal = parseFloat(document.getElementById('montoTotal').value) || 0;
                const factor = 1 + (tasaIVA / 100);
                const montoNeto = Math.round(montoTotal / factor);
                const iva = Math.round(montoTotal - montoNeto);

                document.getElementById('montoNeto').value = montoNeto;
                document.getElementById('ivaCalculado').value = iva;
            }
        } else { // Factura u otros documentos (33, 34, 61)
            if (activeId === 'montoTotal') {
                // Si se modificó el monto total, calcular hacia atrás
                const montoTotal = parseFloat(document.getElementById('montoTotal').value) || 0;
                const factor = 1 + (tasaIVA / 100);
                const montoNeto = Math.round(montoTotal / factor);
                const iva = Math.round(montoTotal - montoNeto);

                document.getElementById('montoNeto').value = montoNeto;
                document.getElementById('ivaCalculado').value = iva;
            } else {
                // Si se modificó el monto neto o cualquier otro campo, calcular hacia adelante
                const montoNeto = parseFloat(document.getElementById('montoNeto').value) || 0;
                const iva = Math.round(montoNeto * (tasaIVA / 100));
                const montoTotal = Math.round(montoNeto + iva);

                document.getElementById('ivaCalculado').value = iva;
                document.getElementById('montoTotal').value = montoTotal;
            }
        }
    }

    // Add event listener for DTE type changes
    document.getElementById('tipoDTE').addEventListener('change', function() {
        // Recalcular todos los montos cuando cambia el tipo de DTE
        document.querySelectorAll('.item-row').forEach(row => {
            calcularMontoItem(row.querySelector('.item-precio'));
        });
        actualizarMontoNeto();

        // Actualizar etiquetas de precio según el tipo de documento
        actualizarEtiquetasPrecio();

        // Mostrar mensaje informativo sobre el cambio en el cálculo
        const tipoDTE = parseInt(this.value);
        let mensaje = '';

        if (tipoDTE === 39) {
            mensaje = 'Boleta: El precio ingresado se considera con IVA incluido. El monto del ítem pasa al monto total.';
        } else {
            mensaje = 'Factura: El precio ingresado se considera neto (sin IVA). El monto del ítem pasa al monto neto.';
        }

        // Mostrar notificación temporal
        if (typeof showNotification === 'function') {
            showNotification(mensaje, 'info');
        } else {
            alert(mensaje);
        }
    });

    // Función para actualizar las etiquetas de precio según el tipo de documento
    function actualizarEtiquetasPrecio() {
        const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
        const precioTipoLabel = document.getElementById('precio-tipo-label');
        const precioInfo = document.getElementById('precio-info');

        if (tipoDTE === 39) { // Boleta
            if (precioTipoLabel) precioTipoLabel.textContent = '(Con IVA)';
            if (precioInfo) precioInfo.textContent = 'En boletas, el precio incluye IVA. El monto del ítem pasa al monto total.';
        } else { // Factura u otros documentos
            if (precioTipoLabel) precioTipoLabel.textContent = '(Neto)';
            if (precioInfo) precioInfo.textContent = 'En facturas, el precio es neto (sin IVA). El monto del ítem pasa al monto neto.';
        }

        // Actualizar también las etiquetas en filas de items adicionales
        document.querySelectorAll('.item-row').forEach((row, index) => {
            if (index > 0) { // Solo para filas adicionales (no la primera que ya tiene etiquetas)
                const precioLabel = row.querySelector('label[for^="precio_"]');
                if (precioLabel) {
                    // Eliminar etiqueta anterior si existe
                    const existingLabel = precioLabel.querySelector('.precio-tipo-label');
                    if (existingLabel) {
                        existingLabel.remove();
                    }

                    // Crear nueva etiqueta
                    const newLabel = document.createElement('span');
                    newLabel.className = 'precio-tipo-label';
                    newLabel.textContent = tipoDTE === 39 ? '(Con IVA)' : '(Neto)';
                    precioLabel.appendChild(newLabel);
                }
            }
        });
    }

    // Initialize remove button functionality for initial item
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelector('.remove-item-btn').addEventListener('click', function() {
            if (document.querySelectorAll('.item-row').length > 1) {
                this.closest('.item-row').remove();
                actualizarMontoNeto();
            } else {
                alert('Debe mantener al menos un ítem en el documento');
            }
        });
    });




            function enviarDTE() {
            // Obtener el JSON generado
            const jsonData = document.getElementById('jsonResult').textContent;

            if (!jsonData) {
                alert('Primero debe generar el JSON antes de enviarlo.');
                return;
            }

            try {
                // Validar que sea un JSON válido
                JSON.parse(jsonData);
            } catch (e) {
                alert('El JSON generado no es válido: ' + e.message);
                return;
            }

            // Mostrar indicador de carga
            document.getElementById('loadingIndicator').style.display = 'flex';
            document.getElementById('responseContainer').style.display = 'none';

            // Ocultar el botón de imprimir PDF (si estaba visible de una operación anterior)
            document.getElementById('printPdfBtn').style.display = 'none';

            // Preparar los datos para enviar al servidor
            const formData = new FormData();
            formData.append('jsonData', jsonData);
            formData.append('apiKey', document.getElementById('apiToken').value);

            // Realizar la solicitud POST al script PHP en el servidor
            fetch('enviar_dte.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Ocultar indicador de carga
                document.getElementById('loadingIndicator').style.display = 'none';

                if (data.error) {
                    // Mostrar mensaje de error con detalles
                    const responseElement = document.getElementById('apiResponse');
                    let errorHtml = `
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <p>${data.error}</p>
                        </div>
                    `;

                    // Añadir detalles del error si están disponibles
                    if (data.detalles) {
                        errorHtml += `
                            <div class="error-details">
                                <h4>Detalles del error:</h4>
                                <pre>${JSON.stringify(data.detalles, null, 2)}</pre>
                            </div>
                        `;
                    }

                    // Añadir información sobre la respuesta de la API
                    if (data.respuesta_api_tamano) {
                        errorHtml += `
                            <div class="api-response-info">
                                <h4>Información de la respuesta API:</h4>
                                <p>Tamaño: ${data.respuesta_api_tamano} bytes</p>
                                <p>¿Es XML? ${data.respuesta_api_es_xml ? 'Sí' : 'No'}</p>
                                <p>Inicio: <code>${data.respuesta_api_inicio}</code></p>
                            </div>
                        `;
                    }

                    responseElement.innerHTML = errorHtml;
                    document.getElementById('responseContainer').style.display = 'block';
                    return;
                }

                // Mostrar mensaje de éxito
                const responseElement = document.getElementById('apiResponse');
                let successMessage = `
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <p>El DTE ha sido procesado correctamente</p>
                        <ul>
                            <li><strong>Archivo XML guardado:</strong> ${data.archivo}</li>
                            <li><strong>Ruta XML:</strong> ${data.ruta}</li>
                            ${data.pdf && data.pdf.success ? `
                            <li><strong>Archivo PDF generado:</strong> ${data.pdf.archivo_pdf}</li>
                            <li><strong>Ruta PDF:</strong> ${data.pdf.ruta_pdf}</li>
                            <li><strong>Tamaño PDF:</strong> ${(data.pdf.tamaño_pdf / 1024).toFixed(2)} KB</li>
                            ` : '<li><strong>PDF:</strong> No se generó el archivo PDF</li>'}
                        </ul>
                        <p>El documento ha sido registrado en la base de datos.</p>
                `;

                // Añadir información del receptor si está disponible
                if (data.receptor) {
                    successMessage += `
                        <p>Información del receptor ${data.receptor.accion} correctamente:</p>
                        <ul>
                            <li><strong>ID:</strong> ${data.receptor.id}</li>
                            <li><strong>RUT:</strong> ${data.receptor.rut}</li>
                        </ul>
                    `;
                }

                if (data.receptor_error) {
                    successMessage += `
                        <p class="warning">Advertencia: No se pudo guardar la información del receptor: ${data.receptor_error}</p>
                    `;
                }

                successMessage += `</div>`;
                responseElement.innerHTML = successMessage;

                // Mostrar el contenedor de respuesta
                document.getElementById('responseContainer').style.display = 'block';

                // NUEVO: Si se generó el PDF correctamente, mostrar y configurar el botón de impresión
                if (data.pdf && data.pdf.success) {
                    const printPdfBtn = document.getElementById('printPdfBtn');
                    printPdfBtn.style.display = 'inline-block';

                    // Almacenar la ruta del PDF en un atributo data para usarlo al hacer clic
                    printPdfBtn.dataset.pdfPath = data.pdf.ruta_pdf;

                    // Hacer scroll hacia el botón para asegurar que sea visible
                    printPdfBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // NUEVO: Guardar los productos del DTE en la base de datos
                if (data.dte_id) {
                    // Guardar los productos del DTE
                    guardarProductosDTE(data.dte_id)
                        .then(result => {
                            if (result.success) {
                                // Mostrar mensaje de éxito
                                responseElement.innerHTML += `
                                    <div class="info-message" style="margin-top: 15px;">
                                        <i class="fas fa-check-circle"></i>
                                        <p>${result.message}</p>
                                    </div>
                                `;
                            } else {
                                // Mostrar mensaje de error
                                responseElement.innerHTML += `
                                    <div class="warning-message" style="margin-top: 15px;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <p>${result.message}</p>
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('Error al guardar los productos:', error);
                            responseElement.innerHTML += `
                                <div class="warning-message" style="margin-top: 15px;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <p>Error al guardar los productos: ${error.message}</p>
                                </div>
                            `;
                        });
                }

                // NUEVO: Tras envío exitoso, actualizar el folio automáticamente
                // Obtener el tipo de documento actual
                const tipoDocumento = document.getElementById('tipoDTE').value;

                // Llamar a la función fetchFolio para obtener el siguiente folio disponible
                fetchFolio();

                // Mostrar mensaje adicional sobre la actualización del folio
                responseElement.innerHTML += `
                    <div class="info-message" style="margin-top: 15px;">
                        <i class="fas fa-info-circle"></i>
                        <p>Actualizando al siguiente folio disponible...</p>
                    </div>
                `;
            })
            .catch(error => {
                // Ocultar indicador de carga
                document.getElementById('loadingIndicator').style.display = 'none';

                // Mostrar el error
                const responseElement = document.getElementById('apiResponse');
                responseElement.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error al enviar el DTE: ${error.message}</p>
                    </div>
                `;

                // Mostrar el contenedor de respuesta
                document.getElementById('responseContainer').style.display = 'block';

                console.error('Error al enviar el DTE:', error);
            });
        }

        // Función para actualizar botones después de generar JSON
        function actualizarBotonesPostJSON() {
            // Mostrar el contenedor de envío
            document.getElementById('enviarDTEContainer').style.display = 'block';

            // Hacer scroll hasta el botón de envío
            document.getElementById('enviarDTEBtn').scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Función para manejar el checkbox de Descuentos y Recargos
        function toggleDescuentosRecargos() {
    const checkbox = document.getElementById('enableDescuentosCheckbox');
    const fieldsContainer = document.getElementById('descuentosRecargosFields');

    if (!fieldsContainer) return; // Safety check

    // Visual indication of section state
    if (checkbox.checked) {
        fieldsContainer.classList.remove('section-disabled');
    } else {
        fieldsContainer.classList.add('section-disabled');
    }

    // Enable/disable all input and select fields
    const fields = fieldsContainer.querySelectorAll('input, select');
    fields.forEach(field => {
        field.disabled = !checkbox.checked;
    });
}

        // Modificar la función generarJSONFactura para excluir Descuentos y Recargos si está deshabilitado
        function generarJSONFactura() {
            // Obtener los valores del formulario
            const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
            const folio = parseInt(document.getElementById('folioDTE').value);
            const fechaEmision = document.getElementById('fechaEmision').value;
            let fechaVencimiento = document.getElementById('fechaVencimiento').value;
            const formaPago = parseInt(document.getElementById('formaPago').value);

            // Validar que la fecha de vencimiento no sea menor a la fecha de emisión
            const fechaEmisionObj = new Date(fechaEmision);
            const fechaVencimientoObj = new Date(fechaVencimiento);

            if (fechaVencimientoObj < fechaEmisionObj) {
                console.warn('La fecha de vencimiento es menor a la fecha de emisión. Ajustando a la fecha de emisión.');
                fechaVencimiento = fechaEmision;
                document.getElementById('fechaVencimiento').value = fechaEmision;
            }

            // Datos del emisor
            const rutEmisor = document.getElementById('rutEmisor').value;
            const razonSocialEmisor = document.getElementById('razonSocialEmisor').value;
            const giroEmisor = document.getElementById('giroEmisor').value;
            const direccionEmisor = document.getElementById('direccionEmisor').value;
            const comunaEmisor = document.getElementById('comunaEmisor').value;
            const ciudadEmisor = document.getElementById('ciudadEmisor').value;

            // Datos del receptor
            const rutReceptor = document.getElementById('rutReceptor').value;
            const razonSocialReceptor = document.getElementById('razonSocialReceptor').value;
            const direccionReceptor = document.getElementById('direccionReceptor').value;
            const comunaReceptor = document.getElementById('comunaReceptor').value;
            const giroReceptor = document.getElementById('giroReceptor').value;
            const contactoReceptor = document.getElementById('contactoReceptor').value;

            // Datos de totales
            const montoNeto = parseInt(document.getElementById('montoNeto').value);
            const tasaIVA = parseInt(document.getElementById('tasaIVA').value);
            const iva = parseInt(document.getElementById('ivaCalculado').value);
            const montoTotal = parseInt(document.getElementById('montoTotal').value);

            // Verificar si los descuentos/recargos están habilitados
            const descuentosHabilitados = document.getElementById('enableDescuentosCheckbox').checked;

            // Datos de descuentos/recargos (solo si están habilitados)
            let tipoMov = '';
            let descTipoValor = '';
            let descValor = 0;
            let descDescripcion = '';

            if (descuentosHabilitados) {
                tipoMov = document.getElementById('tipoMov').value;
                descTipoValor = document.getElementById('descTipoValor').value;
                descValor = parseInt(document.getElementById('descValor').value) || 0;
                descDescripcion = document.getElementById('descDescripcion').value;
            }

            // Datos del certificado
            const rutCertificado = document.getElementById('rutCertificado').value;
            const passwordCertificado = document.getElementById('passwordCertificado').value;

            // Crear array de detalles (items)
            const detalles = [];
            document.querySelectorAll('.item-row').forEach((row, index) => {
                const nombre = row.querySelector('.item-nombre').value;
                const descripcion = row.querySelector('.item-descripcion').value;
                const cantidad = parseFloat(row.querySelector('.item-cantidad').value);
                const unidadMedida = row.querySelector('.item-unidad').value;
                const precio = parseFloat(row.querySelector('.item-precio').value);
                const descuento = parseFloat(row.querySelector('.item-descuento').value) || 0;
                const recargo = parseFloat(row.querySelector('.item-recargo').value) || 0;
                const montoItem = parseInt(row.querySelector('.item-monto').value);

                detalles.push({
                    "IndicadorExento": 0,
                    "Nombre": nombre,
                    "Descripcion": descripcion,
                    "Cantidad": cantidad,
                    "UnidadMedida": unidadMedida,
                    "Precio": precio,
                    "Descuento": descuento,
                    "Recargo": recargo,
                    "MontoItem": montoItem
                });
            });

            // Construir el objeto JSON
            const jsonObj = {
                "Documento": {
                    "Encabezado": {
                        "IdentificacionDTE": {
                            "TipoDTE": tipoDTE,
                            "Folio": folio,
                            "FechaEmision": fechaEmision,
                            "FechaVencimiento": fechaVencimiento, // Siempre incluir FechaVencimiento
                            ...(tipoDTE !== 61 ? { "FormaPago": formaPago } : {}) // Incluir FormaPago solo si NO es Nota de Crédito
                        },
                        "Emisor": {
                            "Rut": rutEmisor,
                            "RazonSocial": razonSocialEmisor,
                            "Giro": giroEmisor,
                            "ActividadEconomica": [620200, 631100],
                            "DireccionOrigen": direccionEmisor,
                            "ComunaOrigen": comunaEmisor,
                            "Telefono": []
                        },
                        "Receptor": {
                            "Rut": rutReceptor,
                            "RazonSocial": razonSocialReceptor,
                            "Direccion": direccionReceptor,
                            "Comuna": comunaReceptor,
                            "Giro": giroReceptor,
                            "Ciudad": comunaReceptor // Usar la comuna como ciudad
                        },
                        "RutSolicitante": "",
                        "Transporte": null,
                        "Totales": {
                            "MontoNeto": montoNeto,
                            "MontoExento": 0, // Agregar MontoExento con valor 0
                            "TasaIVA": tasaIVA,
                            "IVA": iva,
                            "MontoTotal": montoTotal
                        }
                    },
                    "Detalles": detalles,
                    "Referencias": []
                },
                "Certificado": {
                    "Rut": rutCertificado,
                    "Password": passwordCertificado
                }
            };

            // Incluir DescuentosRecargos solo si están habilitados
            if (descuentosHabilitados && tipoMov && descTipoValor) {
                jsonObj.Documento.DescuentosRecargos = [
                    {
                        "TipoMovimiento": tipoMov,
                        "Descripcion": descDescripcion,
                        "TipoValor": descTipoValor,
                        "Valor": descValor
                    }
                ];
            }

            // Add special handling for Nota de Crédito (61)
            if (tipoDTE === 61) {
                // Obtener el tipo de documento de referencia
                const tipoDocRef = parseInt(document.getElementById('tipoDocRef').value);
                console.log('Generando JSON para Nota de Crédito con referencia a documento tipo:', tipoDocRef);

                jsonObj.Documento.Referencias = [{
                    FechaDocumentoReferencia: document.getElementById('fechaDocRef').value,
                    TipoDocumento: tipoDocRef, // Usar el tipo seleccionado en el dropdown
                    FolioReferencia: parseInt(document.getElementById('folioRef').value),
                    CodigoReferencia: parseInt(document.getElementById('codigoRef').value),
                    RazonReferencia: document.getElementById('razonRef').value
                }];
            }

            return jsonObj;
        }

        document.addEventListener('DOMContentLoaded', function() {
    // Función para manejar el checkbox de Descuentos y Recargos
    window.toggleDescuentosRecargos = function() {
        const checkbox = document.getElementById('enableDescuentosCheckbox');
        const fieldsContainer = document.getElementById('descuentosRecargosFields');

        // Alternar la clase visual para indicar el estado de la sección
        if (checkbox.checked) {
            fieldsContainer.classList.remove('section-disabled');
        } else {
            fieldsContainer.classList.add('section-disabled');
        }

        // Habilitar o deshabilitar los campos del contenedor
        const fields = fieldsContainer.querySelectorAll('select, input');
        fields.forEach(field => {
            // Asegurarse de no deshabilitar el propio checkbox
            if (field.id !== 'enableDescuentosCheckbox') {
                field.disabled = !checkbox.checked;
            }
        });
    };

    // Desmarcar el checkbox por defecto y desactivar los campos inicialmente
    const checkbox = document.getElementById('enableDescuentosCheckbox');
    if (checkbox) {
        checkbox.checked = false;
        toggleDescuentosRecargos();
    }

    // Agregar estilos para sección deshabilitada
    const style = document.createElement('style');
    style.textContent = `
        .section-disabled {
            opacity: 0.6;
            pointer-events: none;
        }
        .section-disabled input, .section-disabled select {
            background-color: #f0f0f0;
        }
        .checkbox-container {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .checkbox-label {
            margin-left: 5px;
            font-size: 0.9em;
        }
    `;
    document.head.appendChild(style);

    // Asociar evento al botón de enviar DTE
    const enviarDTEBtn = document.getElementById('enviarDTEBtn');
    if (enviarDTEBtn) {
        enviarDTEBtn.addEventListener('click', enviarDTE);
    }

    // Modificar la función generarJSON existente
    const originalGenerarJSON = window.generarJSON;
    if (typeof originalGenerarJSON === 'function') {
        window.generarJSON = function() {
            // Llamar a la función original
            const result = originalGenerarJSON.apply(this, arguments);

            // Actualizar los botones después de generar el JSON
            actualizarBotonesPostJSON();

            return result;
        };
    }
});

// Función para generar JSON y enviar directamente
function generateAndSendDirect() {
    console.log("--- generateAndSendDirect: Inicio de la función ---"); // Log inicio

     // Validar el formulario primero
     const validacion = validarFormularioDTE();
     if (!validacion.esValido) {
         alert('Por favor complete los siguientes campos obligatorios:\n\n- ' + validacion.camposFaltantes.join('\n- '));
         console.log("--- generateAndSendDirect: Validación fallida ---", validacion.camposFaltantes);
         return;
     }

     // Validación específica para Nota de Crédito (61)
     let tipoDTE = parseInt(document.getElementById('tipoDTE').value);
     if (tipoDTE === 61) {
         console.log("--- generateAndSendDirect: Validando campos específicos para Nota de Crédito ---");

         // Validar campos de referencia
         const fechaDocRef = document.getElementById('fechaDocRef').value;
         const tipoDocRef = document.getElementById('tipoDocRef').value;
         const folioRef = document.getElementById('folioRef').value;
         const codigoRef = document.getElementById('codigoRef').value;
         const razonRef = document.getElementById('razonRef').value;

         // Verificar la fecha de vencimiento para Notas de Crédito
         const fechaEmision = document.getElementById('fechaEmision').value;
         let fechaVencimiento = document.getElementById('fechaVencimiento').value;

         // Si la fecha de vencimiento está vacía, establecerla igual a la fecha de emisión
         if (!fechaVencimiento) {
             console.log("--- generateAndSendDirect: Fecha de vencimiento no establecida para Nota de Crédito. Estableciendo igual a la fecha de emisión ---");
             fechaVencimiento = fechaEmision;
             document.getElementById('fechaVencimiento').value = fechaEmision;
         }

         // Validar que la fecha de vencimiento no sea menor a la fecha de emisión
         const fechaEmisionObj = new Date(fechaEmision);
         const fechaVencimientoObj = new Date(fechaVencimiento);

         if (fechaVencimientoObj < fechaEmisionObj) {
             console.warn("--- generateAndSendDirect: La fecha de vencimiento es menor a la fecha de emisión. Ajustando a la fecha de emisión ---");
             fechaVencimiento = fechaEmision;
             document.getElementById('fechaVencimiento').value = fechaEmision;
         }

         if (!fechaDocRef || !tipoDocRef || !folioRef || !codigoRef || !razonRef) {
             let emptyFields = [];
             if (!fechaDocRef) emptyFields.push('Fecha Documento Referencia');
             if (!tipoDocRef) emptyFields.push('Tipo Documento Referencia');
             if (!folioRef) emptyFields.push('Folio Referencia');
             if (!codigoRef) emptyFields.push('Código Referencia');
             if (!razonRef) emptyFields.push('Razón Referencia');

             alert('Para generar una Nota de Crédito, debe completar todos los campos de referencia. Faltan los siguientes campos: ' +
                   emptyFields.join(', '));
             console.log("--- generateAndSendDirect: Validación de Nota de Crédito fallida ---", emptyFields);
             return; // Detener ejecución
         }

         console.log("--- generateAndSendDirect: Validación de Nota de Crédito exitosa ---");
     }

     // Continuar con la solicitud de confirmación solo si la validación pasó
     if (!confirm('¿Está seguro que desea generar y enviar el DTE directamente?')) {
         console.log("--- generateAndSendDirect: Usuario canceló la operación ---");
         return;
     }

    console.log("--- generateAndSendDirect: Confirmación aceptada ---"); // Log confirmación

    try {
        console.log("--- generateAndSendDirect: Verificando tipo de DTE ---");
        const tipoDTEElement = document.getElementById('tipoDTE');
        if (!tipoDTEElement) {
            console.error("--- generateAndSendDirect: ERROR - Elemento tipoDTE no encontrado ---");
            alert("Error crítico: No se encontró el selector de tipo de DTE.");
            return;
        }
        // Verificar que el valor de tipoDTE no ha cambiado
        if (tipoDTE !== parseInt(tipoDTEElement.value)) {
            console.warn("--- generateAndSendDirect: El valor de tipoDTE ha cambiado desde la validación inicial ---");
            // Actualizar el valor de tipoDTE
            tipoDTE = parseInt(tipoDTEElement.value);
        }
        console.log("--- generateAndSendDirect: Tipo DTE seleccionado:", tipoDTE);

        // Generar JSON según el tipo de documento
        let dteJSON;
        if (tipoDTE === 39) { // Boleta
            console.log("--- generateAndSendDirect: Llamando a generarJSONBoleta ---");
            dteJSON = generarJSONBoleta();
            console.log("--- generateAndSendDirect: JSON Boleta generado:", dteJSON);
        } else { // Factura u otros documentos (33, 34, 61)
            console.log("--- generateAndSendDirect: Llamando a generarJSONFactura ---");
            dteJSON = generarJSONFactura();
            console.log("--- generateAndSendDirect: JSON Factura/Otro generado:", dteJSON);
        }

        if (!dteJSON) {
             console.error("--- generateAndSendDirect: ERROR - El JSON generado es nulo o indefinido ---");
             alert("Error: No se pudo generar el objeto JSON del DTE.");
             return;
        }

        // Convertir a string para envío
        const jsonData = JSON.stringify(dteJSON, null, 2);
        console.log("--- generateAndSendDirect: JSON convertido a string:", jsonData);

        // Verificar descuentos habilitados (igual que en generarJSON)
        const enableDescuentosCheckbox = document.getElementById('enableDescuentosCheckbox');
        if (!enableDescuentosCheckbox) {
            console.warn("--- generateAndSendDirect: ADVERTENCIA - Checkbox de descuentos no encontrado ---");
        }
        const descuentosHabilitados = enableDescuentosCheckbox ? enableDescuentosCheckbox.checked : false;
        console.log("--- generateAndSendDirect: Descuentos habilitados:", descuentosHabilitados);

        if (descuentosHabilitados) {
            console.log("--- generateAndSendDirect: Validando campos de descuentos ---");
            const tipoMov = document.getElementById('tipoMov')?.value;
            const descTipoValor = document.getElementById('descTipoValor')?.value;
            const descValor = document.getElementById('descValor')?.value;
            const descDescripcion = document.getElementById('descDescripcion')?.value;

            // Validar campos de descuentos
            if (!tipoMov || !descTipoValor || !descValor || !descDescripcion) {
                let emptyFields = [];
                if (!tipoMov) emptyFields.push('Tipo');
                if (!descTipoValor) emptyFields.push('Tipo de Valor');
                if (!descValor) emptyFields.push('Valor');
                if (!descDescripcion) emptyFields.push('Descripción');

                console.error("--- generateAndSendDirect: ERROR - Validación de descuentos falló. Campos faltantes:", emptyFields);
                alert('La sección de Descuentos y Recargos está habilitada, pero faltan los siguientes campos: ' +
                      emptyFields.join(', ') + '.\n' +
                      'Por favor complete todos los campos o desactive la sección.');
                return; // Detener si hay campos faltantes
            }

            if (parseFloat(descValor) === 0) {
                console.error("--- generateAndSendDirect: ERROR - Valor de descuento es cero ---");
                alert('El campo "Valor" en Descuentos y Recargos no puede ser cero cuando la sección está habilitada.');
                return;
            }
            console.log("--- generateAndSendDirect: Validación de descuentos superada ---");
        }

        console.log("--- generateAndSendDirect: Mostrando indicador de carga ---");
        // Mostrar indicador de carga
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        } else {
            console.error("--- generateAndSendDirect: ERROR - Elemento loadingIndicator no encontrado ---");
        }

        // const responseContainer = document.getElementById('responseContainer');
        // if (responseContainer) {
        //     responseContainer.style.display = 'none';
        // } else {
        //     console.error("--- generateAndSendDirect: ERROR - Elemento responseContainer no encontrado ---");
        // }

        // Preparar los datos para enviar al servidor
        console.log("--- generateAndSendDirect: Preparando FormData ---");
        const formData = new FormData();
        formData.append('jsonData', jsonData);

        const apiKey = '2037-N680-6391-2493-5987';
        formData.append('apiKey', apiKey);

        // // Hacer visible el contenedor de envío (si es necesario)
        // const enviarDTEContainer = document.getElementById('enviarDTEContainer');
        // if (enviarDTEContainer) {
        //     enviarDTEContainer.style.display = 'block';
        //     console.log("--- generateAndSendDirect: Contenedor enviarDTEContainer visible ---");
        // } else {
        //     console.error("--- generateAndSendDirect: ERROR - Elemento enviarDTEContainer no encontrado ---");
        // }

        console.log("--- generateAndSendDirect: Enviando solicitud fetch a enviar_dte.php ---");

        // Para Notas de Crédito, mostrar información adicional
        if (tipoDTE === 61) {
            console.log("--- generateAndSendDirect: Enviando Nota de Crédito (61) ---");
            console.log("--- generateAndSendDirect: Datos de referencia ---");
            console.log("- Fecha Doc Ref:", document.getElementById('fechaDocRef').value);
            console.log("- Tipo Doc Ref:", document.getElementById('tipoDocRef').value);
            console.log("- Folio Ref:", document.getElementById('folioRef').value);
            console.log("- Código Ref:", document.getElementById('codigoRef').value);
            console.log("- Razón Ref:", document.getElementById('razonRef').value);
        }

        // Enviar directamente al servidor
        fetch('enviar_dte.php', {
            method: 'POST',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            body: formData
        })
        .then(response => {
            console.log("--- generateAndSendDirect: Respuesta fetch recibida. Status:", response.status);
            if (!response.ok) {
                 console.error("--- generateAndSendDirect: ERROR - Respuesta fetch no OK. Status:", response.statusText);
                 // Intenta leer el cuerpo de la respuesta aunque no sea JSON para más detalles
                 return response.text().then(text => {
                     throw new Error(`Error en la respuesta del servidor: ${response.status} ${response.statusText}. Respuesta: ${text}`);
                 });
            }
            console.log("--- generateAndSendDirect: Procesando respuesta JSON ---");
            return response.json();
        })
        .then(data => {
            console.log("--- generateAndSendDirect: Datos JSON procesados:", data);
            // Ocultar indicador de carga
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            const responseElement = document.getElementById('apiResponse');
            if (!responseElement) {
                console.error("--- generateAndSendDirect: ERROR - Elemento apiResponse no encontrado ---");
                alert("Error crítico: No se pudo mostrar la respuesta.");
                return;
            }

            // Verificar si se ha alcanzado el límite de folios
            if (data.lastFolio === true || data.no_folios_disponibles === true || (data.error && data.error.includes('límite del rango de folio'))) {
                console.warn("--- generateAndSendDirect: ALERTA - Se ha alcanzado el límite de folios ---");

                // Mostrar alerta al usuario
                let mensajeAlerta = "¡ATENCIÓN! Se ha alcanzado el límite del rango de folios para este tipo de documento.";

                // Si es una nota de crédito y se generó el PDF a pesar del límite de folios
                if (data.no_folios_disponibles === true && data.pdf_generado === true) {
                    mensajeAlerta += "\n\nSin embargo, se ha generado el PDF correctamente.";
                }

                // Usar el valor de tipoDTE que ya tenemos para la solicitud automática de folios
                console.log("--- generateAndSendDirect: Tipo DTE para solicitud automática de folios:", tipoDTE);

                // Llamar a la función de solicitud automática de folios
                if (typeof window.handleFolioLimitMessage === 'function') {
                    console.log("--- generateAndSendDirect: Llamando a handleFolioLimitMessage ---");
                    window.handleFolioLimitMessage(tipoDTE);
                } else {
                    console.error("--- generateAndSendDirect: Función handleFolioLimitMessage no encontrada ---");
                }

                mensajeAlerta += "\n\nPor favor, solicite un nuevo rango de folios antes de continuar.";

                alert(mensajeAlerta);

                // Si hay un mensaje de advertencia específico, mostrarlo también
                if (data.warning) {
                    console.warn("--- generateAndSendDirect: Mensaje de advertencia:", data.warning);
                }
            }

            if (data.error) {
                console.error("--- generateAndSendDirect: Error recibido desde el servidor:", data.error, "Detalles:", data.detalles);

                // Verificar si es un error específico para Nota de Crédito
                if (tipoDTE === 61) {
                    console.error("--- generateAndSendDirect: Error en Nota de Crédito ---");

                    // Verificar si el error está relacionado con folios
                    if (data.error.includes('folios') || data.error.includes('Folio') || data.error.includes('CAF')) {
                        console.error("--- generateAndSendDirect: Error relacionado con folios para Nota de Crédito ---");

                        // Mostrar mensaje específico para error de folios en Nota de Crédito
                        alert('Error al generar Nota de Crédito: ' + data.error + '\n\n' +
                              'Verifique que existan folios CAF activos para Notas de Crédito (tipo 61) en la tabla folios_caf.');

                        // Sugerir solicitar folios CAF
                        if (confirm('¿Desea solicitar folios CAF para Notas de Crédito ahora?')) {
                            console.log("--- generateAndSendDirect: Usuario confirmó solicitud de folios CAF para Nota de Crédito ---");

                            // Llamar a la función de solicitud de folios si existe
                            if (typeof window.solicitarFoliosCaf === 'function') {
                                window.solicitarFoliosCaf(61);
                            } else {
                                alert('La función para solicitar folios CAF no está disponible. Por favor, solicite los folios manualmente.');
                            }
                        }

                        return;
                    }
                }

                // Verificar si es un error de documento duplicado
                if (data.tipo_error === 'duplicado') {
                    console.log("--- generateAndSendDirect: Detectado error de documento duplicado ---");

                    // Construir un mensaje de error más amigable para duplicados
                    let errorHtml = `
                        <div class="error-message" style="background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
                            <h4 style="margin-top: 0;">Documento Duplicado</h4>
                            <p>${data.mensaje}</p>
                            <p>No se puede generar un documento con el mismo tipo y folio que ya existe en la base de datos.</p>
                            ${data.detalles ? `<p><strong>Detalles:</strong> ${data.detalles}</p>` : ''}
                            <p style="margin-top: 15px;">Sugerencias:</p>
                            <ul>
                                <li>Verifique si el documento ya fue generado anteriormente.</li>
                                <li>Si necesita generar un nuevo documento, utilice un folio diferente.</li>
                            </ul>
                        </div>
                    `;

                    responseElement.innerHTML = errorHtml;
                } else {
                    // Construir el HTML para otros tipos de errores
                    let errorHtml = `
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <p>${data.error}</p>
                        </div>
                    `;

                    // Añadir detalles del error si están disponibles
                    if (data.detalles) {
                        errorHtml += `
                            <div class="error-details">
                                <h4>Detalles del error:</h4>
                                <pre>${JSON.stringify(data.detalles, null, 2)}</pre>
                            </div>
                        `;
                    }

                    responseElement.innerHTML = errorHtml;
                }

                if (responseContainer) {
                    responseContainer.style.display = 'block';
                }
                return;
            }

            console.log("--- generateAndSendDirect: Éxito en el envío. Mostrando mensaje ---");

            // Verificar si es una nota de crédito para agregar botón de limpiar formulario
            // Usar el valor de tipoDTE que ya tenemos
            const esNotaCredito = tipoDTE === 61;
            const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

            // Mostrar mensaje de éxito en el contenedor de respuesta
            let successMessage = `
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <p>${data.no_folios_disponibles ? 'El DTE ha sido procesado pero se ha alcanzado el límite de folios' : 'El DTE ha sido procesado correctamente'}</p>
                    ${data.no_folios_disponibles ? `
                    <div class="warning-box" style="background-color: #fff3e0; border: 1px solid #ff9800; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <i class="fas fa-exclamation-triangle" style="color: #ff9800;"></i>
                        <span>Se ha alcanzado el límite de folios para este tipo de documento.</span>
                        ${data.folios_solicitados ? `
                        <div style="margin-top: 8px; background-color: #e8f5e9; border: 1px solid #4caf50; padding: 8px; border-radius: 4px;">
                            <i class="fas fa-check" style="color: #4caf50;"></i>
                            <span>Se han solicitado automáticamente nuevos folios. Rango: ${data.folios_info.rango_inicial} - ${data.folios_info.rango_final}</span>
                        </div>
                        ` : `
                        <div style="margin-top: 8px;">
                            <span>Por favor, solicite nuevos folios manualmente.</span>
                        </div>
                        `}
                    </div>
                    ` : ''}
                    <ul>
                        <li><strong>Archivo XML guardado:</strong> ${data.archivo}</li>
                        <li><strong>Ruta XML:</strong> ${data.ruta}</li>
                        ${data.pdf_generado === true ? `
                        <li><strong>Archivo PDF generado:</strong> ${data.pdf.archivo_pdf}</li>
                        <li><strong>Tamaño PDF:</strong> ${(data.pdf.tamaño_pdf / 1024).toFixed(2)} KB</li>
                        ` : `<li><strong>PDF:</strong> <span style="color: #e74c3c;">No se generó el archivo PDF</span></li>`}
                    </ul>
                    <p>El documento ha sido registrado en la base de datos.</p>
                </div>
            `;

            // Si hubo un error en la generación del PDF pero el DTE se procesó correctamente, mostrar advertencia
            if (data.pdf_generado === false && data.pdf_error) {
                successMessage += `
                <div class="warning-message" style="margin-top: 15px; padding: 10px; background-color: #fff3e0; border-left: 4px solid #ff9800; border-radius: 4px;">
                    <i class="fas fa-exclamation-triangle" style="color: #ff9800;"></i>
                    <p><strong>Advertencia:</strong> El documento DTE se procesó correctamente, pero hubo un problema al generar el PDF.</p>
                    <p><strong>Error:</strong> ${data.pdf_error}</p>
                    ${data.pdf_detalles ? `<p><strong>Detalles:</strong> ${data.pdf_detalles}</p>` : ''}
                    <p>Puede intentar generar el PDF manualmente más tarde.</p>
                </div>
                `;
            }

            responseElement.innerHTML = successMessage;

            if (responseContainer) {
                responseContainer.style.display = 'block';
            }

            // Agregar botón para limpiar formulario si es una nota de crédito
            if (esNotaCredito && formularioPoblado) {
                // Agregar botón de limpiar formulario
                const limpiarBtn = document.createElement('button');
                limpiarBtn.id = 'limpiarFormularioBtn';
                limpiarBtn.className = 'json-btn';
                limpiarBtn.style.backgroundColor = '#3498db';
                limpiarBtn.style.marginTop = '20px';
                limpiarBtn.style.display = 'block';
                limpiarBtn.style.width = '100%';
                limpiarBtn.innerHTML = '<i class="fas fa-broom"></i> Limpiar formulario y desbloquear campos';

                // Agregar el botón al contenedor de respuesta
                responseElement.appendChild(limpiarBtn);

                // Configurar el evento del botón
                limpiarBtn.addEventListener('click', function() {
                    console.log('Botón limpiar formulario clickeado');

                    // Desbloquear campos
                    if (typeof window.desbloquearCamposFormulario === 'function') {
                        window.desbloquearCamposFormulario();
                        console.log('Campos desbloqueados');
                    } else {
                        console.error('Función desbloquearCamposFormulario no encontrada');
                    }

                    // Limpiar formulario
                    if (typeof window.limpiarFormulario === 'function') {
                        window.limpiarFormulario();
                        console.log('Formulario limpiado');
                    } else {
                        console.error('Función limpiarFormulario no encontrada');
                    }

                    // Mostrar mensaje
                    if (typeof window.showNotification === 'function') {
                        window.showNotification('Formulario limpiado y campos desbloqueados correctamente', 'success');
                    } else {
                        alert('Formulario limpiado y campos desbloqueados correctamente');
                    }

                    // Ocultar el botón
                    this.style.display = 'none';
                });
            }

            // Configurar el botón de impresión utilizando nuestra función
            if (data.pdf_generado === true && data.pdf) {
                console.log("--- generateAndSendDirect: Configurando botón de impresión PDF ---");
                setupPrintPdfButton(data.pdf);
            } else {
                console.log("--- generateAndSendDirect: No se configura botón de impresión porque no se generó PDF ---");
                // Ocultar el botón de impresión si existe
                const printPdfBtn = document.getElementById('printPdfBtn');
                if (printPdfBtn) {
                    printPdfBtn.style.display = 'none';
                }
            }

            // Guardar los productos del DTE en la base de datos
            if (data.dte_id) {
                console.log("--- generateAndSendDirect: Guardando productos del DTE. ID:", data.dte_id);
                // Guardar los productos del DTE
                guardarProductosDTE(data.dte_id)
                    .then(result => {
                        if (result.success) {
                            // Mostrar mensaje de éxito
                            console.log("--- generateAndSendDirect: Productos guardados correctamente:", result);
                            responseElement.innerHTML += `
                                <div class="info-message" style="margin-top: 15px;">
                                    <i class="fas fa-check-circle"></i>
                                    <p>${result.message}</p>
                                </div>
                            `;

                            // Limpiar el formulario automáticamente después de guardar los productos
                            console.log("--- generateAndSendDirect: Limpiando formulario automáticamente ---");

                            // Verificar si es una nota de crédito con datos poblados
                            if (esNotaCredito && formularioPoblado) {
                                console.log("--- generateAndSendDirect: Es una nota de crédito con datos poblados, no se limpia automáticamente ---");
                                // No limpiar automáticamente, ya que el usuario debe usar el botón específico
                                // que también desbloquea los campos
                            } else {
                                // Para facturas, boletas o notas de crédito sin datos poblados, limpiar automáticamente
                                if (typeof window.limpiarFormularioDTE === 'function') {
                                    // Usar true para skipConfirmation y no mostrar diálogo de confirmación
                                    window.limpiarFormularioDTE(true);
                                    console.log("--- generateAndSendDirect: Formulario limpiado automáticamente ---");

                                    // Mostrar notificación de limpieza
                                    if (typeof window.showNotification === 'function') {
                                        window.showNotification('Formulario limpiado automáticamente. Puede generar otro documento.', 'success');
                                    }
                                } else {
                                    console.error("--- generateAndSendDirect: Función limpiarFormularioDTE no encontrada ---");
                                }
                            }
                        } else {
                            // Mostrar mensaje de error
                            console.error("--- generateAndSendDirect: Error al guardar productos:", result);
                            responseElement.innerHTML += `
                                <div class="warning-message" style="margin-top: 15px;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <p>${result.message}</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error("--- generateAndSendDirect: Error al guardar los productos:", error);
                        responseElement.innerHTML += `
                            <div class="warning-message" style="margin-top: 15px;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Error al guardar los productos: ${error.message}</p>
                            </div>
                        `;
                    });
            } else {
                console.warn("--- generateAndSendDirect: No se recibió ID de DTE para guardar productos");

                // Aún sin ID de DTE, limpiar el formulario si no es nota de crédito con datos poblados
                if (!(esNotaCredito && formularioPoblado)) {
                    console.log("--- generateAndSendDirect: Limpiando formulario automáticamente (sin ID de DTE) ---");
                    if (typeof window.limpiarFormularioDTE === 'function') {
                        window.limpiarFormularioDTE(true);
                        console.log("--- generateAndSendDirect: Formulario limpiado automáticamente ---");

                        // Mostrar notificación de limpieza
                        if (typeof window.showNotification === 'function') {
                            window.showNotification('Formulario limpiado automáticamente. Puede generar otro documento.', 'success');
                        }
                    } else {
                        console.error("--- generateAndSendDirect: Función limpiarFormularioDTE no encontrada ---");
                    }
                }
            }

            // Actualizar folio automáticamente
            // ... código existente ...
        })
        .catch(error => {
            console.error("--- generateAndSendDirect: ERROR en la petición fetch o procesamiento:", error);

            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            const responseElement = document.getElementById('apiResponse');
            if (responseElement) {
                responseElement.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error al enviar el DTE: ${error.message}</p>
                        <p>Verifique la consola del navegador para más detalles.</p>
                    </div>
                `;
            }

            if (responseContainer) {
                responseContainer.style.display = 'block';
            }

            alert("Error al enviar el DTE: " + error.message);
        });
    } catch (error) {
        console.error("--- generateAndSendDirect: ERROR general en el bloque try...catch:", error);
        alert("Error inesperado al procesar la solicitud: " + error.message);
        // Ocultar indicador de carga si aún está visible
        const loadingIndicator = document.getElementById('loadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }
    console.log("--- generateAndSendDirect: Fin de la función ---"); // Log fin
}

// Asegurarnos de que la función generateAndSendDirect esté correctamente definida y accesible
// (El código existente para asignar el event listener ya está bien, pero lo dejamos para contexto)
document.addEventListener('DOMContentLoaded', function() {
    // ...existing code...

    console.log("¡DOM completamente cargado y listo!"); // Mensaje mejorado

    const generateAndSendBtn = document.getElementById('generateAndSendBtn');
    if (generateAndSendBtn) {
        console.log("Asignando event listener al botón 'generateAndSendBtn'");

        // Eliminar listeners previos si existen (buena práctica)
        const newBtn = generateAndSendBtn.cloneNode(true);
        generateAndSendBtn.parentNode.replaceChild(newBtn, generateAndSendBtn);

        // Agregar el listener al nuevo botón clonado
        newBtn.addEventListener('click', function(e) {
            e.preventDefault(); // Prevenir comportamiento por defecto del botón si es type="submit"
            console.log("--- Event Listener: CLICK detectado en botón Generar y Enviar DTE ---");
            // No poner alert aquí, interrumpe el flujo
            generateAndSendDirect(); // Llamar a la función con logging
        });
    } else {
        console.error("ERROR CRÍTICO: No se encontró el botón con ID 'generateAndSendBtn' en el DOM.");
    }

    // ...existing code...
});

// Añadir esta función para solicitar folios CAF desde Simple API con logging mejorado
function requestFoliosFromAPI() {
    console.log("===== INICIO: SOLICITUD DE FOLIOS CAF =====");
    console.log("Iniciando solicitud de folios CAF...");

    // Obtener el tipo de documento seleccionado
    const tipoDTE = document.getElementById('tipoDTE').value;
    console.log(`Tipo de documento seleccionado: ${tipoDTE}`);

    let folderName;

    // Determinar la carpeta destino según el tipo de documento
    switch (tipoDTE) {
        case "39":
            folderName = "Boletas";
            break;
        case "61":
            folderName = "NotasCredito";
            break;
        case "34":
            folderName = "FacturasExentas";
            break;
        case "33":
        default:
            folderName = "Facturas";
            break;
    }
    console.log(`Carpeta destino seleccionada: ${folderName}`);

    // Crear un nombre de archivo único para los folios
    const timestamp = new Date().getTime();
    const fileName = `folios_${tipoDTE}_${timestamp}.pfx`;
    console.log(`Nombre de archivo generado: ${fileName}`);

    // Preparar los datos para la solicitud
    console.log("Preparando datos para la solicitud...");
    const requestData = {
        rutCertificado: "17365958-K",
        password: "1569",
        rutEmpresa: "78078979-4",
        ambiente: 1,
        tipoDTE: tipoDTE,
        cantidad: 19,  // Solicitar 20 folios
        certificadoPath: "Documents/17365958-K.pfx",
        folderDestino: `Documents/folios/${folderName}`,
        fileName: fileName
    };
    console.log("Datos de solicitud:", requestData);

    // Mostrar la URL y los parámetros antes de enviar la solicitud
    const apiUrl = `https://servicios.simpleapi.cl/api/folios/get/${tipoDTE}/20`;

    // Mostrar información de la solicitud en una ventana de confirmación para revisión
    const requestInfoHTML = `
        <div class="request-info-modal">
            <h3>Información de la solicitud</h3>
            <p><strong>URL:</strong> ${apiUrl}</p>
            <p><strong>Método:</strong> POST</p>
            <h4>Parámetros JSON:</h4>
            <pre>${JSON.stringify({
                RutCertificado: requestData.rutCertificado,
                Password: requestData.password,
                RutEmpresa: requestData.rutEmpresa,
                Ambiente: requestData.ambiente
            }, null, 2)}</pre>
            <h4>Archivos adjuntos:</h4>
            <p>Certificado: ${requestData.certificadoPath}</p>
            <h4>Destino del archivo:</h4>
            <p>Carpeta: ${requestData.folderDestino}</p>
            <p>Nombre: ${requestData.fileName}</p>
        </div>
    `;

    // Crear y mostrar un modal con la información
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;

    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.cssText = `
        background-color: white;
        padding: 20px;
        border-radius: 5px;
        max-width: 80%;
        max-height: 80%;
        overflow-y: auto;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    `;
    modalContent.innerHTML = `
        ${requestInfoHTML}
        <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
            <button id="cancelRequestBtn" style="margin-right: 10px; padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancelar</button>
            <button id="confirmRequestBtn" style="padding: 8px 16px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Continuar con la solicitud</button>
        </div>
    `;

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // Agregar event listeners a los botones
    document.getElementById('cancelRequestBtn').addEventListener('click', function() {
        document.body.removeChild(modalOverlay);
        console.log("Solicitud cancelada por el usuario");
    });

    document.getElementById('confirmRequestBtn').addEventListener('click', function() {
        document.body.removeChild(modalOverlay);
        console.log("Usuario confirmó la solicitud, procediendo...");

        // Continuar con la solicitud original
        proceedWithFoliosRequest(requestData);
    });
}

// Función para continuar con la solicitud después de la confirmación
function proceedWithFoliosRequest(requestData) {
    // Mostrar un indicador de carga
    console.log("Mostrando indicador de carga...");
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
        console.log("Indicador de carga visible");
    } else {
        console.warn("ADVERTENCIA: Elemento 'loadingIndicator' no encontrado en el DOM");
    }

    // Realizar la solicitud al servidor
    console.log("Enviando solicitud fetch a request_folios.php...");
}
    fetch('request_folios.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log(`Respuesta recibida. Status: ${response.status}`);
        if (!response.ok) {
            console.error(`ERROR: Respuesta no ok (${response.status} ${response.statusText})`);
            return response.text().then(text => {
                console.error(`Contenido de la respuesta de error: ${text}`);
                throw new Error(`Error en la respuesta del servidor: ${response.status} ${text}`);
            });
        }
        console.log("Convirtiendo respuesta a JSON...");
        return response.json();
    })
    .then(data => {
        // ... rest of the existing code in the original function ...
        console.log("Datos JSON recibidos:", data);

        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
            console.log("Indicador de carga ocultado");
        }

        // Mostrar el resultado
        const responseContainer = document.getElementById('responseContainer');
        const apiResponse = document.getElementById('apiResponse');

        if (responseContainer && apiResponse) {
            console.log("Mostrando contenedor de respuesta");
            responseContainer.style.display = 'block';

            if (data.success) {
                console.log("Solicitud exitosa. Actualizando UI con mensaje de éxito");
                apiResponse.innerHTML = `
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <p>Archivo PFX guardado correctamente</p>
                        <ul>
                            <li><strong>Tipo de documento:</strong> ${requestData.tipoDTE}</li>
                            <li><strong>Archivo guardado:</strong> ${data.fileName}</li>
                            <li><strong>Ruta completa:</strong> ${data.filePath}</li>
                            <li><strong>Tamaño del archivo:</strong> ${data.fileInfo.size} bytes</li>
                            <li><strong>Fecha de creación:</strong> ${data.fileInfo.created}</li>
                        </ul>
                        <p class="mt-3">El archivo ha sido guardado y está listo para su uso.</p>
                    </div>
                `;
            } else {
                console.error("Error en la solicitud:", data.error);
                apiResponse.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <p>${data.error || 'Error al solicitar folios'}</p>
                        ${data.details ? `<pre class="error-details">${data.details}</pre>` : ''}
                    </div>
                `;
            }
        } else {
            console.warn("ADVERTENCIA: Elementos 'responseContainer' o 'apiResponse' no encontrados en el DOM");
        }
    })
    .catch(error => {
        console.error("ERROR CRÍTICO en la solicitud de folios:", error);

        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
            console.log("Indicador de carga ocultado después del error");
        }

        const responseContainer = document.getElementById('responseContainer');
        const apiResponse = document.getElementById('apiResponse');

        if (responseContainer && apiResponse) {
            console.log("Mostrando contenedor de respuesta con mensaje de error");
            responseContainer.style.display = 'block';
            apiResponse.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error al solicitar folios: ${error.message}</p>
                </div>
            `;
        }
    })
    .finally(() => {
        console.log("===== FIN: SOLICITUD DE FOLIOS CAF =====");
    });


// ... existing code ...

// Función para actualizar la base de datos local de folios (será implementada en el backend)
function updateFoliosInDatabase(tipoDTE, firstFolio, lastFolio, filePath) {
    console.log("Iniciando actualización de folios en base de datos...");

    if (!firstFolio || !lastFolio || !filePath) {
        console.warn("Faltan datos para actualizar la base de datos");
        return;
    }

    const updateData = {
        tipoDTE: tipoDTE,
        firstFolio: firstFolio,
        lastFolio: lastFolio,
        filePath: filePath
    };

    console.log("Datos para actualización:", updateData);

    // Esta solicitud es separada y no bloquea el flujo principal
    fetch('update_folios_db.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
    })
    .then(response => {
        console.log(`Respuesta de actualización DB recibida. Status: ${response.status}`);
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`Error en la actualización: ${response.status} ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log("Actualización de base de datos completada:", data);
        if (data.success) {
            showNotification("Base de datos de folios actualizada correctamente", "info");
        } else {
            showNotification("No se pudo actualizar la base de datos: " + data.error, "error");
        }
    })
    .catch(error => {
        console.error("Error al actualizar base de datos:", error);
        showNotification("Error al actualizar base de datos: " + error.message, "error");
    });
}

// Añadir esta parte al evento DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    // ... código existente ...

    // Asignar el evento al botón de solicitar folios
    const requestFoliosBtn = document.getElementById('requestFoliosBtn');
    if (requestFoliosBtn) {
        requestFoliosBtn.addEventListener('click', function() {
            requestFoliosFromAPI();
        });
    }

    // Asignar evento al botón de imprimir PDF
    const printPdfBtn = document.getElementById('printPdfBtn');
    if (printPdfBtn) {
        printPdfBtn.addEventListener('click', function() {
            const pdfPath = this.dataset.pdfPath;
            if (pdfPath) {
                // Abrir el PDF en una nueva ventana/pestaña para imprimirlo
                const pdfWindow = window.open(pdfPath, '_blank');
                if (pdfWindow) {
                    // En algunos navegadores, podemos activar la impresión automáticamente
                    setTimeout(() => {
                        try {
                            pdfWindow.print();
                        } catch (e) {
                            console.log('No se pudo imprimir automáticamente, pero el PDF está abierto.');
                        }
                    }, 1000);
                } else {
                    alert('El navegador bloqueó la apertura de la ventana. Por favor, permite ventanas emergentes para este sitio.');
                }
            } else {
                alert('No se encontró la ruta del PDF para imprimir.');
            }
        });
    }

    // ... código existente ...
});

// Función para configurar el botón de impresión PDF
function setupPrintPdfButton(pdfData) {
    const printPdfBtn = document.getElementById('printPdfBtn');

    if (!printPdfBtn) {
        console.error("No se encontró el botón de impresión PDF");
        return;
    }

    if (pdfData && pdfData.success) {
        // Mostrar el botón
        printPdfBtn.style.display = 'inline-block';

        // Asegurarse de que la ruta sea relativa a los directorios permitidos
        const pdfPath = pdfData.ruta_pdf.startsWith('/') ?
            pdfData.ruta_pdf.substring(1) : pdfData.ruta_pdf;

        printPdfBtn.dataset.pdfPath = pdfPath;

        console.log('PDF Path configurado:', pdfPath);

        // Añadir el evento click si no existe
        if (!printPdfBtn.hasEventListener) {
            printPdfBtn.addEventListener('click', handlePrintPdfClick);
            printPdfBtn.hasEventListener = true;
        }

        // Hacer scroll hacia el botón para asegurar que sea visible
        printPdfBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
        printPdfBtn.style.display = 'none';
    }
}

// Función para reiniciar el botón de impresión
function resetPrintButton() {
    const printBtn = document.getElementById('printPdfBtn');
    if (printBtn) {
        printBtn.style.display = 'inline-block';
        printBtn.disabled = false;
        printBtn.innerHTML = '<i class="fas fa-print"></i> Imprimir DTE';
        printBtn.removeAttribute('data-pdf-path');
        console.log('Botón de impresión reiniciado para nuevo documento');
    }
}

// Función para manejar el clic en el botón de impresión PDF
function handlePrintPdfClick(event) {
    let pdfPath = event.currentTarget.dataset.pdfPath;
    const button = event.currentTarget;

    if (!pdfPath) {
        console.error('No hay ruta de PDF disponible');
        alert('Error: No se encontró el documento para imprimir.');
        return;
    }

    console.log('Intentando abrir PDF:', pdfPath);

    // Mostrar indicador de carga
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Abriendo PDF...';
    button.disabled = true;

    // Ocultar el botón después de un breve retraso
    const hideButton = () => {
        console.log('Ocultando botón de impresión...');
        button.style.display = 'none';
        button.disabled = true;
        button.innerHTML = originalText;
        
        // Eliminar el atributo pdfPath para evitar reimpresiones
        button.removeAttribute('data-pdf-path');
    };

    // Intentar abrir el PDF en una nueva pestaña
    const pdfWindow = window.open(pdfPath, '_blank');
    
    if (pdfWindow) {
        console.log('Ventana de PDF abierta con éxito');
        
        // Ocultar el botón inmediatamente después de abrir el PDF
        hideButton();
        
        // Intentar imprimir automáticamente después de un breve retraso
        setTimeout(() => {
            try {
                pdfWindow.print();
                console.log('Comando de impresión enviado');
            } catch (e) {
                console.log('No se pudo imprimir automáticamente, pero el PDF está abierto.');
            }
        }, 1000);
        
    } else {
        alert('El navegador bloqueó la apertura de la ventana. Por favor, permite ventanas emergentes para este sitio.');
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Función para limpiar el formulario DTE manteniendo la sección de Identificación
function limpiarFormularioDTE(skipConfirmation = false) {
    // No pedir confirmación cuando se llama desde la impresión
    if (!skipConfirmation && !confirm('¿Está seguro que desea limpiar todos los campos del formulario?')) {
        return;
    }

    console.log('Limpiando formulario DTE, manteniendo Identificación...');

    // Guardar valores de la sección Identificación DTE
    const tipoDTE = document.getElementById('tipoDTE').value;
    const folioDTE = document.getElementById('folioDTE').value;
    const fechaEmision = document.getElementById('fechaEmision').value;
    const fechaVencimiento = document.getElementById('fechaVencimiento').value;
    const formaPago = document.getElementById('formaPago').value;
    const indicadorServicio = document.getElementById('indicadorServicio').value;

    // Limpiar campos del receptor
    document.getElementById('rutReceptor').value = '';
    document.getElementById('razonSocialReceptor').value = '';
    document.getElementById('direccionReceptor').value = '';
    document.getElementById('comunaReceptor').value = '';
    document.getElementById('giroReceptor').value = '';
    document.getElementById('contactoReceptor').value = '';

    // Limpiar campos de referencia (para notas de crédito)
    const referenciasSection = document.getElementById('referenciasSection');
    if (referenciasSection) {
        const fechaDocRef = document.getElementById('fechaDocRef');
        const folioRef = document.getElementById('folioRef');
        const razonRef = document.getElementById('razonRef');

        if (fechaDocRef) fechaDocRef.value = '';
        if (folioRef) folioRef.value = '';
        if (razonRef) razonRef.value = '';
    }

    // Limpiar items/productos
    const itemRows = document.querySelectorAll('.item-row');
    // Mantener solo la primera fila y limpiarla
    if (itemRows.length > 0) {
        // Limpiar la primera fila
        const firstRow = itemRows[0];

        // Verificar que los elementos existan antes de intentar limpiarlos
        if (firstRow.querySelector('.item-nombre')) firstRow.querySelector('.item-nombre').value = '';
        if (firstRow.querySelector('.item-descripcion')) firstRow.querySelector('.item-descripcion').value = '';
        if (firstRow.querySelector('.item-cantidad')) firstRow.querySelector('.item-cantidad').value = '1';
        if (firstRow.querySelector('.item-precio')) firstRow.querySelector('.item-precio').value = '0';
        if (firstRow.querySelector('.item-monto')) firstRow.querySelector('.item-monto').value = '0';

        // Eliminar las filas adicionales
        for (let i = 1; i < itemRows.length; i++) {
            itemRows[i].remove();
        }
    }

    
    // Limpiar montos totales
    document.getElementById('montoNeto').value = '0';
    document.getElementById('ivaCalculado').value = '0';
    document.getElementById('montoTotal').value = '0';

    // Restaurar valores de la sección Identificación DTE
    document.getElementById('tipoDTE').value = tipoDTE;
    document.getElementById('folioDTE').value = folioDTE;
    document.getElementById('fechaEmision').value = fechaEmision;
    document.getElementById('fechaVencimiento').value = fechaVencimiento;
    document.getElementById('formaPago').value = formaPago;
    document.getElementById('indicadorServicio').value = indicadorServicio;

    console.log('Formulario limpiado, manteniendo Identificación DTE');

    // Limpiar montos totales
    document.getElementById('montoNeto').value = '0';
    document.getElementById('ivaCalculado').value = '0';
    document.getElementById('montoTotal').value = '0';

    // Mantener el botón de impresión visible si se está limpiando automáticamente después de generar un documento
    // Si no es limpieza automática, ocultar el botón
    if (!skipConfirmation) {
        const printPdfBtn = document.getElementById('printPdfBtn');
        if (printPdfBtn) {
            printPdfBtn.style.display = 'none';
        }
    }

    // Limpiar contenedor de respuesta (solo si no se está mostrando un mensaje de éxito)
    if (skipConfirmation) {
        // No limpiar el contenedor de respuesta si estamos limpiando automáticamente
        // después de un envío exitoso, para que el usuario pueda ver el mensaje de éxito
    } else {
        const responseContainer = document.getElementById('responseContainer');
        if (responseContainer) {
            responseContainer.style.display = 'none';
        }
    }

    // Mostrar mensaje de confirmación (solo si no se está saltando la confirmación)
    if (!skipConfirmation) {
        const mensaje = document.createElement('div');
        mensaje.className = 'alert alert-success';
        mensaje.style.position = 'fixed';
        mensaje.style.top = '20px';
        mensaje.style.right = '20px';
        mensaje.style.padding = '15px';
        mensaje.style.borderRadius = '5px';
        mensaje.style.backgroundColor = '#d4edda';
        mensaje.style.borderColor = '#c3e6cb';
        mensaje.style.color = '#155724';
        mensaje.style.zIndex = '1000';
        mensaje.innerHTML = '<i class="fas fa-check-circle"></i> Formulario limpiado correctamente';

        document.body.appendChild(mensaje);

        // Eliminar el mensaje después de 3 segundos
        setTimeout(() => {
            mensaje.remove();
        }, 3000);
    }

    console.log('Formulario DTE limpiado correctamente');

    // Hacer que la función esté disponible globalmente
    window.limpiarFormularioDTE = limpiarFormularioDTE;
}

// Función de diagnóstico para ayudar a depurar problemas
function diagnosticarPDF(pdfPath) {
    console.log('=== Diagnóstico de PDF ===');
    console.log('Ruta original:', pdfPath);
    console.log('Ruta normalizada:', '/Documents/PDF_88/' + pdfPath.replace('Documents/PDF_88/', ''));

    fetch(pdfPath, { method: 'HEAD' })
        .then(response => {
            console.log('Estado HTTP:', response.status);
            console.log('Tipo de contenido:', response.headers.get('content-type'));
            console.log('Accesible:', response.ok ? 'Sí' : 'No');
        })
        .catch(error => {
            console.error('Error en diagnóstico:', error);
        });
}

// Función para enviar sobre DTE
document.addEventListener('DOMContentLoaded', function() {
    const enviarSobreBtn = document.getElementById('enviarSobreBtn');

    if (enviarSobreBtn) {
        enviarSobreBtn.addEventListener('click', function() {
            // Crear y mostrar un indicador de carga simple
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'enviarSobreLoadingIndicator';
            loadingIndicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            const loadingContent = document.createElement('div');
            loadingContent.style.cssText = `
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
            `;
            loadingContent.innerHTML = `
                <div>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-spinner fa-spin fa-3x"></i>
                    </div>
                    <p>Enviando sobres pendientes al SII...</p>
                </div>
            `;

            loadingIndicator.appendChild(loadingContent);
            document.body.appendChild(loadingIndicator);

            // Realizar la petición AJAX al endpoint enviar_sobre.php
            fetch('enviar_sobre.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                if (data.success) {
                    // Si la operación fue exitosa
                    alert(`¡Éxito! ${data.mensaje} ${data.trackid ? 'TrackID: ' + data.trackid : ''}`);
                } else {
                    // Si hubo un error
                    alert(`Error: ${data.error || 'Ocurrió un error al enviar el sobre'}`);
                }
            })
            .catch(error => {
                console.error('Error en la petición:', error);

                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                // Mostrar mensaje de error
                alert(`Error de conexión al enviar el sobre: ${error.message}`);
            });
        });
    }
});

// Función para validar campos obligatorios del formulario
function validarFormularioDTE() {
    // Determinar tipo de documento para validaciones específicas
    const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
    const camposFaltantes = [];

    // Validar campos comunes
    if (!document.getElementById('folioDTE').value)
        camposFaltantes.push('Folio del documento');

    if (!document.getElementById('fechaEmision').value)
        camposFaltantes.push('Fecha de emisión');

    // Datos del receptor
    if (!document.getElementById('rutReceptor').value)
        camposFaltantes.push('RUT del receptor');

    if (!document.getElementById('razonSocialReceptor').value)
        camposFaltantes.push('Razón social del receptor');

    if (!document.getElementById('direccionReceptor').value)
        camposFaltantes.push('Dirección del receptor');

    if (!document.getElementById('comunaReceptor').value)
        camposFaltantes.push('Comuna del receptor');

    // Validar totales
    if (parseInt(document.getElementById('montoNeto').value || 0) <= 0)
        camposFaltantes.push('Monto neto (debe ser mayor a cero)');

    // Validar al menos un ítem
    const itemRows = document.querySelectorAll('.item-row');
    if (itemRows.length > 0) {
        const primerItem = itemRows[0];

        if (!primerItem.querySelector('.item-nombre').value)
            camposFaltantes.push('Nombre del producto');

        if (parseFloat(primerItem.querySelector('.item-cantidad').value || 0) <= 0)
            camposFaltantes.push('Cantidad del producto');

        if (parseFloat(primerItem.querySelector('.item-precio').value || 0) <= 0)
            camposFaltantes.push('Precio del producto');
    }

    // Validaciones específicas para Facturas
    if (tipoDTE === 33 || tipoDTE === 34) {
        if (!document.getElementById('fechaVencimiento').value)
            camposFaltantes.push('Fecha de vencimiento');

        if (!document.getElementById('giroReceptor').value)
            camposFaltantes.push('Giro del receptor');
    }

    return {
        esValido: camposFaltantes.length === 0,
        camposFaltantes: camposFaltantes
    };
}


// Agregar event listener para el botón buscar receptor
document.addEventListener('DOMContentLoaded', function() {
    const buscarReceptorBtn = document.getElementById('buscarReceptorBtn');
    if (buscarReceptorBtn) {
        buscarReceptorBtn.addEventListener('click', buscarReceptor);
    }
});

// Define a single, robust buscarReceptor function
function buscarReceptor() {
    console.log('Función buscarReceptor ejecutada');

    const rutInput = document.getElementById('rutReceptor');
    const rutReceptor = rutInput.value.trim();

    // Validar el RUT antes de hacer la búsqueda
    if (!validarFormatoRUT(rutInput)) {
        console.log('RUT inválido, no se realizará la búsqueda');
        return;
    }

    console.log('RUT validado correctamente:', rutReceptor);

    // Mostrar indicador visual de búsqueda
    rutInput.style.backgroundColor = '#f0f8ff';
    mostrarMensaje('Buscando receptor...', 'info');

    // Hacer la petición AJAX para buscar el receptor
    const url = `buscar_receptor.php?rut=${encodeURIComponent(rutReceptor)}`;
    console.log('URL de búsqueda:', url);

    fetch(url)
        .then(response => {
            console.log('Respuesta recibida, status:', response.status);
            if (!response.ok) {
                throw new Error('Error en la solicitud: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            rutInput.style.backgroundColor = '';

            if (data.success && data.data) {
                document.getElementById('razonSocialReceptor').value = data.data.razon_social || '';
                document.getElementById('direccionReceptor').value = data.data.direccion || '';
                document.getElementById('comunaReceptor').value = data.data.comuna || '';
                document.getElementById('giroReceptor').value = data.data.giro || '';
                document.getElementById('contactoReceptor').value = data.data.contacto || '';
                mostrarMensaje('Receptor encontrado correctamente', 'success');
            } else {
                mostrarMensaje(data.message || 'No se encontró ningún receptor con ese RUT', 'error');
                limpiarCamposReceptor();
            }
        })
        .catch(error => {
            console.error('Error en la búsqueda:', error);
            mostrarMensaje('Error al realizar la búsqueda: ' + error.message, 'error');
            rutInput.style.backgroundColor = '';
        });
}

// Función para mostrar mensajes
function mostrarMensaje(mensaje, tipo) {
    console.log(`Mostrando mensaje: "${mensaje}" (${tipo})`);

    const messageElement = document.getElementById('receptorSearchMessage');
    if (messageElement) {
        messageElement.textContent = mensaje;
        messageElement.style.display = 'block';

        if (tipo === 'error') {
            messageElement.style.color = '#e74c3c';
        } else if (tipo === 'success') {
            messageElement.style.color = '#2ecc71';
        } else {
            messageElement.style.color = '#3498db';
        }

        if (tipo === 'success') {
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    } else {
        alert(mensaje);
    }
}

// Función para limpiar los campos del receptor
function limpiarCamposReceptor() {
    document.getElementById('razonSocialReceptor').value = '';
    document.getElementById('direccionReceptor').value = '';
    document.getElementById('comunaReceptor').value = '';
    document.getElementById('giroReceptor').value = '';
    document.getElementById('contactoReceptor').value = '';
}

// Configuración única de event listeners cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Configurando evento para buscar receptor...');

    // Asignar evento al botón de búsqueda (sin reemplazar el elemento)
    const buscarReceptorBtn = document.getElementById('buscarReceptorBtn');
    if (buscarReceptorBtn) {
        console.log('Botón de búsqueda encontrado');

        // NO reemplazamos el botón, ya que tiene un onclick inline
        // Solo agregamos un listener adicional como respaldo
        buscarReceptorBtn.addEventListener('click', function(e) {
            // No prevenimos el evento predeterminado para permitir
            // que el onclick inline se ejecute normalmente
            buscarReceptor();
        });
    } else {
        console.error('No se encontró el botón de búsqueda de receptor (ID: buscarReceptorBtn)');
    }

    // Configurar evento de teclado para buscar con Enter en el campo RUT
    const rutInput = document.getElementById('rutReceptor');
    if (rutInput) {
        rutInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                buscarReceptor();
            }
        });
    }

    // Exportar la función para acceso global si es necesario
    window.buscarReceptor = buscarReceptor;
});

// Añadir esta función al final del archivo (después de tu código existente)

// Esta función asegura que el manejador onclick en el HTML siga funcionando
function buscarReceptorInline() {
    // Simplemente llama a la implementación principal
    buscarReceptor();
}

// Asegura que ambas funciones estén disponibles globalmente
window.buscarReceptor = buscarReceptor;
window.buscarReceptorInline = buscarReceptorInline;

// Asegúrate de que este código se ejecute tan pronto como el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    const buscarReceptorBtn = document.getElementById('buscarReceptorBtn');
    if (buscarReceptorBtn) {
        buscarReceptorBtn.addEventListener('click', function() {
            buscarReceptor();
        });
    }

    // Resto del código DOMContentLoaded...
});

// Define la función principal buscarReceptor
function buscarReceptor() {
    // El código existente de búsqueda...
}

// Exporta la función al objeto window para hacerla globalmente accesible
window.buscarReceptor = buscarReceptor;

// 1. Declaraciones globales al inicio del archivo
window.buscarReceptorInline = function() {
    buscarReceptor();
};

// 2. Función principal de búsqueda
function buscarReceptor() {
    console.log('Función buscarReceptor ejecutada');

    const rutReceptor = document.getElementById('rutReceptor').value.trim();
    console.log('RUT ingresado:', rutReceptor);

    if (!rutReceptor) {
        mostrarMensaje('Debe ingresar un RUT para buscar', 'error');
        return;
    }

    const rutInput = document.getElementById('rutReceptor');
    rutInput.style.backgroundColor = '#f0f8ff';
    mostrarMensaje('Buscando receptor...', 'info');

    const url = `buscar_receptor.php?rut=${encodeURIComponent(rutReceptor)}`;
    console.log('URL de búsqueda:', url);

    fetch(url)
        .then(response => {
            console.log('Respuesta recibida, status:', response.status);
            if (!response.ok) {
                throw new Error('Error en la solicitud: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            rutInput.style.backgroundColor = '';

            if (data.success && data.data) {
                document.getElementById('razonSocialReceptor').value = data.data.razon_social || '';
                document.getElementById('direccionReceptor').value = data.data.direccion || '';
                document.getElementById('comunaReceptor').value = data.data.comuna || '';
                document.getElementById('giroReceptor').value = data.data.giro || '';
                document.getElementById('contactoReceptor').value = data.data.contacto || '';
                mostrarMensaje('Receptor encontrado correctamente', 'success');
            } else {
                mostrarMensaje(data.message || 'No se encontró ningún receptor con ese RUT', 'error');
                limpiarCamposReceptor();
            }
        })
        .catch(error => {
            console.error('Error en la búsqueda:', error);
            mostrarMensaje('Error al realizar la búsqueda: ' + error.message, 'error');
            rutInput.style.backgroundColor = '';
        });
}

// 3. Funciones de soporte
function mostrarMensaje(mensaje, tipo) {
    console.log(`Mostrando mensaje: "${mensaje}" (${tipo})`);

    const messageElement = document.getElementById('receptorSearchMessage');
    if (messageElement) {
        messageElement.textContent = mensaje;
        messageElement.style.display = 'block';

        if (tipo === 'error') {
            messageElement.style.color = '#e74c3c';
        } else if (tipo === 'success') {
            messageElement.style.color = '#2ecc71';
        } else {
            messageElement.style.color = '#3498db';
        }

        if (tipo === 'success') {
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    } else {
        alert(mensaje);
    }
}

function limpiarCamposReceptor() {
    document.getElementById('razonSocialReceptor').value = '';
    document.getElementById('direccionReceptor').value = '';
    document.getElementById('comunaReceptor').value = '';
    document.getElementById('giroReceptor').value = '';
    document.getElementById('contactoReceptor').value = '';
}

// 4. Configuración de eventos cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado. Configurando eventos para búsqueda de receptor...');

    const rutInput = document.getElementById('rutReceptor');
    if (rutInput) {
        rutInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                buscarReceptor();
            }
        });
    }

    // Asegurarnos de que las funciones estén disponibles globalmente
    window.buscarReceptor = buscarReceptor;
});

// --- Resto del código existente --- //
function validarFormatoRUT(input) {
    let rut = input.value.trim();

    // Eliminar puntos y guiones
    rut = rut.replace(/[.-]/g, '');

    // Verificar longitud mínima
    if (rut.length < 2) {
        mostrarErrorRUT('RUT inválido');
        return false;
    }

    // Separar cuerpo y dígito verificador
    const cuerpo = rut.slice(0, -1);
    let dv = rut.slice(-1).toUpperCase();

    // Validar que el cuerpo solo contenga números
    if (!/^\d+$/.test(cuerpo)) {
        mostrarErrorRUT('El RUT debe contener solo números');
        return false;
    }

    // Validar que el dígito verificador sea número o 'K'
    if (!/^[0-9K]$/.test(dv)) {
        mostrarErrorRUT('Dígito verificador inválido');
        return false;
    }

    // Calcular dígito verificador
    let suma = 0;
    let multiplicador = 2;

    // Calcular suma ponderada
    for (let i = cuerpo.length - 1; i >= 0; i--) {
        suma += parseInt(cuerpo.charAt(i)) * multiplicador;
        multiplicador = multiplicador === 7 ? 2 : multiplicador + 1;
    }

    // Calcular dígito verificador esperado
    let dvEsperado = 11 - (suma % 11);
    dvEsperado = dvEsperado === 11 ? '0' : dvEsperado === 10 ? 'K' : dvEsperado.toString();

    // Validar que el dígito verificador sea correcto
    if (dv !== dvEsperado) {
        mostrarErrorRUT('RUT inválido');
        return false;
    }

    // Formatear RUT solo con guión
    const rutFormateado = cuerpo + '-' + dv;
    input.value = rutFormateado;

    // Ocultar mensaje de error si todo está correcto
    ocultarErrorRUT();
    return true;
}

function mostrarErrorRUT(mensaje) {
    const errorDiv = document.getElementById('rutError');
    errorDiv.textContent = mensaje;
    errorDiv.style.display = 'block';
    document.getElementById('rutReceptor').classList.add('error');
}

function ocultarErrorRUT() {
    const errorDiv = document.getElementById('rutError');
    errorDiv.style.display = 'none';
    document.getElementById('rutReceptor').classList.remove('error');
}

// Agregar evento para formatear mientras el usuario escribe
document.addEventListener('DOMContentLoaded', function() {
    const rutInput = document.getElementById('rutReceptor');
    if (rutInput) {
        rutInput.addEventListener('input', function() {
            // Eliminar cualquier carácter que no sea número, K o guión
            let valor = this.value.replace(/[^\dkK-]/g, '');

            // Convertir 'k' minúscula a mayúscula
            valor = valor.replace(/k/g, 'K');

            // Asegurar que solo haya un guión
            valor = valor.replace(/-/g, '');
            if (valor.length > 1) {
                valor = valor.slice(0, -1) + '-' + valor.slice(-1);
            }

            this.value = valor;
        });
    }
});

// Function to calculate the net price from the total price
// function calcularPrecioNeto(element) {
//     const row = element.closest('.item-row');
//     const tipoDTE = parseInt(document.getElementById('tipoDTE').value);
//     const tasaIVA = parseFloat(document.getElementById('tasaIVA').value) || 19;

//     // Verificar si es una nota de cr�dito con datos poblados desde un documento de referencia
//     const esNotaCredito = tipoDTE === 61;
//     const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

//     // Si es una nota de cr�dito con datos poblados, no recalcular
//     if (esNotaCredito && formularioPoblado) {
//         console.log('Nota de cr�dito con datos poblados: no se recalcula el precio');
//         return;
//     }

//     // Obtener el valor del precio total ingresado
//     const precioTotal = parseFloat(element.value) || 0;
//     const cantidad = parseFloat(row.querySelector('.item-cantidad').value) || 1;

//     // Calcular el precio neto seg�n el tipo de documento
//     let precioNeto;

//     if (tipoDTE === 39) { // Boleta (precio con IVA)
//         // Para boletas, el precio total ya incluye IVA
//         precioNeto = precioTotal;
//     } else { // Factura u otros documentos (precio neto)
//         // Para facturas, calcular el precio neto a partir del total
//         precioNeto = precioTotal / (1 + (tasaIVA / 100));
//     }

//     // Calcular el precio unitario (dividir por la cantidad)
//     const precioUnitario = precioNeto / cantidad;

//     // Actualizar el campo de precio con el valor calculado
//     row.querySelector('.item-precio').value = Math.round(precioUnitario);

//     // Recalcular el monto del �tem
//     calcularMontoItem(row.querySelector('.item-precio'));
//     actualizarMontoNeto();
// }
