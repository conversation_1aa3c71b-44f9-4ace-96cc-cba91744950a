<?php
// Archivo: create_entrada.php
// Descripción: Endpoint para guardar entradas de inventario en la tabla stock

// Incluir archivos necesarios
require_once 'db_connection.php';
require_once 'auth_check.php';
require_once 'logger.php';

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Inicializar respuesta
$response = ['status' => 'error', 'message' => ''];

try {
    // Obtener conexión a la base de datos
    $conn = getConnection();
    
    // Validar datos recibidos
    if (!isset($_POST['repuesto_id']) || empty($_POST['repuesto_id'])) {
        throw new Exception('El ID del repuesto es obligatorio');
    }
    
    if (!isset($_POST['almacen_id']) || empty($_POST['almacen_id'])) {
        throw new Exception('El ID del almacén es obligatorio');
    }
    
    if (!isset($_POST['cantidad']) || !is_numeric($_POST['cantidad']) || $_POST['cantidad'] <= 0) {
        throw new Exception('La cantidad debe ser un número mayor que cero');
    }
    
    // Obtener y sanitizar datos
    $repuesto_id = intval($_POST['repuesto_id']);
    $almacen_id = intval($_POST['almacen_id']);
    $cantidad = intval($_POST['cantidad']);
    $lote = isset($_POST['lote']) ? trim($_POST['lote']) : null;
    $fecha_vencimiento = isset($_POST['fecha_vencimiento']) && !empty($_POST['fecha_vencimiento']) 
                        ? $_POST['fecha_vencimiento'] 
                        : null;
    
    // Verificar si el repuesto existe
    $stmt = $conn->prepare("SELECT id FROM repuestos WHERE id = ?");
    $stmt->bind_param("i", $repuesto_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('El repuesto seleccionado no existe');
    }
    
    // Verificar si el almacén existe
    $stmt = $conn->prepare("SELECT id FROM almacenes WHERE id = ?");
    $stmt->bind_param("i", $almacen_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('El almacén seleccionado no existe');
    }
    
    // Verificar si ya existe una entrada con la misma combinación de repuesto, almacén y lote
    $stmt = $conn->prepare("SELECT id, cantidad FROM stock WHERE repuesto_id = ? AND almacen_id = ? AND lote = ?");
    $stmt->bind_param("iis", $repuesto_id, $almacen_id, $lote);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Si existe, actualizar la cantidad
        $row = $result->fetch_assoc();
        $stock_id = $row['id'];
        $nueva_cantidad = $row['cantidad'] + $cantidad;
        
        $stmt = $conn->prepare("UPDATE stock SET cantidad = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->bind_param("ii", $nueva_cantidad, $stock_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Error al actualizar el stock: ' . $stmt->error);
        }
        
        $response = [
            'status' => 'success',
            'message' => 'Stock actualizado correctamente',
            'data' => [
                'id' => $stock_id,
                'cantidad' => $nueva_cantidad
            ]
        ];
    } else {
        // Si no existe, insertar nueva entrada
        $stmt = $conn->prepare("INSERT INTO stock (repuesto_id, almacen_id, cantidad, lote, fecha_vencimiento) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("iiiss", $repuesto_id, $almacen_id, $cantidad, $lote, $fecha_vencimiento);
        
        if (!$stmt->execute()) {
            throw new Exception('Error al insertar en la tabla stock: ' . $stmt->error);
        }
        
        $stock_id = $stmt->insert_id;
        
        $response = [
            'status' => 'success',
            'message' => 'Entrada registrada exitosamente',
            'data' => [
                'id' => $stock_id,
                'cantidad' => $cantidad
            ]
        ];
    }
    
    // Registrar la acción en el log
    $user_id = $_SESSION['user_id'] ?? 0;
    $action = "Registro de entrada de inventario: Repuesto ID $repuesto_id, Almacén ID $almacen_id, Cantidad $cantidad";
    logAction($user_id, $action);
    
} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'message' => $e->getMessage()
    ];
    
    // Registrar el error en el log
    $user_id = $_SESSION['user_id'] ?? 0;
    $error_msg = "Error al registrar entrada de inventario: " . $e->getMessage();
    logError($user_id, $error_msg);
}

// Devolver respuesta en formato JSON
header('Content-Type: application/json');
echo json_encode($response);
