/**
 * Función para buscar un documento por su folio y poblar el formulario de nota de crédito
 */
function buscarDocumentoReferencia() {
    console.log('Función buscarDocumentoReferencia ejecutada');

    // Obtener el folio ingresado
    const folioInput = document.getElementById('folioRef');
    const folio = folioInput.value.trim();

    // Obtener el tipo de documento seleccionado
    const tipoDocRef = document.getElementById('tipoDocRef').value;

    // Validar que se haya ingresado un folio
    if (!folio) {
        mostrarMensajeFolio('Debe ingresar un número de folio para buscar', 'error');
        return;
    }

    // Mostrar indicador visual de búsqueda
    folioInput.style.backgroundColor = '#f0f8ff';
    mostrarMensajeFolio('Buscando documento...', 'info');

    // Construir la URL de búsqueda
    const url = `buscar_documento.php?folio=${encodeURIComponent(folio)}&tipo_dte=${encodeURIComponent(tipoDocRef)}&is_reference=true`;
    console.log('URL de búsqueda:', url);

    // Realizar la petición AJAX
    fetch(url)
        .then(response => {
            console.log('Respuesta recibida, status:', response.status);
            if (!response.ok) {
                throw new Error('Error en la solicitud: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            folioInput.style.backgroundColor = '';

            if (data.success && data.json_data) {
                mostrarMensajeFolio('Documento encontrado. Poblando formulario...', 'success');
                console.log('Tipo de documento encontrado:', data.data.tipo_dte);

                // Poblar el formulario con los datos del documento
                poblarFormularioNotaCredito(data.json_data);
            } else {
                console.log('Error en la búsqueda:', data.debug || 'No hay información de depuración');
                mostrarMensajeFolio(data.message || 'No se encontró ningún documento con ese folio', 'error');

                // Si hay tipos encontrados pero no coinciden con el seleccionado, sugerir cambiar el tipo
                if (data.debug && data.debug.tipos_encontrados && data.debug.tipos_encontrados.length > 0) {
                    const tiposEncontrados = data.debug.tipos_encontrados;
                    console.log('Tipos de documento encontrados con este folio:', tiposEncontrados);

                    // Sugerir cambiar al primer tipo encontrado
                    if (tiposEncontrados.length === 1) {
                        const tipoEncontrado = tiposEncontrados[0];
                        const selectTipoDoc = document.getElementById('tipoDocRef');

                        // Verificar si el tipo encontrado está en las opciones disponibles
                        const opcionExiste = Array.from(selectTipoDoc.options).some(option => option.value == tipoEncontrado);

                        if (opcionExiste) {
                            mostrarMensajeFolio(`Sugerencia: Cambie el tipo de documento a ${tipoEncontrado} y vuelva a buscar`, 'info');
                        }
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error en la búsqueda:', error);
            mostrarMensajeFolio('Error al realizar la búsqueda: ' + error.message, 'error');
            folioInput.style.backgroundColor = '';
        });
}

/**
 * Función para mostrar mensajes en el campo de folio
 */
function mostrarMensajeFolio(mensaje, tipo) {
    console.log(`Mostrando mensaje: "${mensaje}" (${tipo})`);

    const messageElement = document.getElementById('folioRefMessage');
    if (messageElement) {
        messageElement.textContent = mensaje;
        messageElement.style.display = 'block';

        // Estilos según el tipo de mensaje
        if (tipo === 'error') {
            messageElement.style.color = '#e74c3c';
            messageElement.style.backgroundColor = '#fde8e8';
            messageElement.style.border = '1px solid #e74c3c';
            messageElement.style.padding = '8px';
            messageElement.style.borderRadius = '4px';
        } else if (tipo === 'success') {
            messageElement.style.color = '#2ecc71';
            messageElement.style.backgroundColor = '#e8f8f0';
            messageElement.style.border = '1px solid #2ecc71';
            messageElement.style.padding = '8px';
            messageElement.style.borderRadius = '4px';
        } else {
            messageElement.style.color = '#3498db';
            messageElement.style.backgroundColor = '#e8f4fd';
            messageElement.style.border = '1px solid #3498db';
            messageElement.style.padding = '8px';
            messageElement.style.borderRadius = '4px';
        }

        // Hacer scroll al mensaje para asegurarse de que sea visible
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        // Solo ocultar automáticamente los mensajes de éxito
        if (tipo === 'success') {
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    } else {
        alert(mensaje);
    }
}

/**
 * Función para poblar el formulario de nota de crédito con los datos del documento encontrado
 */
function poblarFormularioNotaCredito(jsonData) {
    console.log('Poblando formulario con datos:', jsonData);

    try {
        // Verificar si tenemos un objeto Documento
        if (!jsonData.Documento) {
            throw new Error('El JSON no contiene la estructura esperada (Documento)');
        }

        const documento = jsonData.Documento;

        // Fecha del documento de referencia
        if (documento.Encabezado && documento.Encabezado.IdentificacionDTE && documento.Encabezado.IdentificacionDTE.FechaEmision) {
            const fechaEmision = documento.Encabezado.IdentificacionDTE.FechaEmision;
            document.getElementById('fechaDocRef').value = fechaEmision;

            // Para notas de crédito, establecemos automáticamente la fecha de vencimiento igual a la fecha de emisión
            // para evitar el error 400 de la API
            console.log('Nota de crédito: Estableciendo fecha de vencimiento igual a la fecha de emisión');

            // Establecer la fecha de emisión actual
            const fechaEmisionActual = document.getElementById('fechaEmision').value;

            // Establecer la fecha de vencimiento igual a la fecha de emisión actual
            document.getElementById('fechaVencimiento').value = fechaEmisionActual;
            console.log('Fecha de vencimiento establecida a:', fechaEmisionActual);
        }

        // Actualizar el tipo de documento con el tipo real del documento encontrado
        if (documento.Encabezado && documento.Encabezado.IdentificacionDTE && documento.Encabezado.IdentificacionDTE.TipoDTE) {
            const tipoDTEReal = documento.Encabezado.IdentificacionDTE.TipoDTE;
            console.log('Tipo de documento real encontrado:', tipoDTEReal);

            // Verificar si el tipo existe en las opciones del select
            const tipoDocRefSelect = document.getElementById('tipoDocRef');
            const opcionExiste = Array.from(tipoDocRefSelect.options).some(option => option.value == tipoDTEReal);

            if (opcionExiste) {
                tipoDocRefSelect.value = tipoDTEReal;
                console.log('Tipo de documento actualizado en el select a:', tipoDTEReal);
            } else {
                console.warn('El tipo de documento', tipoDTEReal, 'no existe en las opciones del select');
            }
        } else {
            console.warn('No se pudo determinar el tipo de documento real');
        }

        // Razón de referencia (por defecto "Anula documento de referencia")
        document.getElementById('razonRef').value = "Anula documento de referencia";

        // Código de referencia (por defecto 1 - Anula documento)
        document.getElementById('codigoRef').value = "1";

        // Datos del receptor
        if (documento.Encabezado && documento.Encabezado.Receptor) {
            const receptor = documento.Encabezado.Receptor;

            if (receptor.Rut) document.getElementById('rutReceptor').value = receptor.Rut;
            if (receptor.RazonSocial) document.getElementById('razonSocialReceptor').value = receptor.RazonSocial;
            if (receptor.Direccion) document.getElementById('direccionReceptor').value = receptor.Direccion;
            if (receptor.Comuna) document.getElementById('comunaReceptor').value = receptor.Comuna;
            if (receptor.Giro) document.getElementById('giroReceptor').value = receptor.Giro;
            if (receptor.Contacto) document.getElementById('contactoReceptor').value = receptor.Contacto;
        }

        // Limpiar los items existentes excepto el primero
        const itemsContainer = document.getElementById('itemsContainer');
        const items = itemsContainer.querySelectorAll('.item-row');

        // Mantener solo el primer item
        if (items.length > 1) {
            for (let i = 1; i < items.length; i++) {
                items[i].remove();
            }
        }

        // Limpiar el primer item
        if (items.length > 0) {
            const firstItem = items[0];
            firstItem.querySelector('.item-nombre').value = '';
            firstItem.querySelector('.item-descripcion').value = '';
            firstItem.querySelector('.item-cantidad').value = '1';
            firstItem.querySelector('.item-unidad').value = 'un';
            firstItem.querySelector('.item-precio').value = '0';
            firstItem.querySelector('.item-descuento').value = '0';
            firstItem.querySelector('.item-recargo').value = '0';
            firstItem.querySelector('.item-monto').value = '0';
        }

        // Poblar los items
        if (documento.Detalles && Array.isArray(documento.Detalles)) {
            // Usar el primer item para el primer detalle
            if (documento.Detalles.length > 0 && items.length > 0) {
                const firstItem = items[0];
                const firstDetail = documento.Detalles[0];

                firstItem.querySelector('.item-nombre').value = firstDetail.Nombre || '';
                firstItem.querySelector('.item-descripcion').value = firstDetail.Descripcion || '';
                firstItem.querySelector('.item-cantidad').value = firstDetail.Cantidad || '1';
                firstItem.querySelector('.item-unidad').value = firstDetail.UnidadMedida || 'un';
                firstItem.querySelector('.item-precio').value = firstDetail.Precio || '0';
                firstItem.querySelector('.item-descuento').value = firstDetail.Descuento || '0';
                firstItem.querySelector('.item-recargo').value = firstDetail.Recargo || '0';

                // Calcular el monto del item
                calcularMontoItem(firstItem.querySelector('.item-cantidad'));
            }

            // Agregar items adicionales si hay más detalles
            for (let i = 1; i < documento.Detalles.length; i++) {
                const detail = documento.Detalles[i];

                // Usar la función existente para agregar un nuevo item
                document.getElementById('addItemBtn').click();

                // Obtener el nuevo item agregado
                const newItems = itemsContainer.querySelectorAll('.item-row');
                const newItem = newItems[newItems.length - 1];

                // Poblar el nuevo item
                newItem.querySelector('.item-nombre').value = detail.Nombre || '';
                newItem.querySelector('.item-descripcion').value = detail.Descripcion || '';
                newItem.querySelector('.item-cantidad').value = detail.Cantidad || '1';
                newItem.querySelector('.item-unidad').value = detail.UnidadMedida || 'un';
                newItem.querySelector('.item-precio').value = detail.Precio || '0';
                newItem.querySelector('.item-descuento').value = detail.Descuento || '0';
                newItem.querySelector('.item-recargo').value = detail.Recargo || '0';

                // Calcular el monto del item
                calcularMontoItem(newItem.querySelector('.item-cantidad'));
            }
        }

        // Para notas de crédito, tomar los totales directamente del documento original
        // en lugar de calcularlos a partir de los ítems
        if (documento.Encabezado && documento.Encabezado.Totales) {
            const totales = documento.Encabezado.Totales;

            // Asignar directamente los valores de totales del documento original
            if (totales.MontoNeto) document.getElementById('montoNeto').value = totales.MontoNeto;
            if (totales.IVA) document.getElementById('ivaCalculado').value = totales.IVA;
            if (totales.MontoTotal) document.getElementById('montoTotal').value = totales.MontoTotal;

            console.log('Totales asignados directamente del documento original:', {
                montoNeto: totales.MontoNeto,
                iva: totales.IVA,
                montoTotal: totales.MontoTotal
            });

            // Mostrar mensaje informativo
            mostrarMensajeFolio('Los totales han sido tomados directamente del documento original', 'info');
        } else {
            // Si no se encuentran los totales en el documento, calcularlos normalmente
            console.log('No se encontraron totales en el documento original, calculando...');
            actualizarMontoNeto();
        }

        // Bloquear todos los campos del formulario para evitar modificaciones
        bloquearCamposFormulario();

        // Mostrar mensaje de éxito con información adicional
        mostrarMensajeFolio('Documento encontrado. Formulario poblado y bloqueado para evitar modificaciones.', 'success');
        console.log('Formulario poblado y bloqueado exitosamente');
    } catch (error) {
        console.error('Error al poblar el formulario:', error);
        mostrarMensajeFolio('Error al poblar el formulario: ' + error.message, 'error');
    }
}

/**
 * Función para bloquear todos los campos del formulario
 */
function bloquearCamposFormulario() {
    console.log('Bloqueando campos del formulario');

    // Marcar el formulario como poblado con datos de referencia
    document.body.setAttribute('data-formulario-poblado', 'true');

    // Bloquear campos de referencia
    document.getElementById('fechaDocRef').readOnly = true;
    document.getElementById('razonRef').readOnly = true;
    document.getElementById('codigoRef').readOnly = true;
    document.getElementById('tipoDocRef').disabled = true;
    document.getElementById('folioRef').readOnly = true;

    // Bloquear botón de búsqueda
    const buscarFolioBtn = document.getElementById('buscarFolioBtn');
    if (buscarFolioBtn) {
        buscarFolioBtn.disabled = true;
    }

    // Bloquear campos del receptor
    document.getElementById('rutReceptor').readOnly = true;
    document.getElementById('razonSocialReceptor').readOnly = true;
    document.getElementById('direccionReceptor').readOnly = true;
    document.getElementById('comunaReceptor').readOnly = true;
    document.getElementById('giroReceptor').readOnly = true;
    document.getElementById('contactoReceptor').readOnly = true;

    // Bloquear campos de items
    document.querySelectorAll('.item-row').forEach(row => {
        row.querySelectorAll('input').forEach(input => {
            input.readOnly = true;
        });

        // Deshabilitar botón de eliminar item
        const removeBtn = row.querySelector('.remove-item-btn');
        if (removeBtn) {
            removeBtn.disabled = true;
            removeBtn.style.opacity = '0.5';
            removeBtn.style.cursor = 'not-allowed';
        }
    });

    // Deshabilitar botón de agregar item
    const addItemBtn = document.getElementById('addItemBtn');
    if (addItemBtn) {
        addItemBtn.disabled = true;
        addItemBtn.style.opacity = '0.5';
        addItemBtn.style.cursor = 'not-allowed';
    }

    // Bloquear campos de totales
    document.getElementById('montoNeto').readOnly = true;
    document.getElementById('ivaCalculado').readOnly = true;
    document.getElementById('montoTotal').readOnly = true;

    // Agregar clase visual para indicar campos bloqueados
    document.querySelectorAll('input[readonly], select[disabled]').forEach(field => {
        field.classList.add('campo-bloqueado');
    });

    // Agregar estilo para campos bloqueados si no existe
    if (!document.getElementById('estilos-campos-bloqueados')) {
        const style = document.createElement('style');
        style.id = 'estilos-campos-bloqueados';
        style.textContent = `
            .campo-bloqueado {
                background-color: #f8f9fa !important;
                border-color: #dee2e6 !important;
                color: #495057 !important;
                cursor: not-allowed !important;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * Función para desbloquear todos los campos del formulario
 */
function desbloquearCamposFormulario() {
    console.log('Desbloqueando campos del formulario');

    // Asegurarse de que la función esté disponible globalmente
    window.desbloquearCamposFormulario = desbloquearCamposFormulario;

    // Marcar el formulario como no poblado
    document.body.removeAttribute('data-formulario-poblado');

    // Desbloquear campos de referencia
    document.getElementById('fechaDocRef').readOnly = false;
    document.getElementById('razonRef').readOnly = false;
    document.getElementById('codigoRef').readOnly = false;
    document.getElementById('tipoDocRef').disabled = false;
    document.getElementById('folioRef').readOnly = false;

    // Desbloquear botón de búsqueda
    const buscarFolioBtn = document.getElementById('buscarFolioBtn');
    if (buscarFolioBtn) {
        buscarFolioBtn.disabled = false;
    }

    // Desbloquear campos del receptor
    document.getElementById('rutReceptor').readOnly = false;
    document.getElementById('razonSocialReceptor').readOnly = false;
    document.getElementById('direccionReceptor').readOnly = false;
    document.getElementById('comunaReceptor').readOnly = false;
    document.getElementById('giroReceptor').readOnly = false;
    document.getElementById('contactoReceptor').readOnly = false;

    // Desbloquear campos de items
    document.querySelectorAll('.item-row').forEach(row => {
        row.querySelectorAll('input').forEach(input => {
            input.readOnly = false;
        });

        // Habilitar botón de eliminar item
        const removeBtn = row.querySelector('.remove-item-btn');
        if (removeBtn) {
            removeBtn.disabled = false;
            removeBtn.style.opacity = '';
            removeBtn.style.cursor = '';
        }
    });

    // Habilitar botón de agregar item
    const addItemBtn = document.getElementById('addItemBtn');
    if (addItemBtn) {
        addItemBtn.disabled = false;
        addItemBtn.style.opacity = '';
        addItemBtn.style.cursor = '';
    }

    // Desbloquear campos de totales
    document.getElementById('montoNeto').readOnly = false;
    document.getElementById('ivaCalculado').readOnly = false;
    document.getElementById('montoTotal').readOnly = false;

    // Quitar clase visual de campos bloqueados
    document.querySelectorAll('.campo-bloqueado').forEach(field => {
        field.classList.remove('campo-bloqueado');
    });

    // Limpiar mensaje de folio
    const folioRefMessage = document.getElementById('folioRefMessage');
    if (folioRefMessage) {
        folioRefMessage.style.display = 'none';
    }
}

/**
 * Función para limpiar el formulario cuando se cambia de tipo de documento
 */
function limpiarFormulario() {
    console.log('Limpiando formulario');

    // Asegurarse de que la función esté disponible globalmente
    window.limpiarFormulario = limpiarFormulario;

    // Limpiar campos de referencia
    document.getElementById('fechaDocRef').value = '';
    document.getElementById('razonRef').value = '';
    document.getElementById('codigoRef').value = '1';
    document.getElementById('folioRef').value = '';

    // Limpiar campos del receptor (solo si no es boleta con checkbox marcado)
    const tipoDTE = document.getElementById('tipoDTE').value;
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');

    if (tipoDTE !== '39' || !defaultReceptorCheckbox || !defaultReceptorCheckbox.checked) {
        document.getElementById('rutReceptor').value = '';
        document.getElementById('razonSocialReceptor').value = '';
        document.getElementById('direccionReceptor').value = '';
        document.getElementById('comunaReceptor').value = '';
        document.getElementById('giroReceptor').value = '';
        document.getElementById('contactoReceptor').value = '';
    }

    // Limpiar items (mantener solo uno vacío)
    const itemsContainer = document.getElementById('itemsContainer');
    const items = itemsContainer.querySelectorAll('.item-row');

    // Mantener solo el primer item
    if (items.length > 1) {
        for (let i = 1; i < items.length; i++) {
            items[i].remove();
        }
    }

    // Limpiar el primer item
    if (items.length > 0) {
        const firstItem = items[0];
        firstItem.querySelector('.item-nombre').value = '';
        firstItem.querySelector('.item-descripcion').value = '';
        firstItem.querySelector('.item-cantidad').value = '1';
        firstItem.querySelector('.item-unidad').value = 'un';
        firstItem.querySelector('.item-precio').value = '0';
        firstItem.querySelector('.item-descuento').value = '0';
        firstItem.querySelector('.item-recargo').value = '0';
        firstItem.querySelector('.item-monto').value = '0';
    }

    // Actualizar totales
    actualizarMontoNeto();
}

// Agregar el evento al botón de búsqueda cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    const buscarFolioBtn = document.getElementById('buscarFolioBtn');
    if (buscarFolioBtn) {
        buscarFolioBtn.addEventListener('click', buscarDocumentoReferencia);
    }

    // También agregar evento para buscar al presionar Enter en el campo de folio
    const folioInput = document.getElementById('folioRef');
    if (folioInput) {
        folioInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                buscarDocumentoReferencia();
            }
        });
    }

    // Agregar evento al cambio de tipo de documento
    const tipoDTESelect = document.getElementById('tipoDTE');
    if (tipoDTESelect) {
        tipoDTESelect.addEventListener('change', function() {
            const nuevoTipo = this.value;
            const formularioPoblado = document.body.hasAttribute('data-formulario-poblado');

            // Si el formulario estaba poblado con datos de referencia
            if (formularioPoblado) {
                console.log('Cambio de tipo de documento detectado. Formulario estaba poblado.');

                // Si cambiamos de nota de crédito (61) a otro tipo
                if (nuevoTipo !== '61') {
                    console.log('Cambiando de nota de crédito a otro tipo de documento');

                    // Desbloquear campos
                    desbloquearCamposFormulario();

                    // Limpiar formulario
                    limpiarFormulario();

                    // Mostrar mensaje
                    alert('Se ha cambiado el tipo de documento. Los datos de la nota de crédito han sido limpiados.');
                }
            }
        });
    }
});
