document.addEventListener('DOMContentLoaded', function() {

function filterTable(tableId) {
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');
    const filters = table.getElementsByClassName('column-search');

    // Add event listeners to all search inputs in this table
    Array.from(filters).forEach(filter => {
        filter.addEventListener('keyup', function() {
            filterRows();
        });
    });

    function filterRows() {
        for (let i = 2; i < rows.length; i++) {
            let row = rows[i];
            let showRow = true;

            for (let j = 0; j < filters.length; j++) {
                let cell = row.cells[j];
                let filter = filters[j].value.toUpperCase();

                if (cell && filter) {
                    let txtValue = cell.textContent || cell.innerText;
                    if (txtValue.toUpperCase().indexOf(filter) === -1) {
                        showRow = false;
                        break;
                    }
                }
            }

            row.style.display = showRow ? '' : 'none';
        }
    }
}

// Call filterTable for each table that needs filtering
filterTable('tabla_repuestos');

});



document.addEventListener('DOMContentLoaded', function() {
    // console.log('Inicializando manejadores del modal');
    




    // Obtener referencias a los elementos
    const marcaSelect = document.getElementById('modelo_new_marca_id');
    const modeloSelect = document.getElementById('modelo_id');
    
    if (!marcaSelect || !modeloSelect) {
        console.error('No se encontraron los elementos select');
        return;
    }

    // Inicializar el modal usando Bootstrap
    const modelModal = new bootstrap.Modal(document.getElementById('newModelModal'));

    // Manejar el cambio en el select de marca
    marcaSelect.addEventListener('change', async function() {
        // console.log('Cambio detectado en marca');
        const marcaId = this.value;
        // console.log('Marca ID seleccionada:', marcaId);
        
        if (!marcaId) {
            modeloSelect.innerHTML = '<option value="">Seleccione un modelo</option>';
            return;
        }

        try {
            // Mostrar estado de carga
            modeloSelect.innerHTML = '<option value="">Cargando modelos...</option>';
            modeloSelect.disabled = true;

            // Realizar la petición
            const response = await fetch(`get_modelos.php?marca_id=${marcaId}`);
            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }

            const data = await response.json();
            // console.log('Datos recibidos:', data);

            // Limpiar y llenar el select de modelos
            modeloSelect.innerHTML = '<option value="">Seleccione un modelo</option>';
            if (Array.isArray(data)) {
                data.forEach(modelo => {
                    const option = document.createElement('option');
                    option.value = modelo.id;
                    option.textContent = modelo.nombre;
                    modeloSelect.appendChild(option);
                });
            }

        } catch (error) {
            console.error('Error al cargar modelos:', error);
            modeloSelect.innerHTML = '<option value="">Error al cargar modelos</option>';
        } finally {
            modeloSelect.disabled = false;
        }
    });

    // Manejar el submit del formulario de nuevo modelo
    const modelForm = document.getElementById('newModelForm');
     if (modelForm) {
        modelForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('Enviando formulario de nuevo modelo');

            try {
                const formData = new FormData(this);
                const response = await fetch('save_modelos.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                if (data.status === 'success') {
                    alert('Modelo creado exitosamente');
                    modelModal.hide();
                    location.reload();
                } else {
                    throw new Error(data.message || 'Error al crear el modelo');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error al crear el modelo: ' + error.message);
            }
        });
    }

    // Función para abrir el modal
    window.openNewModelModal = function() {
        modelModal.show();
    };



        
document.getElementById('anio_inicio').addEventListener('change', function() {
    const anioInicio = parseInt(this.value);
    const anioFinSelect = document.getElementById('anio_fin');
    const anioFin = parseInt(anioFinSelect.value);
    
    // Actualizar las opciones del año fin
    anioFinSelect.innerHTML = '<option value="">Seleccione año fin</option>';
    for(let i = 2024; i >= anioInicio; i--) {
        anioFinSelect.innerHTML += `<option value="${i}">${i}</option>`;
    }
    
    // Si el año fin seleccionado es válido, mantenerlo
    if(anioFin >= anioInicio) {
        anioFinSelect.value = anioFin;
    }
});

});