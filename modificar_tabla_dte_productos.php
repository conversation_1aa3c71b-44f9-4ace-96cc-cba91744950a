<?php
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Verificar si la tabla ya existe
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tb_dte_productos'");
    $stmt->execute();
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        // Verificar si la columna nombre_producto ya existe
        $stmt = $conn->prepare("SHOW COLUMNS FROM tb_dte_productos LIKE 'nombre_producto'");
        $stmt->execute();
        $columnExists = $stmt->fetch();
        
        if (!$columnExists) {
            // Modificar la tabla para hacer repuesto_id nullable y agregar nombre_producto
            $sql = "ALTER TABLE tb_dte_productos 
                    MODIFY COLUMN repuesto_id INT NULL,
                    ADD COLUMN nombre_producto VARCHAR(255) NOT NULL AFTER repuesto_id,
                    ADD COLUMN descripcion_producto TEXT NULL AFTER nombre_producto";
            
            $conn->exec($sql);
            echo json_encode(['status' => 'success', 'message' => 'Tabla tb_dte_productos modificada exitosamente']);
        } else {
            echo json_encode(['status' => 'info', 'message' => 'La columna nombre_producto ya existe en la tabla']);
        }
    } else {
        // Crear la tabla tb_dte_productos
        $sql = "CREATE TABLE tb_dte_productos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            dte_id INT NOT NULL,
            repuesto_id INT NULL,
            nombre_producto VARCHAR(255) NOT NULL,
            descripcion_producto TEXT NULL,
            cantidad INT NOT NULL,
            precio_unitario DECIMAL(10, 2) NOT NULL,
            monto_item DECIMAL(10, 2) NOT NULL,
            fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (dte_id) REFERENCES tb_facturas_dte(id) ON DELETE CASCADE,
            FOREIGN KEY (repuesto_id) REFERENCES repuesto(id) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
        echo json_encode(['status' => 'success', 'message' => 'Tabla tb_dte_productos creada exitosamente']);
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => 'Error al modificar/crear la tabla: ' . $e->getMessage()]);
}
?>
