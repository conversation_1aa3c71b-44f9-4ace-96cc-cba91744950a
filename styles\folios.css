/**
 * Estilos para la funcionalidad de solicitud de folios CAF
 */

/* Notificaciones */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: flex;
    align-items: flex-start;
    z-index: 9999;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.notification.success {
    background-color: #d4edda;
    border-left: 5px solid #28a745;
    color: #155724;
}

.notification.error {
    background-color: #f8d7da;
    border-left: 5px solid #dc3545;
    color: #721c24;
}

.notification.warning {
    background-color: #fff3cd;
    border-left: 5px solid #ffc107;
    color: #856404;
}

.notification.info {
    background-color: #d1ecf1;
    border-left: 5px solid #17a2b8;
    color: #0c5460;
}

.notification-icon {
    margin-right: 15px;
    font-size: 24px;
}

.notification-content {
    flex: 1;
    white-space: pre-line;
}

.notification-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    padding: 0 5px;
    margin-left: 10px;
}

.notification-close:hover {
    opacity: 1;
}

/* Indicador de carga */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator p {
    color: white;
    font-size: 18px;
    text-align: center;
    max-width: 80%;
}

/* Estilos para el campo de folio */
#folioDTE.last-folio {
    background-color: #fff3cd;
    border-color: #ffc107;
}

#folioDTE.new-folio {
    background-color: #d4edda;
    border-color: #28a745;
    transition: background-color 0.5s ease;
}

/* Estilos para el modal de límite de folios */
.folios-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    z-index: 10000;
    max-width: 500px;
    width: 90%;
}

.folios-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.folios-modal-header h2 {
    margin: 0;
    color: #e74c3c;
    display: flex;
    align-items: center;
}

.folios-modal-header h2 i {
    margin-right: 10px;
}

.folios-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #777;
}

.folios-modal-content {
    margin-bottom: 20px;
}

.folios-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.folios-modal-actions button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.folios-modal-actions .btn-primary {
    background-color: #3498db;
    color: white;
}

.folios-modal-actions .btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.folios-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
}

/* Animación para el botón de solicitud de folios */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 1.5s infinite;
}

/* Estilos para el botón de limpiar formulario */
#limpiarFormularioBtn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 20px;
    display: block;
    width: 100%;
    text-align: center;
}

#limpiarFormularioBtn:hover {
    background-color: #2980b9;
}

#limpiarFormularioBtn i {
    margin-right: 8px;
}
