/* Estilos específicos para la funcionalidad de búsqueda */

/* Asegurar que las tarjetas mantengan su diseño al filtrar */
.product-card {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    min-height: 450px !important;
}

/* Asegurar que el grid de productos mantenga su diseño */
.products-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
    gap: 1.5rem !important;
}

/* Asegurar que las imágenes de productos mantengan su tamaño */
.product-image {
    width: 100% !important;
    height: 200px !important;
    object-fit: contain !important;
    background-color: #f8f9fa !important;
}

/* Asegurar que los detalles del producto mantengan su diseño */
.product-info {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.product-details {
    padding: 1.5rem !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 0.8rem !important;
}

/* Estilos para el mensaje de no resultados */
.no-results {
    margin: 2rem auto;
    max-width: 600px;
}

/* Estilos para el input de búsqueda cuando está activo */
#search-input:focus {
    border-color: #34495e;
    box-shadow: 0 0 0 0.2rem rgba(52, 73, 94, 0.25);
}

/* Estilos para los botones de búsqueda y reset */
#search-icon, #reset-filters {
    transition: all 0.3s ease;
}

#search-icon:hover, #reset-filters:hover {
    transform: scale(1.1);
}

/* Asegurar que las tarjetas filtradas mantengan su altura */
.product-card[style*="display: none"] {
    display: none !important;
}

.product-card:not([style*="display: none"]) {
    display: flex !important;
    min-height: 450px !important;
}

/* Asegurar que el contenedor de controles mantenga su posición */
.product-card .controls-container {
    margin-top: auto !important;
    padding: 1rem !important;
}
