<?php
// Archivo: get_dashboard_data.php
// Propósito: Proporcionar datos para el dashboard de ventas

// Establecer la zona horaria correcta para Chile
date_default_timezone_set('America/Santiago');

// Imprimir la fecha y hora actual para depuración
error_log("Fecha y hora actual del servidor: " . date('Y-m-d H:i:s'));

header('Content-Type: application/json');
require_once 'db_connection.php';

// Función para obtener la fecha de inicio según el período
function getFechaInicio($periodo) {
    // Asegurarnos de obtener la fecha actual con la zona horaria correcta
    $hoy = date('Y-m-d');
    error_log("Fecha actual (hoy): " . $hoy);

    switch ($periodo) {
        case 'dia':
            return $hoy . ' 00:00:00';
        case 'semana':
            // Obtener fecha de hace 7 días (incluye el día actual)
            $fechaInicio = date('Y-m-d', strtotime('-6 days')) . ' 00:00:00';
            error_log("Fecha inicio semana: " . $fechaInicio);
            return $fechaInicio;
        case 'mes':
            return date('Y-m-01') . ' 00:00:00';
        case 'anio':
            return date('Y-01-01') . ' 00:00:00';
        case 'semana_anterior':
            // Obtener fecha de hace 14 días hasta hace 7 días
            return date('Y-m-d', strtotime('-13 days')) . ' 00:00:00';
        case 'mes_anterior':
            return date('Y-m-d', strtotime('first day of last month')) . ' 00:00:00';
        case 'anio_anterior':
            return date('Y-01-01', strtotime('-1 year')) . ' 00:00:00';
        default:
            return $hoy . ' 00:00:00';
    }
}

// Función para obtener la fecha de fin según el período
function getFechaFin($periodo) {
    // Asegurarnos de obtener la fecha actual con la zona horaria correcta
    $hoy = date('Y-m-d');
    switch ($periodo) {
        case 'dia':
            // Asegurarnos de incluir todo el día actual hasta el último segundo
            return $hoy . ' 23:59:59';
        case 'semana':
            // Fecha actual (hoy) hasta el último segundo
            return $hoy . ' 23:59:59';
        case 'mes':
            return date('Y-m-t') . ' 23:59:59'; // Último día del mes actual
        case 'anio':
            return date('Y-12-31') . ' 23:59:59';
        case 'semana_anterior':
            // Fecha de hace 7 días
            return date('Y-m-d', strtotime('-7 days')) . ' 23:59:59';
        case 'mes_anterior':
            return date('Y-m-d', strtotime('last day of last month')) . ' 23:59:59';
        case 'anio_anterior':
            return date('Y-12-31', strtotime('-1 year')) . ' 23:59:59';
        default:
            return $hoy . ' 23:59:59';
    }
}

// Obtener el tipo de datos solicitado
$tipo = isset($_GET['tipo']) ? $_GET['tipo'] : 'kpis';
$periodo = isset($_GET['periodo']) ? $_GET['periodo'] : 'mes';

try {
    $conn = getConnection();

    // Respuesta por defecto
    $response = [
        'success' => true,
        'data' => null,
        'periodo' => $periodo
    ];

    // Obtener fechas según el período
    $fechaInicio = getFechaInicio($periodo);
    $fechaFin = getFechaFin($periodo);

    // Según el tipo de datos solicitado
    switch ($tipo) {
        case 'kpis':
            // Obtener KPIs principales
            $stmt = $conn->prepare("
                SELECT
                    COUNT(*) as num_transacciones,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE fecha_generacion BETWEEN :fecha_inicio AND :fecha_fin
            ");
            $stmt->bindParam(':fecha_inicio', $fechaInicio);
            $stmt->bindParam(':fecha_fin', $fechaFin);
            $stmt->execute();
            $kpis = $stmt->fetch(PDO::FETCH_ASSOC);

            // Calcular ticket promedio
            $kpis['ticket_promedio'] = $kpis['num_transacciones'] > 0 ?
                                      round($kpis['venta_total'] / $kpis['num_transacciones']) : 0;

            // Obtener datos del período anterior para comparación
            $periodoAnterior = 'mes_anterior';
            if ($periodo == 'semana') {
                $periodoAnterior = 'semana_anterior';
            } else if ($periodo == 'anio') {
                $periodoAnterior = 'anio_anterior';
            }
            $fechaInicioAnterior = getFechaInicio($periodoAnterior);
            $fechaFinAnterior = getFechaFin($periodoAnterior);

            $stmt = $conn->prepare("
                SELECT
                    COUNT(*) as num_transacciones,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE fecha_generacion BETWEEN :fecha_inicio AND :fecha_fin
            ");
            $stmt->bindParam(':fecha_inicio', $fechaInicioAnterior);
            $stmt->bindParam(':fecha_fin', $fechaFinAnterior);
            $stmt->execute();
            $kpisAnteriores = $stmt->fetch(PDO::FETCH_ASSOC);

            // Guardar valores de referencia
            $kpis['venta_total_anterior'] = $kpisAnteriores['venta_total'];
            $kpis['num_transacciones_anterior'] = $kpisAnteriores['num_transacciones'];

            // Calcular variaciones porcentuales
            $kpis['variacion_venta'] = $kpisAnteriores['venta_total'] > 0 ?
                                      round((($kpis['venta_total'] - $kpisAnteriores['venta_total']) / $kpisAnteriores['venta_total']) * 100, 1) : 0;

            $kpis['variacion_transacciones'] = $kpisAnteriores['num_transacciones'] > 0 ?
                                             round((($kpis['num_transacciones'] - $kpisAnteriores['num_transacciones']) / $kpisAnteriores['num_transacciones']) * 100, 1) : 0;

            // Meta mensual (ejemplo: 10% más que el mes anterior)
            $kpis['meta_mensual'] = round($kpisAnteriores['venta_total'] * 1.1);
            $kpis['progreso_meta'] = $kpis['meta_mensual'] > 0 ?
                                    round(($kpis['venta_total'] / $kpis['meta_mensual']) * 100, 1) : 0;

            $response['data'] = $kpis;
            break;

        case 'ventas_diarias':
            // Obtener ventas diarias para el período actual
            $stmt = $conn->prepare("
                SELECT
                    DATE(fecha_generacion) as fecha,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total,
                    COUNT(*) as num_transacciones
                FROM tb_facturas_dte
                WHERE fecha_generacion BETWEEN :fecha_inicio AND :fecha_fin
                GROUP BY DATE(fecha_generacion)
                ORDER BY fecha
            ");

            // Imprimir la consulta SQL para depuración
            error_log("SQL Query: SELECT DATE(fecha_generacion) as fecha, COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total, COUNT(*) as num_transacciones FROM tb_facturas_dte WHERE fecha_generacion BETWEEN '{$fechaInicio}' AND '{$fechaFin}' GROUP BY DATE(fecha_generacion) ORDER BY fecha");
            $stmt->bindParam(':fecha_inicio', $fechaInicio);
            $stmt->bindParam(':fecha_fin', $fechaFin);
            $stmt->execute();
            $ventasDiarias = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Asegurarse de que el día actual esté incluido en los resultados
            $hoy = date('Y-m-d');
            $fechaEncontrada = false;

            foreach ($ventasDiarias as $venta) {
                if ($venta['fecha'] === $hoy) {
                    $fechaEncontrada = true;
                    break;
                }
            }

            // Si el día actual no está en los resultados, añadirlo con valores en cero
            if (!$fechaEncontrada) {
                $ventasDiarias[] = [
                    'fecha' => $hoy,
                    'venta_total' => '0',
                    'num_transacciones' => '0'
                ];
            }

            // Ordenar el array por fecha
            usort($ventasDiarias, function($a, $b) {
                return strcmp($a['fecha'], $b['fecha']);
            });

            // Imprimir información de depuración
            error_log("Fecha actual: " . $hoy);
            error_log("Fecha inicio: " . $fechaInicio);
            error_log("Fecha fin: " . $fechaFin);
            error_log("Número de registros: " . count($ventasDiarias));
            foreach ($ventasDiarias as $venta) {
                error_log("Fecha: " . $venta['fecha'] . ", Venta: " . $venta['venta_total']);
            }

            $response['data'] = $ventasDiarias;
            break;

        case 'comparativa_mensual':
            // Obtener ventas mensuales para el año actual y anterior
            $anioActual = date('Y');
            $anioAnterior = date('Y', strtotime('-1 year'));

            // Ventas por mes del año actual
            $stmt = $conn->prepare("
                SELECT
                    MONTH(fecha_generacion) as mes,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE YEAR(fecha_generacion) = :anio
                GROUP BY MONTH(fecha_generacion)
                ORDER BY mes
            ");
            $stmt->bindParam(':anio', $anioActual);
            $stmt->execute();
            $ventasAnioActual = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Ventas por mes del año anterior
            $stmt = $conn->prepare("
                SELECT
                    MONTH(fecha_generacion) as mes,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE YEAR(fecha_generacion) = :anio
                GROUP BY MONTH(fecha_generacion)
                ORDER BY mes
            ");
            $stmt->bindParam(':anio', $anioAnterior);
            $stmt->execute();
            $ventasAnioAnterior = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response['data'] = [
                'anio_actual' => $ventasAnioActual,
                'anio_anterior' => $ventasAnioAnterior,
                'anio_actual_valor' => $anioActual,
                'anio_anterior_valor' => $anioAnterior
            ];
            break;

        case 'tendencia_anual':
            // Obtener ventas por mes para el año actual
            $anioActual = date('Y');

            $stmt = $conn->prepare("
                SELECT
                    MONTH(fecha_generacion) as mes,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE YEAR(fecha_generacion) = :anio
                GROUP BY MONTH(fecha_generacion)
                ORDER BY mes
            ");
            $stmt->bindParam(':anio', $anioActual);
            $stmt->execute();
            $ventasPorMes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calcular proyección para los meses restantes
            $mesActual = date('n');
            $ventasTotales = 0;
            $mesesConDatos = 0;

            foreach ($ventasPorMes as $venta) {
                if ($venta['mes'] <= $mesActual) {
                    $ventasTotales += $venta['venta_total'];
                    $mesesConDatos++;
                }
            }

            $promedioMensual = $mesesConDatos > 0 ? $ventasTotales / $mesesConDatos : 0;
            $proyeccion = [];

            for ($mes = 1; $mes <= 12; $mes++) {
                $encontrado = false;
                foreach ($ventasPorMes as $venta) {
                    if ($venta['mes'] == $mes) {
                        $proyeccion[] = [
                            'mes' => $mes,
                            'venta_total' => (float)$venta['venta_total'],
                            'es_proyeccion' => false
                        ];
                        $encontrado = true;
                        break;
                    }
                }

                if (!$encontrado) {
                    $proyeccion[] = [
                        'mes' => $mes,
                        'venta_total' => $mes <= $mesActual ? 0 : round($promedioMensual),
                        'es_proyeccion' => $mes > $mesActual
                    ];
                }
            }

            $response['data'] = $proyeccion;
            break;

        case 'estacionalidad':
            // Obtener ventas por día de la semana
            $stmt = $conn->prepare("
                SELECT
                    DAYOFWEEK(fecha_generacion) as dia_semana,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total,
                    COUNT(*) as num_transacciones
                FROM tb_facturas_dte
                WHERE fecha_generacion BETWEEN DATE_SUB(NOW(), INTERVAL 6 MONTH) AND NOW()
                GROUP BY DAYOFWEEK(fecha_generacion)
                ORDER BY dia_semana
            ");
            $stmt->execute();
            $ventasPorDia = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Mapear números de día a nombres
            $nombresDias = [
                1 => 'Domingo',
                2 => 'Lunes',
                3 => 'Martes',
                4 => 'Miércoles',
                5 => 'Jueves',
                6 => 'Viernes',
                7 => 'Sábado'
            ];

            foreach ($ventasPorDia as &$dia) {
                $dia['nombre_dia'] = $nombresDias[$dia['dia_semana']];
            }

            $response['data'] = $ventasPorDia;
            break;

        case 'distribucion_dte':
            // Obtener distribución de ventas por tipo de DTE
            $stmt = $conn->prepare("
                SELECT
                    tipo_dte,
                    COUNT(*) as cantidad,
                    COALESCE(SUM(JSON_UNQUOTE(JSON_EXTRACT(json_enviado, '$.Documento.Encabezado.Totales.MontoTotal'))), 0) as venta_total
                FROM tb_facturas_dte
                WHERE fecha_generacion BETWEEN :fecha_inicio AND :fecha_fin
                GROUP BY tipo_dte
                ORDER BY venta_total DESC
            ");
            $stmt->bindParam(':fecha_inicio', $fechaInicio);
            $stmt->bindParam(':fecha_fin', $fechaFin);
            $stmt->execute();
            $distribucionDTE = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Mapear tipos de DTE a nombres descriptivos
            $nombresDTE = [
                33 => 'Factura Electrónica',
                39 => 'Boleta Electrónica',
                61 => 'Nota de Crédito',
                56 => 'Nota de Débito'
            ];

            foreach ($distribucionDTE as &$dte) {
                $tipoDTE = $dte['tipo_dte'];
                $dte['nombre_dte'] = isset($nombresDTE[$tipoDTE]) ? $nombresDTE[$tipoDTE] : 'Tipo ' . $tipoDTE;
            }

            $response['data'] = $distribucionDTE;
            break;

        default:
            throw new Exception("Tipo de datos no válido");
    }

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
