<?php
// Archivo: generar_sobre.php
header('Content-Type: application/json');

// Incluir el manejador de errores personalizado
require_once 'error_log_handler.php';

// Incluir el logger normal
require_once 'logger.php';
Logger::init();

// Activar la visualización de errores para depuración
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Registrar el inicio del proceso en el log de errores
ErrorLogHandler::logError('Iniciando proceso de generación de sobre', [
    'fecha' => date('Y-m-d H:i:s'),
    'post_data' => $_POST,
    'server' => $_SERVER['SERVER_NAME']
], 'info');

// Start logging
Logger::log('Iniciando proceso de generación de sobre', 'info', [
    'fecha' => date('Y-m-d H:i:s'),
    'servidor' => $_SERVER['SERVER_NAME'],
    'ip' => $_SERVER['REMOTE_ADDR']
]);

// Verificar que los directorios necesarios existan y tengan permisos adecuados
$directoriosRequeridos = [
    'Documents/DTE/Facturas',
    'Documents/DTE/Boletas',
    'Documents/DTE/NotasCredito',
    'Documents/DTE/Otros',
    'Documents/sobreEnvio'
];

$directoriosProblematicos = [];

foreach ($directoriosRequeridos as $dir) {
    if (!file_exists($dir)) {
        // Intentar crear el directorio si no existe
        if (!mkdir($dir, 0755, true)) {
            $directoriosProblematicos[] = [
                'directorio' => $dir,
                'problema' => 'No existe y no se pudo crear',
                'error' => error_get_last()
            ];
        } else {
            error_log("Directorio creado: $dir");
        }
    } else if (!is_readable($dir)) {
        $directoriosProblematicos[] = [
            'directorio' => $dir,
            'problema' => 'No es legible',
            'permisos' => decoct(fileperms($dir))
        ];
    } else if (!is_writable($dir)) {
        $directoriosProblematicos[] = [
            'directorio' => $dir,
            'problema' => 'No es escribible',
            'permisos' => decoct(fileperms($dir))
        ];
    }
}

if (!empty($directoriosProblematicos)) {
    Logger::log("Problemas con directorios requeridos", 'error', $directoriosProblematicos);
    error_log("ADVERTENCIA: Se encontraron problemas con directorios requeridos: " . json_encode($directoriosProblematicos));
} else {
    Logger::log("Verificación de directorios completada", 'success', [
        'directorios_verificados' => count($directoriosRequeridos)
    ]);
}

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Incluir la conexión a la base de datos
require_once 'db_connection.php';
// Agrega esta línea después de require_once:
$conn = getConnection();

try {
    // Obtener el token de solicitud para prevenir solicitudes duplicadas
    $requestToken = isset($_POST['request_token']) ? $_POST['request_token'] : null;

    // Verificar si ya existe una solicitud en proceso con este token
    if ($requestToken) {
        // Crear tabla de control de solicitudes si no existe
        $conn->exec("CREATE TABLE IF NOT EXISTS tb_request_control (
            id INT AUTO_INCREMENT PRIMARY KEY,
            request_token VARCHAR(100) NOT NULL,
            request_type VARCHAR(50) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME NULL,
            status TINYINT DEFAULT 0,
            UNIQUE KEY (request_token)
        )");

        // Verificar si el token ya existe y está en proceso
        $stmt = $conn->prepare("SELECT id, status FROM tb_request_control WHERE request_token = :token");
        $stmt->bindParam(':token', $requestToken);
        $stmt->execute();
        $existingRequest = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingRequest) {
            // Si la solicitud ya existe y está en proceso (status = 0)
            if ($existingRequest['status'] == 0) {
                Logger::log("Solicitud duplicada detectada", 'warning', [
                    'token' => $requestToken,
                    'status' => 'en proceso'
                ]);
                echo json_encode([
                    'error' => 'Ya existe una solicitud en proceso con este token',
                    'request_id' => $existingRequest['id']
                ]);
                exit;
            }
            // Si la solicitud ya existe pero está completada (status = 1), continuamos con una nueva
            Logger::log("Token de solicitud reutilizado", 'info', [
                'token' => $requestToken,
                'status' => 'completada anteriormente'
            ]);
        } else {
            // Registrar la nueva solicitud
            $stmt = $conn->prepare("INSERT INTO tb_request_control (request_token, request_type) VALUES (:token, 'generar_sobre')");
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();
            $requestId = $conn->lastInsertId();

            Logger::log("Nueva solicitud registrada", 'info', [
                'token' => $requestToken,
                'id' => $requestId
            ]);
        }
    } else {
        // Si no hay token, generamos uno para control interno
        $requestToken = uniqid('gen_', true);
        Logger::log("Token de solicitud generado internamente", 'info', [
            'token' => $requestToken
        ]);
    }

    // Obtener el límite de documentos a procesar (predeterminado: 50)
    $limite = isset($_POST['limite']) ? (int)$_POST['limite'] : 50;
    // Obtener el tipo de DTE específico (opcional)
    $tipoDTE = isset($_POST['tipo_dte']) ? $_POST['tipo_dte'] : null;

    Logger::log("Configuración de procesamiento", 'info', [
        'limite' => $limite,
        'tipo_dte' => $tipoDTE ?: 'todos',
        'request_token' => $requestToken
    ]);

    // Validar el límite
    if ($limite <= 0 || $limite > 100) {
        $limite = 50; // Valor predeterminado si está fuera de rango
    }

    // Preparar la consulta base
    $sql = "SELECT nombre_archivo, id, tipo_dte, folio FROM tb_facturas_dte WHERE estado_sobre = 0";

    // Si hay un tipo específico de DTE
    if ($tipoDTE !== null) {
        $sql .= " AND tipo_dte = :tipo_dte";
    }

    // Agregar el límite
    $sql .= " ORDER BY id ASC LIMIT :limite";

    // Preparar la consulta
    $stmt = $conn->prepare($sql);

    // Vincular el límite primero
    $stmt->bindValue(':limite', $limite, PDO::PARAM_INT);

    // Vincular tipo_dte solo si está presente
    if ($tipoDTE !== null) {
        $stmt->bindValue(':tipo_dte', $tipoDTE);
    }

    // Ejecutar la consulta
    $stmt->execute();

    $documentos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Verificar y corregir rutas de archivos XML si es necesario
    foreach ($documentos as $key => $documento) {
        $xmlPath = $documento['nombre_archivo'];

        // Si el archivo no existe, intentar reconstruir la ruta
        if (!file_exists($xmlPath)) {
            error_log("Archivo XML no encontrado en la ruta original: " . $xmlPath);

            // Determinar directorio basado en el tipo de documento
            $tipoDTE = $documento['tipo_dte'];
            $folio = $documento['folio'];

            switch ($tipoDTE) {
                case '33': // Factura Electrónica (33)
                case '34': // Factura Exenta (34)
                    $directorio = 'Documents/DTE/Facturas/';
                    break;
                case '39': // Boleta (39)
                    $directorio = 'Documents/DTE/Boletas/';
                    break;
                case '61': // Nota de Crédito (61)
                    $directorio = 'Documents/DTE/NotasCredito/';
                    break;
                default:
                    $directorio = 'Documents/DTE/Otros/';
                    break;
            }

            // Buscar archivos que coincidan con el patrón en el directorio correcto
            if (file_exists($directorio) && is_readable($directorio)) {
                $files = scandir($directorio);
                $matchingFiles = [];

                foreach ($files as $file) {
                    // Buscar archivos que contengan el tipo de DTE y el folio
                    if (strpos($file, "DTE_{$tipoDTE}_{$folio}_") !== false &&
                        pathinfo($file, PATHINFO_EXTENSION) === 'xml') {
                        $matchingFiles[] = $file;
                    }
                }

                // Si encontramos archivos que coinciden
                if (!empty($matchingFiles)) {
                    // Usar el archivo más reciente (asumiendo que el formato de nombre incluye fecha)
                    usort($matchingFiles, function($a, $b) {
                        return strcmp($b, $a); // Orden descendente
                    });

                    $newXmlPath = $directorio . $matchingFiles[0];
                    error_log("Archivo XML encontrado en ruta alternativa: " . $newXmlPath);

                    // Actualizar la ruta en el array de documentos
                    $documentos[$key]['nombre_archivo'] = $newXmlPath;

                    // Actualizar la ruta en la base de datos
                    try {
                        $updateStmt = $conn->prepare("UPDATE tb_facturas_dte SET nombre_archivo = :nueva_ruta WHERE id = :id");
                        $updateStmt->bindParam(':nueva_ruta', $newXmlPath);
                        $updateStmt->bindParam(':id', $documento['id']);
                        $updateStmt->execute();

                        error_log("Ruta actualizada en la base de datos para el documento ID: " . $documento['id']);
                        Logger::log("Ruta de archivo XML corregida", 'success', [
                            'id' => $documento['id'],
                            'ruta_anterior' => $xmlPath,
                            'nueva_ruta' => $newXmlPath
                        ]);
                    } catch (Exception $e) {
                        error_log("Error al actualizar la ruta en la base de datos: " . $e->getMessage());
                    }
                } else {
                    error_log("No se encontraron archivos XML alternativos para DTE tipo {$tipoDTE}, folio {$folio}");
                }
            } else {
                error_log("El directorio {$directorio} no existe o no es legible");
            }
        }
    }

    Logger::log("Documentos encontrados: " . count($documentos), 'info', [
        'tipo_dte' => $tipoDTE ?: 'todos',
        'cantidad' => count($documentos)
    ]);

    if (empty($documentos)) {
        echo json_encode(['error' => 'No hay documentos pendientes de envío' . ($tipoDTE ? " para el tipo DTE $tipoDTE" : '')]);
        exit;
    }

    // Registrar cantidad de documentos encontrados
    error_log("Documentos pendientes encontrados: " . count($documentos));

    // Extraer los IDs y rutas para procesar después
    $documentosIds = array_column($documentos, 'id');
    $xmlPaths = array_column($documentos, 'nombre_archivo');

    // Añadir log explícito para verificar archivos XML pendientes
    Logger::log("Verificando archivos XML pendientes", 'info', [
        'total_encontrados' => count($documentos),
        'total_facturas' => count($documentos)
    ]);

    // Verificar explícitamente si hay archivos XML faltantes
    $faltantes = [];
    foreach ($xmlPaths as $index => $xmlPath) {
        // Verificar si el archivo existe
        $fileExists = file_exists($xmlPath);

        // Obtener información detallada sobre la ruta
        $pathInfo = pathinfo($xmlPath);
        $dirExists = file_exists($pathInfo['dirname']);
        $dirPerms = $dirExists ? decoct(fileperms($pathInfo['dirname'])) : 'N/A';

        // Verificar si el directorio es accesible
        $dirReadable = $dirExists ? is_readable($pathInfo['dirname']) : false;
        $dirWritable = $dirExists ? is_writable($pathInfo['dirname']) : false;

        // Verificar si hay algún archivo con nombre similar en el directorio
        $similarFiles = [];
        if ($dirExists && $dirReadable) {
            $files = scandir($pathInfo['dirname']);
            $baseName = $pathInfo['filename'];
            foreach ($files as $file) {
                if (strpos($file, $baseName) !== false) {
                    $similarFiles[] = $file;
                }
            }
        }

        if (!$fileExists) {
            $faltantes[] = [
                'id' => $documentos[$index]['id'] ?? 'desconocido',
                'tipo_dte' => $documentos[$index]['tipo_dte'] ?? 'desconocido',
                'ruta' => $xmlPath,
                'directorio' => $pathInfo['dirname'],
                'directorio_existe' => $dirExists ? 'sí' : 'no',
                'directorio_permisos' => $dirPerms,
                'directorio_readable' => $dirReadable ? 'sí' : 'no',
                'directorio_writable' => $dirWritable ? 'sí' : 'no',
                'archivos_similares' => $similarFiles,
                'ruta_absoluta' => realpath($pathInfo['dirname']) . '/' . $pathInfo['basename'],
                'documento_id' => $documentos[$index]['id'] ?? 'desconocido'
            ];
        }
    }

    if (!empty($faltantes)) {
        Logger::log("ARCHIVOS XML NO ENCONTRADOS", 'error', $faltantes);

        // Registrar en error_log para depuración en consola
        error_log("ERROR: Se encontraron " . count($faltantes) . " archivos XML faltantes");
        error_log("DETALLES DE ARCHIVOS FALTANTES:");

        foreach ($faltantes as $idx => $faltante) {
            error_log("  Archivo faltante #" . ($idx + 1) . ":");
            error_log("    ID: " . $faltante['id']);
            error_log("    Tipo DTE: " . $faltante['tipo_dte']);
            error_log("    Ruta: " . $faltante['ruta']);
            error_log("    Directorio: " . $faltante['directorio'] . " (Existe: " . $faltante['directorio_existe'] . ")");
            error_log("    Permisos directorio: " . $faltante['directorio_permisos']);

            if (!empty($faltante['archivos_similares'])) {
                error_log("    Archivos similares encontrados: " . implode(", ", $faltante['archivos_similares']));
            } else {
                error_log("    No se encontraron archivos similares en el directorio");
            }

            // Verificar si hay algún problema con la ruta en la base de datos
            $stmt = $conn->prepare("SELECT nombre_archivo FROM tb_facturas_dte WHERE id = :id");
            $stmt->bindParam(':id', $faltante['documento_id']);
            $stmt->execute();
            $dbPath = $stmt->fetchColumn();

            if ($dbPath !== $faltante['ruta']) {
                error_log("    ADVERTENCIA: La ruta en la base de datos no coincide con la ruta utilizada");
                error_log("    Ruta en DB: " . $dbPath);
                error_log("    Ruta usada: " . $faltante['ruta']);
            }
        }

        // Verificar si hay algún problema con la estructura de directorios
        $directorioBase = dirname($xmlPaths[0] ?? '.');
        error_log("Verificando estructura de directorios para XML:");
        error_log("  Directorio base: " . $directorioBase);
        error_log("  Existe: " . (file_exists($directorioBase) ? 'Sí' : 'No'));

        if (file_exists($directorioBase)) {
            error_log("  Permisos: " . decoct(fileperms($directorioBase)));
            error_log("  Es legible: " . (is_readable($directorioBase) ? 'Sí' : 'No'));
            error_log("  Es escribible: " . (is_writable($directorioBase) ? 'Sí' : 'No'));

            // Listar algunos archivos en el directorio
            $files = scandir($directorioBase);
            $fileList = array_slice($files, 0, 10); // Mostrar solo los primeros 10 archivos
            error_log("  Primeros archivos en el directorio: " . implode(", ", $fileList) . (count($files) > 10 ? "... (y " . (count($files) - 10) . " más)" : ""));
        }
    }

    // Verificar que los archivos XML existan
    $xmlPathsValidos = [];
    $tiposDTE = [];
    $tiposDTEConteo = [];

    // Registrar en consola el inicio de la verificación
    error_log("Iniciando verificación de " . count($xmlPaths) . " archivos XML");

    foreach ($xmlPaths as $index => $xmlPath) {
        $tipoDTE = $documentos[$index]['tipo_dte'] ?? 'desconocido';

        // Contabilizar tipos de DTE para diagnóstico
        if (!isset($tiposDTEConteo[$tipoDTE])) {
            $tiposDTEConteo[$tipoDTE] = 0;
        }
        $tiposDTEConteo[$tipoDTE]++;

        Logger::log("Verificando archivo XML", 'debug', [
            'ruta' => $xmlPath,
            'tamaño' => file_exists($xmlPath) ? filesize($xmlPath) : 'no existe',
            'permisos' => file_exists($xmlPath) ? decoct(fileperms($xmlPath)) : 'N/A'
        ]);

        if (file_exists($xmlPath)) {
            $xmlPathsValidos[] = [
                'path' => $xmlPath,
                'id' => $documentos[$index]['id'],
                'tipo_dte' => $tipoDTE
            ];

            // Registrar cada tipo de DTE encontrado
            if (!in_array($tipoDTE, $tiposDTE)) {
                $tiposDTE[] = $tipoDTE;
            }

            Logger::log("Archivo XML encontrado: $xmlPath", 'success');
        } else {
            error_log("Archivo XML no encontrado: " . $xmlPath);
            Logger::log("Archivo XML no encontrado: $xmlPath", 'error');
        }
    }

    // Registrar resumen de tipos de DTE encontrados
    error_log("Resumen de tipos de DTE encontrados:");
    foreach ($tiposDTEConteo as $tipo => $cantidad) {
        error_log("  Tipo DTE $tipo: $cantidad documentos");
    }

    // Registrar resumen de archivos válidos
    error_log("Total archivos XML válidos: " . count($xmlPathsValidos) . " de " . count($xmlPaths));

    // Si hay múltiples tipos de DTE, registrar advertencia
    if (count($tiposDTE) > 1) {
        error_log("ADVERTENCIA: Se encontraron múltiples tipos de DTE: " . implode(", ", $tiposDTE));
        error_log("Los sobres deben contener documentos del mismo tipo de DTE");
    }

    if (empty($xmlPathsValidos)) {
        // Diagnosticar problemas de permisos en el directorio principal
        $directorioBase = dirname($xmlPaths[0] ?? '.');

        // Recopilar información detallada sobre el error para diagnóstico
        $errorDiagnostico = [
            'directorio' => $directorioBase,
            'existe_directorio' => file_exists($directorioBase) ? 'sí' : 'no',
            'permisos_directorio' => file_exists($directorioBase) ? decoct(fileperms($directorioBase)) : 'N/A',
            'is_readable_directorio' => is_readable($directorioBase) ? 'sí' : 'no',
            'is_writable_directorio' => is_writable($directorioBase) ? 'sí' : 'no',
            'usuario_php' => function_exists('posix_getpwuid') ? (posix_getpwuid(posix_geteuid())['name'] ?? 'desconocido') : 'función posix no disponible',
            'rutas_verificadas' => $xmlPaths,
            'cantidad_archivos_db' => count($documentos),
            'documentos_ids' => array_column($documentos, 'id'),
            'documentos_tipos' => array_column($documentos, 'tipo_dte'),
            'documentos_folios' => array_column($documentos, 'folio')
        ];

        // Detalles de archivos individuales
        $infoArchivos = [];
        foreach ($xmlPaths as $index => $xmlPath) {
            // Obtener información del directorio donde debería estar el archivo
            $dirInfo = pathinfo($xmlPath);
            $dirPath = $dirInfo['dirname'];
            $dirExists = file_exists($dirPath);

            // Verificar si hay archivos similares en el directorio
            $similarFiles = [];
            if ($dirExists && is_readable($dirPath)) {
                $allFiles = scandir($dirPath);
                $baseName = pathinfo($xmlPath, PATHINFO_FILENAME);
                foreach ($allFiles as $file) {
                    if (strpos($file, substr($baseName, 0, 10)) !== false) {
                        $similarFiles[] = $file;
                    }
                }
            }

            // Verificar si hay archivos en directorios alternativos
            $alternativeDirs = [
                'Documents/DTE/Facturas/',
                'Documents/DTE/Boletas/',
                'Documents/DTE/NotasCredito/',
                'Documents/DTE/Otros/'
            ];

            $alternativeFiles = [];
            $tipoDTE = $documentos[$index]['tipo_dte'] ?? '';
            $folio = $documentos[$index]['folio'] ?? '';

            if ($tipoDTE && $folio) {
                foreach ($alternativeDirs as $altDir) {
                    if (file_exists($altDir) && is_readable($altDir)) {
                        $altFiles = scandir($altDir);
                        foreach ($altFiles as $file) {
                            if (strpos($file, "DTE_{$tipoDTE}_{$folio}_") !== false) {
                                $alternativeFiles[] = $altDir . $file;
                            }
                        }
                    }
                }
            }

            $infoArchivos[] = [
                'id' => $documentos[$index]['id'] ?? 'desconocido',
                'tipo_dte' => $documentos[$index]['tipo_dte'] ?? 'desconocido',
                'folio' => $documentos[$index]['folio'] ?? 'desconocido',
                'ruta' => $xmlPath,
                'existe' => file_exists($xmlPath) ? 'sí' : 'no',
                'tamaño' => file_exists($xmlPath) ? filesize($xmlPath) . ' bytes' : 'N/A',
                'permisos' => file_exists($xmlPath) ? decoct(fileperms($xmlPath)) : 'N/A',
                'is_readable' => is_readable($xmlPath) ? 'sí' : 'no',
                'directorio' => $dirPath,
                'directorio_existe' => $dirExists ? 'sí' : 'no',
                'permisos_directorio' => $dirExists ? decoct(fileperms($dirPath)) : 'N/A',
                'archivos_similares' => $similarFiles,
                'archivos_alternativos' => $alternativeFiles
            ];
        }
        $errorDiagnostico['info_archivos'] = $infoArchivos;

        // Verificar si hay algún problema con la estructura de directorios
        $directoriosEstado = [];
        $directoriosRequeridos = [
            'Documents/DTE/Facturas',
            'Documents/DTE/Boletas',
            'Documents/DTE/NotasCredito',
            'Documents/DTE/Otros',
            'Documents/sobreEnvio'
        ];

        foreach ($directoriosRequeridos as $dir) {
            $directoriosEstado[$dir] = [
                'existe' => file_exists($dir) ? 'sí' : 'no',
                'permisos' => file_exists($dir) ? decoct(fileperms($dir)) : 'N/A',
                'is_readable' => is_readable($dir) ? 'sí' : 'no',
                'is_writable' => is_writable($dir) ? 'sí' : 'no'
            ];
        }
        $errorDiagnostico['directorios_estado'] = $directoriosEstado;

        // Registrar en el log para diagnóstico
        Logger::log("Diagnóstico de fallo en archivos XML", 'error', $errorDiagnostico);

        // Intentar corregir automáticamente las rutas en la base de datos
        $correccionesRealizadas = 0;
        foreach ($infoArchivos as $info) {
            if (!empty($info['archivos_alternativos'])) {
                // Usar el primer archivo alternativo encontrado
                $nuevaRuta = $info['archivos_alternativos'][0];

                try {
                    $updateStmt = $conn->prepare("UPDATE tb_facturas_dte SET nombre_archivo = :nueva_ruta WHERE id = :id");
                    $updateStmt->bindParam(':nueva_ruta', $nuevaRuta);
                    $updateStmt->bindParam(':id', $info['id']);
                    $updateStmt->execute();

                    $correccionesRealizadas++;

                    Logger::log("Ruta de archivo XML corregida automáticamente", 'success', [
                        'id' => $info['id'],
                        'ruta_anterior' => $info['ruta'],
                        'nueva_ruta' => $nuevaRuta
                    ]);
                } catch (Exception $e) {
                    error_log("Error al actualizar la ruta en la base de datos: " . $e->getMessage());
                }
            }
        }

        $errorDiagnostico['correcciones_realizadas'] = $correccionesRealizadas;

        // Si se realizaron correcciones, sugerir al usuario que intente nuevamente
        if ($correccionesRealizadas > 0) {
            echo json_encode([
                'error' => 'Se corrigieron rutas de archivos XML en la base de datos',
                'details' => [
                    'message' => "Se corrigieron $correccionesRealizadas rutas de archivos XML. Por favor, intente generar el sobre nuevamente.",
                    'code' => 404,
                    'file' => __FILE__,
                    'line' => __LINE__,
                    'diagnostico' => $errorDiagnostico,
                    'correcciones' => $correccionesRealizadas
                ],
                'logs' => Logger::getLogs()
            ]);
        } else {
            echo json_encode([
                'error' => 'Ninguno de los archivos XML pendientes fue encontrado',
                'details' => [
                    'message' => 'Falló el acceso a los archivos XML de facturas. No se pudieron corregir automáticamente las rutas.',
                    'code' => 404,
                    'file' => __FILE__,
                    'line' => __LINE__,
                    'diagnostico' => $errorDiagnostico
                ],
                'logs' => Logger::getLogs()
            ]);
        }
        exit;
    }

    // Verificar que todos los documentos sean del mismo tipo
    $tiposDTEUnicos = array_unique(array_column($documentos, 'tipo_dte'));
    if (count($tiposDTEUnicos) > 1) {
        // Contar cuántos documentos hay de cada tipo
        $conteoTipos = [];
        foreach ($documentos as $doc) {
            $tipo = $doc['tipo_dte'];
            if (!isset($conteoTipos[$tipo])) {
                $conteoTipos[$tipo] = 0;
            }
            $conteoTipos[$tipo]++;
        }

        // Crear un mensaje detallado
        $mensajeDetallado = "ERROR DE CANTIDAD: Se encontraron múltiples tipos de DTE en la selección.\n";
        $mensajeDetallado .= "Los sobres deben contener documentos del mismo tipo.\n\n";
        $mensajeDetallado .= "Tipos encontrados:\n";

        foreach ($conteoTipos as $tipo => $cantidad) {
            $mensajeDetallado .= "- Tipo $tipo: $cantidad documento(s)\n";
        }

        $mensajeDetallado .= "\nSolución: Procese cada tipo de DTE por separado usando el menú desplegable.";

        // Registrar en el log con detalles completos
        Logger::log("Error: Se intentó generar un sobre con múltiples tipos de DTE", 'error', [
            'tipos_encontrados' => $tiposDTEUnicos,
            'conteo_por_tipo' => $conteoTipos,
            'detalle_documentos' => array_map(function($doc) {
                return [
                    'id' => $doc['id'],
                    'tipo_dte' => $doc['tipo_dte'],
                    'nombre_archivo' => basename($doc['nombre_archivo'])
                ];
            }, $documentos)
        ]);

        // Registrar en error_log para la consola
        error_log("ERROR DE CANTIDAD: Múltiples tipos de DTE detectados");
        error_log("Tipos encontrados: " . implode(', ', $tiposDTEUnicos));
        foreach ($conteoTipos as $tipo => $cantidad) {
            error_log("  Tipo $tipo: $cantidad documento(s)");
        }
        error_log("Solución: Procese cada tipo de DTE por separado");

        // Devolver respuesta JSON con detalles completos
        echo json_encode([
            'error' => 'ERROR DE CANTIDAD: No se pueden incluir diferentes tipos de DTE en el mismo sobre',
            'tipos_encontrados' => $tiposDTEUnicos,
            'conteo_por_tipo' => $conteoTipos,
            'detalle_error' => 'Los sobres deben contener documentos del mismo tipo. Se encontraron documentos de tipos: ' . implode(', ', $tiposDTEUnicos),
            'mensaje_detallado' => $mensajeDetallado,
            'error_categoria' => 'multiple_dte_types'
        ]);
        exit;
    }

    // Ruta al certificado
    $certificadoPath = 'Documents/17365958-K.pfx';
    Logger::log("Verificando certificado: $certificadoPath", 'info');
    if (!file_exists($certificadoPath)) {
        echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
        Logger::log("Certificado no encontrado", 'error');
        exit;
    }
    Logger::log("Certificado válido", 'success');

    // Datos para la caratula
    $jsonInput = json_encode([
        'Certificado' => [
            'Password' => '1569',
            'Rut' => '17365958-K'
        ],
        'Caratula' => [
            'RutEmisor' => '78078979-4',
            'RutReceptor' => '60803000-K',
            'FechaResolucion' => '2014-08-22',
            'NumeroResolucion' => 80
        ]
    ]);

    // Configurar la solicitud cURL
    $ch = curl_init('https://api.simpleapi.cl/api/v1/envio/generar');

    // Preparar el formulario multipart
    $boundary = uniqid();
    $delimiter = '-------------' . $boundary;

    // Construir el cuerpo de la solicitud
    $postData = '';

    // Agregar el campo JSON
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
    $postData .= $jsonInput . "\r\n";

    // Agregar el archivo de certificado
    $fileContents = file_get_contents($certificadoPath);
    $postData .= "--" . $delimiter . "\r\n";
    $postData .= 'Content-Disposition: form-data; name="files"; filename="' . basename($certificadoPath) . '"' . "\r\n";
    $postData .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
    $postData .= $fileContents . "\r\n";

    // Crear la tabla de logs de errores si no existe
    try {
        $conn->exec("
            CREATE TABLE IF NOT EXISTS tb_error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tipo VARCHAR(50) NOT NULL,
                mensaje TEXT NOT NULL,
                detalles TEXT,
                fecha DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ");
    } catch (Exception $tableEx) {
        error_log("Error al crear tabla de logs: " . $tableEx->getMessage());
    }

    // Añadir justo antes de procesar los XMLs
    foreach ($xmlPathsValidos as $index => $xmlInfo) {
        $xmlPath = $xmlInfo['path'];

        // Guardar detalles específicos de cada archivo para diagnóstico
        $fileDetails = [
            'id' => $xmlInfo['id'],
            'tipo_dte' => $xmlInfo['tipo_dte'],
            'ruta' => $xmlPath,
            'existe' => file_exists($xmlPath) ? 'sí' : 'no',
            'tamaño' => file_exists($xmlPath) ? filesize($xmlPath) : 'N/A',
            'permisos' => file_exists($xmlPath) ? decoct(fileperms($xmlPath)) : 'N/A',
            'readable' => is_readable($xmlPath) ? 'sí' : 'no'
        ];

        // Guardar estos detalles en un log de errores o en sesión
        error_log("Detalles XML #{$index}: " . json_encode($fileDetails));

        // También puedes guardarlo en una tabla de la base de datos
        try {
            $stmt = $conn->prepare("INSERT INTO tb_error_logs (tipo, mensaje, detalles, fecha)
                                   VALUES ('xml_check', :mensaje, :detalles, NOW())");
            $mensaje = "Verificación archivo XML ID: " . $xmlInfo['id'];
            $detallesJson = json_encode($fileDetails);
            $stmt->bindParam(':mensaje', $mensaje);
            $stmt->bindParam(':detalles', $detallesJson);
            $stmt->execute();
        } catch (Exception $logEx) {
            // Ignorar errores de logging para no interrumpir el flujo
            error_log("Error al registrar log: " . $logEx->getMessage());
        }
    }

    // Agregar todos los archivos XML del DTE usando el mismo nombre de parámetro "files2"
    $xmlCounter = 0;
    $xmlIncluidos = [];
    $tiposDTE = [];

    foreach ($xmlPathsValidos as $xmlInfo) {
        $xmlPath = $xmlInfo['path'];
        $tipoDTE = $xmlInfo['tipo_dte'];

        try {
            $fileContents = file_get_contents($xmlPath);

            if ($fileContents !== false) {
                $postData .= "--" . $delimiter . "\r\n";
                // IMPORTANTE: Todos los archivos XML usan el mismo nombre de parámetro "files2"
                $postData .= 'Content-Disposition: form-data; name="files2"; filename="' . basename($xmlPath) . '"' . "\r\n";
                $postData .= 'Content-Type: application/xml' . "\r\n\r\n";
                $postData .= $fileContents . "\r\n";

                $xmlCounter++;
                $xmlIncluidos[] = $xmlInfo;

                // Registrar los tipos de DTE para el nombre del archivo
                if (!in_array($tipoDTE, $tiposDTE)) {
                    $tiposDTE[] = $tipoDTE;
                }

                error_log("Archivo XML #{$xmlCounter} añadido: " . basename($xmlPath));
                Logger::log("Agregando archivo al sobre: " . basename($xmlInfo['path']), 'info', [
                    'tipo_dte' => $xmlInfo['tipo_dte'],
                    'id' => $xmlInfo['id']
                ]);
            } else {
                error_log("Error al leer el archivo: " . $xmlPath);
            }
        } catch (Exception $e) {
            error_log("Error al procesar XML {$xmlPath}: " . $e->getMessage());
        }
    }

    if ($xmlCounter === 0) {
        echo json_encode(['error' => 'No se pudo leer ningún archivo XML válido']);
        exit;
    }

    // Cerrar el cuerpo del mensaje
    $postData .= "--" . $delimiter . "--\r\n";

    // Configuración de cURL
    $apiKey = $_POST['apiKey'] ?? '2037-N680-6391-2493-5987'; // Token de autorización
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_HTTPHEADER => [
            "Authorization: $apiKey",
            "Content-Type: multipart/form-data; boundary=" . $delimiter,
            "Content-Length: " . strlen($postData)
        ]
    ]);

    // Ejecutar la solicitud
    Logger::log("Preparando solicitud API", 'info', [
        'url' => 'https://api.simpleapi.cl/api/v1/envio/generar',
        'archivos_incluidos' => count($xmlPathsValidos),
        'tipos_dte' => $tiposDTE
    ]);
    Logger::log("Enviando solicitud a la API", 'info');
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $curlInfo = curl_getinfo($ch);
    curl_close($ch);

    // Registrar información de la respuesta para diagnóstico
    error_log("Respuesta API sobre: Código=$httpCode, Tamaño=" . strlen($response));
    error_log("Primeros 200 bytes: " . substr($response, 0, 200));
    Logger::log("Respuesta recibida de API", 'info', [
        'http_code' => $httpCode,
        'content_length' => strlen($response),
        'content_type' => $curlInfo['content_type'] ?? 'desconocido'
    ]);

    // Si hubo algún error con cURL
    if ($curlError) {
        echo json_encode(['error' => 'Error al conectar con la API: ' . $curlError]);
        exit;
    }

    // Si la respuesta no fue exitosa
    if ($httpCode !== 200) {
        $errorDetails = json_decode($response, true);

        // Preparar reporte de error detallado para diagnóstico
        $diagnostico = [
            'error_http' => [
                'http_code' => $httpCode,
                'status_message' => $curlInfo['http_code'] ?? 'desconocido'
            ],
            'request_info' => [
                'url' => 'https://api.simpleapi.cl/api/v1/envio/generar',
                'request_size' => strlen($postData),
                'request_token' => $requestToken ?? 'no generado',
                'documentos_solicitados' => count($xmlPaths),
                'documentos_validos' => count($xmlPathsValidos),
                'tipos_dte' => $tiposDTE,
            ],
            'response_info' => [
                'response_size' => strlen($response),
                'content_type' => $curlInfo['content_type'] ?? 'desconocido',
                'tiempo_total' => ($curlInfo['total_time'] ?? 0) . ' segundos',
                'primera_linea_respuesta' => substr($response, 0, 100) . '...'
            ],
            'server_info' => [
                'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'desconocido',
                'php_version' => PHP_VERSION,
                'max_execution_time' => ini_get('max_execution_time'),
                'memoria_limite' => ini_get('memory_limit'),
                'fecha_hora_error' => date('Y-m-d H:i:s')
            ]
        ];

        // Extraer más información del error JSON si está disponible
        if ($errorDetails) {
            // Guardar todos los campos disponibles en el diagnóstico
            $diagnostico['api_error_json'] = $errorDetails;
        } else {
            // Intentar extraer información útil de la respuesta no-JSON
            $diagnostico['response_raw'] = [
                'primeros_500_caracteres' => substr($response, 0, 500),
                'tipo_respuesta' => is_string($response) ? 'texto/posible xml' : gettype($response)
            ];

            // Intentar analizar como XML si parece ser XML
            if (is_string($response) && (strpos($response, '<?xml') !== false || strpos($response, '<') === 0)) {
                try {
                    $xml = new SimpleXMLElement($response);
                    $diagnostico['response_raw']['es_xml'] = true;
                    // Convertir XML a array para mejor visualización
                    $diagnostico['response_raw']['xml_como_array'] = json_decode(json_encode($xml), true);
                } catch (Exception $xmlEx) {
                    $diagnostico['response_raw']['es_xml'] = false;
                    $diagnostico['response_raw']['xml_error'] = $xmlEx->getMessage();
                }
            }
        }

        // Registrar el error en múltiples lugares
        error_log("ERROR API SimpleAPI: Código HTTP $httpCode");
        error_log("Respuesta completa: " . $response);

        // Construir mensaje de error claro
        $errorMessage = 'Error al generar el sobre. Código HTTP: ' . $httpCode;

        // Extraer mensaje de error más detallado si está disponible
        if ($errorDetails && isset($errorDetails['message'])) {
            $errorMessage .= ' - ' . $errorDetails['message'];
        } elseif ($errorDetails && isset($errorDetails['error'])) {
            $errorMessage .= ' - ' . $errorDetails['error'];
        }

        // Registrar en el logger normal con diagnóstico detallado
        Logger::log("Error en la respuesta de la API", 'error', $diagnostico);

        // Registrar en el manejador de errores personalizado
        ErrorLogHandler::logError("Error en la API SimpleAPI", [
            'diagnostico' => $diagnostico,
            'error_message' => $errorMessage,
            'xml_incluidos' => count($xmlIncluidos),
            'request_token' => $requestToken ?? 'no disponible'
        ], 'api_error');

        // Guardar la respuesta completa en un archivo para análisis
        $errorResponseFile = 'api_error_' . date('Ymd_His') . '.log';
        file_put_contents($errorResponseFile, "DIAGNÓSTICO COMPLETO:\n" . json_encode($diagnostico, JSON_PRETTY_PRINT) . "\n\nRESPONSE:\n$response\n\nCURL INFO:\n" . print_r($curlInfo, true));

        // Guardar en la base de datos
        try {
            $stmt = $conn->prepare("INSERT INTO tb_error_logs (tipo, mensaje, detalles, fecha)
                                   VALUES ('api_error', :mensaje, :detalles, NOW())");
            $detallesJson = json_encode($diagnostico);
            $stmt->bindParam(':mensaje', $errorMessage);
            $stmt->bindParam(':detalles', $detallesJson);
            $stmt->execute();

            $errorId = $conn->lastInsertId();
        } catch (Exception $dbEx) {
            error_log("Error al guardar error de API en base de datos: " . $dbEx->getMessage());
        }

        echo json_encode([
            'error' => $errorMessage,
            'error_id' => $errorId ?? null,
            'error_file' => $errorResponseFile,
            'details' => $diagnostico,
            'response_details' => $errorDetails ?: ['raw_response' => substr($response, 0, 1000) . '...'],
            'logs' => Logger::getLogs()
        ]);
        exit;
    }

    // Procesar la respuesta exitosa
    $directorio = 'Documents/sobreEnvio/';

    // Crear directorio si no existe
    if (!file_exists($directorio)) {
        if (!mkdir($directorio, 0755, true)) {
            echo json_encode(['error' => 'Error al crear el directorio: ' . $directorio]);
            exit;
        }
    }

    // Generar nombre de archivo para el sobre con formato más detallado
    // Incluye tipos de DTE y formato completo de fecha con hora y minutos
    $fecha = date('Ymd_His');

    // Crear un string con los tipos de DTE incluidos
    $tiposDTEStr = implode('-', $tiposDTE);
    $nombreArchivo = "sobreEnvio_DTE{$tiposDTEStr}_{$fecha}_{$xmlCounter}docs.xml";
    $rutaCompleta = $directorio . $nombreArchivo;

    // Guardar el sobre XML
    Logger::log("Guardando sobre en: $rutaCompleta", 'info', [
        'nombre_archivo' => $nombreArchivo,
        'ruta' => $rutaCompleta,
        'tamaño_respuesta' => strlen($response)
    ]);

    // Intentar crear el directorio con permisos 0777 si no existe
    if (!file_exists($directorio)) {
        Logger::log("Creando directorio: $directorio", 'info');
        if (!mkdir($directorio, 0777, true)) {
            $error = error_get_last();
            Logger::log("Error al crear directorio", 'error', [
                'directorio' => $directorio,
                'error' => $error['message'] ?? 'Unknown error'
            ]);
            echo json_encode(['error' => 'Error al crear el directorio: ' . $directorio]);
            exit;
        }
        // Asegurar permisos adecuados
        chmod($directorio, 0777);
        Logger::log("Directorio creado con permisos 0777", 'success');
    } else {
        // Verificar y actualizar los permisos si el directorio ya existe
        Logger::log("Verificando permisos del directorio existente", 'info', [
            'permisos_actuales' => decoct(fileperms($directorio) & 0777)
        ]);
        chmod($directorio, 0777);
    }

    $bytesWritten = file_put_contents($rutaCompleta, $response);
    if ($bytesWritten === false) {
        $errorInfo = error_get_last();
        Logger::log("Error al guardar el archivo del sobre", 'error', [
            'ruta' => $rutaCompleta,
            'permisos_directorio' => decoct(fileperms($directorio)),
            'is_writable' => is_writable($directorio) ? 'sí' : 'no',
            'error' => $errorInfo['message'] ?? 'Unknown error'
        ]);
        echo json_encode([
            'error' => 'Error al guardar el archivo del sobre',
            'details' => [
                'ruta' => $rutaCompleta,
                'permisos' => decoct(fileperms($directorio)),
                'writable' => is_writable($directorio),
                'error' => $errorInfo['message'] ?? null
            ]
        ]);
        exit;
    }
    Logger::log("Sobre guardado: $bytesWritten bytes escritos", 'success');

    // Analizar la respuesta XML para obtener el TrackID
    $trackId = null;
    try {
        $xml = new SimpleXMLElement($response);
        $namespaces = $xml->getNamespaces(true);

        // Buscar el TrackID, la estructura XML puede variar según la API
        if (isset($xml->TrackId)) {
            $trackId = (string)$xml->TrackId;
        } elseif (isset($xml->TRACKID)) {
            $trackId = (string)$xml->TRACKID;
        }

        error_log("TrackID obtenido: " . ($trackId ?: 'No encontrado'));
        Logger::log("TrackID obtenido del XML", 'info', [
            'trackid' => $trackId ?: 'No encontrado'
        ]);
    } catch (Exception $e) {
        error_log("Error al procesar XML de respuesta: " . $e->getMessage());
    }

    // Extraer los datos de la caratula desde el JSON input
    $inputData = json_decode($jsonInput, true);
    $rutEmisor = $inputData['Caratula']['RutEmisor'] ?? '';
    $rutReceptor = $inputData['Caratula']['RutReceptor'] ?? '';
    $fechaResolucion = $inputData['Caratula']['FechaResolucion'] ?? '';
    $numeroResolucion = $inputData['Caratula']['NumeroResolucion'] ?? '';
    $certificadoRut = $inputData['Certificado']['Rut'] ?? '';

    // Determinar el tipo de envío basado en los tipos de DTE que contiene el sobre
    $tipoEnvio = 'ENVIO_DTE'; // Valor predeterminado

    // Si solo hay un tipo de DTE en el sobre, establecer el tipo de envío específico
    if (count($tiposDTE) === 1) {
        switch ($tiposDTE[0]) {
            case '39': // Boletas
                $tipoEnvio = 'ENVIO_BOLETA';
                break;
            case '33': // Facturas
                $tipoEnvio = 'ENVIO_DTE';
                break;
            case '61': // Notas de Crédito
                $tipoEnvio = 'ENVIO_NOTA_CREDITO';
                break;
            case '56': // Notas de Débito
                $tipoEnvio = 'ENVIO_NOTA_DEBITO';
                break;
            default:
                $tipoEnvio = 'ENVIO_DTE'; // Para otros tipos no específicos
        }
    } else if (count($tiposDTE) > 1) {
        // Si hay múltiples tipos, verificar si son todos boletas o todos otros documentos
        $todosBoletas = true;
        foreach ($tiposDTE as $tipo) {
            if ($tipo != '39') {
                $todosBoletas = false;
                break;
            }
        }

        if ($todosBoletas) {
            $tipoEnvio = 'ENVIO_BOLETA';
        } else {
            $tipoEnvio = 'ENVIO_DTE_MIXTO';
        }
    }

    Logger::log("Tipo de envío determinado", 'info', [
        'tipoEnvio' => $tipoEnvio,
        'tipos_dte_incluidos' => $tiposDTE
    ]);

    // Registrar el sobre en la base de datos (con estado_envio como integer)
    $stmt = $conn->prepare("
        INSERT INTO tb_sobre_envios (
            nombre_archivo,
            fecha,
            estado_envio,
            tipoEnvio,
            rut_emisor,
            rut_receptor,
            fecha_resolucion,
            numero_resolucion,
            certificado_rut
        ) VALUES (
            :nombre_archivo,
            NOW(),
            0,
            :tipoEnvio,
            :rut_emisor,
            :rut_receptor,
            :fecha_resolucion,
            :numero_resolucion,
            :certificado_rut
        )
    ");

    $stmt->bindParam(':nombre_archivo', $rutaCompleta);
    $stmt->bindParam(':tipoEnvio', $tipoEnvio); // Vinculamos la variable actualizada
    $stmt->bindParam(':rut_emisor', $rutEmisor);
    $stmt->bindParam(':rut_receptor', $rutReceptor);
    $stmt->bindParam(':fecha_resolucion', $fechaResolucion);
    $stmt->bindParam(':numero_resolucion', $numeroResolucion);
    $stmt->bindParam(':certificado_rut', $certificadoRut);
    $stmt->execute();

    $sobreId = $conn->lastInsertId();

    // Crear tabla de relacionamiento sobre-documentos (si no existe)
    $conn->exec("
        CREATE TABLE IF NOT EXISTS tb_sobre_documentos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sobre_id INT NOT NULL,
            documento_id INT NOT NULL,
            fecha DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sobre_id) REFERENCES tb_sobre_envios(id),
            FOREIGN KEY (documento_id) REFERENCES tb_facturas_dte(id),
            UNIQUE KEY (sobre_id, documento_id)
        )
    ");

    // Actualizar el estado de todos los documentos incluidos y establecer la relación con id_sobre
    $documentosActualizados = [];
    foreach ($xmlIncluidos as $xmlInfo) {
        $documentoId = $xmlInfo['id'];

        // Registrar relación sobre-documento
        $stmt = $conn->prepare("
            INSERT INTO tb_sobre_documentos (sobre_id, documento_id)
            VALUES (:sobre_id, :documento_id)
        ");
        $stmt->bindParam(':sobre_id', $sobreId);
        $stmt->bindParam(':documento_id', $documentoId);
        $stmt->execute();

        // Actualizar estado del documento y establecer id_sobre
        $stmt = $conn->prepare("UPDATE tb_facturas_dte SET estado_sobre = 1, id_sobre = :id_sobre WHERE id = :id");
        $stmt->bindParam(':id_sobre', $sobreId);
        $stmt->bindParam(':id', $documentoId);
        $stmt->execute();

        $documentosActualizados[] = $documentoId;
    }

    // Respuesta exitosa - solo guardamos el trackId para información, no en la base de datos
    $responseData = [
        'success' => true,
        'mensaje' => 'Sobre de envío generado y registrado correctamente',
        'sobre_id' => $sobreId,
        'documentos_incluidos' => count($xmlIncluidos),
        'documentos_ids' => $documentosActualizados,
        'archivo_sobre' => $nombreArchivo,
        'ruta_sobre' => $rutaCompleta,
        'tipoEnvio' => $tipoEnvio,
        'tipos_dte' => $tiposDTE,
        'trackid_info' => $trackId, // Solo para información en la respuesta
        'logs' => Logger::getLogs()
    ];

    Logger::log("Proceso completado exitosamente", 'success');

    // Actualizar el estado de la solicitud si se usó un token
    if ($requestToken) {
        try {
            $stmt = $conn->prepare("UPDATE tb_request_control SET status = 1, completed_at = NOW() WHERE request_token = :token");
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();

            Logger::log("Estado de solicitud actualizado", 'info', [
                'token' => $requestToken,
                'status' => 'completada'
            ]);
        } catch (Exception $updateEx) {
            // Solo registrar el error, no interrumpir el flujo principal
            Logger::log("Error al actualizar estado de solicitud", 'warning', [
                'token' => $requestToken,
                'error' => $updateEx->getMessage()
            ]);
        }
    }

    echo json_encode($responseData);

} catch (Exception $e) {
    // Registrar el error en múltiples lugares para asegurar que se capture
    $errorMessage = "Error en generar_sobre.php: " . $e->getMessage();

    // Preparar diagnóstico detallado del error
    $diagnosticoError = [
        'exception_info' => [
            'class' => get_class($e),
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => explode("\n", $e->getTraceAsString())
        ],
        'context_info' => [
            'request_token' => $requestToken ?? 'no disponible',
            'post_data' => $_POST,
            'documentos_procesados' => isset($documentos) ? count($documentos) : 0,
            'xml_validos' => isset($xmlPathsValidos) ? count($xmlPathsValidos) : 0,
            'timestamp' => date('Y-m-d H:i:s')
        ],
        'system_info' => [
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true) . ' bytes',
            'max_execution_time' => ini_get('max_execution_time'),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'desconocido',
            'ip_servidor' => $_SERVER['SERVER_ADDR'] ?? 'desconocido',
            'sistema_operativo' => PHP_OS
        ],
        'stack_info' => []
    ];

    // Obtener más información sobre el contexto de la excepción
    $trace = $e->getTrace();
    if (!empty($trace)) {
        foreach ($trace as $idx => $t) {
            $diagnosticoError['stack_info'][] = [
                'level' => $idx,
                'function' => $t['function'] ?? 'desconocida',
                'line' => $t['line'] ?? 'desconocida',
                'file' => $t['file'] ?? 'desconocido',
                'args' => isset($t['args']) ? count($t['args']) . ' argumentos' : 'ninguno'
            ];
        }
    }

    // Capturar variables importantes del contexto actual
    if (isset($documentos)) {
        $diagnosticoError['context_info']['doc_ids'] = array_column($documentos, 'id');
        $diagnosticoError['context_info']['doc_tipos'] = array_column($documentos, 'tipo_dte');
    }
    if (isset($xmlPaths)) {
        $diagnosticoError['context_info']['paths_verificados'] = count($xmlPaths);
    }

    // Búsqueda de patrones comunes de error para ayudar en el diagnóstico
    $errorPatterns = [
        'file_not_found' => '/file.+not found|No such file|No existe el|cannot find/i',
        'permission_denied' => '/permission denied|acceso denegado|not permitted/i',
        'memory_limit' => '/memory.+(allocated|exhausted|limit)/i',
        'timeout' => '/time.+out|execution.+exceeded/i',
        'database_error' => '/sql|mysql|database|query|db error/i',
        'api_error' => '/curl|api|https|request|response/i',
        'xml_error' => '/xml|parse|syntax|malformed/i'
    ];

    $diagnosticoError['error_categorias'] = [];
    foreach ($errorPatterns as $category => $pattern) {
        if (preg_match($pattern, strtolower($e->getMessage()))) {
            $diagnosticoError['error_categorias'][] = $category;
        }
    }

    // 1. Log en archivo de errores de PHP con diagnóstico detallado
    error_log($errorMessage);
    error_log("Diagnóstico detallado: " . json_encode($diagnosticoError, JSON_UNESCAPED_SLASHES));

    // 2. Log en nuestro sistema de logs con toda la información
    Logger::log("ERROR CRÍTICO EN PROCESO", 'error', $diagnosticoError);

    // 3. Log en nuestro manejador de errores personalizado
    ErrorLogHandler::logError($errorMessage, $diagnosticoError, 'critical');

    // 4. Guardar en archivo de texto plano para depuración con formato más legible
    $debugLog = "error_diagnostico_" . date('Ymd_His') . ".log";
    file_put_contents($debugLog,
        "==================== DIAGNÓSTICO DE ERROR ====================\n" .
        "FECHA Y HORA: " . date('Y-m-d H:i:s') . "\n" .
        "MENSAJE: " . $errorMessage . "\n\n" .
        "== INFORMACIÓN DE LA EXCEPCIÓN ==\n" .
        "Clase: " . get_class($e) . "\n" .
        "Código: " . $e->getCode() . "\n" .
        "Archivo: " . $e->getFile() . "\n" .
        "Línea: " . $e->getLine() . "\n" .
        "Mensaje: " . $e->getMessage() . "\n\n" .
        "== TRAZA DE LA EXCEPCIÓN ==\n" . $e->getTraceAsString() . "\n\n" .
        "== CATEGORÍAS DE ERROR ==\n" . implode(", ", $diagnosticoError['error_categorias']) . "\n\n" .
        "== DATOS DE LA SOLICITUD ==\n" . print_r($_POST, true) . "\n\n" .
        "== VARIABLES DEL SERVIDOR ==\n" . print_r($_SERVER, true) . "\n\n" .
        "== LOGS DE LA SESIÓN ==\n" . print_r(Logger::getLogs(), true) . "\n" .
        "==============================================================\n");

    // 5. Guardar en la base de datos con diagnóstico completo
    try {
        if (isset($conn) && $conn instanceof PDO) {
            $stmt = $conn->prepare("INSERT INTO tb_error_logs (tipo, mensaje, detalles, fecha)
                                   VALUES ('exception', :mensaje, :detalles, NOW())");
            $detallesJson = json_encode($diagnosticoError, JSON_UNESCAPED_SLASHES);
            $stmt->bindParam(':mensaje', $errorMessage);
            $stmt->bindParam(':detalles', $detallesJson);
            $stmt->execute();

            // Registrar el ID del error para referencia
            $errorId = $conn->lastInsertId();
            error_log("Error registrado en base de datos con ID: " . $errorId);

            // Si hay documentos implicados, registrar la relación
            if (isset($documentos) && !empty($documentos) && isset($errorId)) {
                $docIds = array_column($documentos, 'id');
                foreach ($docIds as $docId) {
                    try {
                        $stmt = $conn->prepare("INSERT INTO tb_error_documentos (error_id, documento_id, fecha)
                                          VALUES (:error_id, :documento_id, NOW())");
                        $stmt->bindParam(':error_id', $errorId);
                        $stmt->bindParam(':documento_id', $docId);
                        $stmt->execute();
                    } catch (Exception $relEx) {
                        // Ignorar errores al relacionar documentos
                    }
                }
            }
        } else {
            error_log("No se pudo guardar el error en la base de datos: conexión no disponible");
        }
    } catch (Exception $dbEx) {
        error_log("Error al guardar excepción en base de datos: " . $dbEx->getMessage());
    }

    // Actualizar el estado de la solicitud en caso de error
    if (isset($requestToken) && isset($conn) && $conn instanceof PDO) {
        try {
            $stmt = $conn->prepare("UPDATE tb_request_control SET status = 2, completed_at = NOW() WHERE request_token = :token");
            $stmt->bindParam(':token', $requestToken);
            $stmt->execute();

            Logger::log("Estado de solicitud actualizado a error", 'info', [
                'token' => $requestToken,
                'status' => 'error'
            ]);
        } catch (Exception $updateEx) {
            // Solo registrar el error
            error_log("Error al actualizar estado de solicitud: " . $updateEx->getMessage());
        }
    }

    // Devolver respuesta con todos los detalles posibles de forma estructurada
    echo json_encode([
        'error' => 'Error interno: ' . $e->getMessage(),
        'error_id' => $errorId ?? null,
        'debug_log' => $debugLog,
        'details' => [
            // Información condensada para la interfaz de usuario
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => basename($e->getFile()),  // Solo el nombre del archivo, no la ruta completa
            'line' => $e->getLine(),
            'categorias' => $diagnosticoError['error_categorias'],
            'request_token' => $requestToken ?? 'no disponible',
            'context' => [
                'documentos' => isset($documentos) ? count($documentos) : 0,
                'archivos_validos' => isset($xmlPathsValidos) ? count($xmlPathsValidos) : 0,
                'tipos_dte' => isset($tiposDTE) ? $tiposDTE : []
            ],
            // Solo incluir los primeros 5 elementos de la traza para no sobrecargar la respuesta
            'trace' => array_slice(explode("\n", $e->getTraceAsString()), 0, 5)
        ],
        // Incluir logs de la sesión para depuración
        'logs' => Logger::getLogs()
    ]);
}
?>
