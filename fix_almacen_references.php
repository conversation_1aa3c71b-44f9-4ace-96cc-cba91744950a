<?php
// fix_almacen_references.php
// Script para corregir referencias a la tabla almacenes en el código

require_once 'db_connection.php';

header('Content-Type: text/html; charset=UTF-8');

try {
    $conn = getConnection();
    
    // Verificar la conexión
    echo "Conexión a la base de datos establecida correctamente.<br>";
    
    // Verificar si la tabla almacen existe
    $stmt = $conn->query("SHOW TABLES LIKE 'almacen'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "La tabla 'almacen' NO existe. Creándola...<br>";
        
        // Crear la tabla
        $createTableSql = "
            CREATE TABLE almacen (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nombre VARCHAR(100) NOT NULL,
                descripcion TEXT,
                activo TINYINT(1) DEFAULT 1,
                fecha_creacion DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $conn->exec($createTableSql);
        
        echo "Tabla 'almacen' creada exitosamente.<br>";
        
        // Insertar un almacén por defecto
        $insertSql = "INSERT INTO almacen (nombre, descripcion, activo) VALUES ('TEMUCO', 'Almacén principal', 1)";
        $conn->exec($insertSql);
        
        echo "Almacén por defecto 'TEMUCO' insertado exitosamente.<br>";
    } else {
        echo "La tabla 'almacen' ya existe.<br>";
    }
    
    // Verificar si hay archivos que hacen referencia a la tabla almacenes
    $files = [
        'create_entrada.php',
        'get_almacenes.php',
        'js/index.js'
    ];
    
    echo "<h2>Verificando archivos que hacen referencia a la tabla 'almacenes'...</h2>";
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            // Buscar referencias a la tabla almacenes
            $pattern = '/almacenes/i';
            $matches = [];
            preg_match_all($pattern, $content, $matches);
            
            echo "Archivo: $file - ";
            
            if (!empty($matches[0])) {
                echo "Encontradas " . count($matches[0]) . " referencias a 'almacenes'.<br>";
                
                // Mostrar las líneas que contienen referencias
                $lines = explode("\n", $content);
                $lineNumber = 1;
                
                echo "<pre>";
                foreach ($lines as $line) {
                    if (preg_match($pattern, $line)) {
                        echo "Línea $lineNumber: " . htmlspecialchars($line) . "\n";
                    }
                    $lineNumber++;
                }
                echo "</pre>";
                
                // Corregir las referencias
                $correctedContent = preg_replace('/almacenes/i', 'almacen', $content);
                
                // Guardar el archivo corregido
                file_put_contents($file, $correctedContent);
                
                echo "Archivo $file corregido exitosamente.<br>";
            } else {
                echo "No se encontraron referencias a 'almacenes'.<br>";
            }
        } else {
            echo "Archivo: $file - No existe.<br>";
        }
    }
    
    echo "<h2>Proceso completado</h2>";
    echo "<p>Se han corregido las referencias a la tabla 'almacenes' en los archivos relevantes.</p>";
    echo "<p>Por favor, recarga la página para verificar que el problema se ha solucionado.</p>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
