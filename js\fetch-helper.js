/**
 * Helper para realizar peticiones fetch con credenciales incluidas
 * Usar este helper en lugar de fetch directo para asegurar que las cookies de sesión
 * se envíen correctamente en todas las peticiones AJAX
 */

/**
 * Realiza una petición fetch con credentials:include por defecto
 * @param {string} url - URL de la petición
 * @param {Object} options - Opciones adicionales para fetch (opcional)
 * @returns {Promise} - Promesa de fetch
 */
async function fetchWithCredentials(url, options = {}) {
    // Obtener el token CSRF del meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    // Asegurar que credentials:include está presente
    const fetchOptions = {
        ...options,
        credentials: 'include'
    };
    
    // Agregar el token CSRF al header si existe y no es GET
    if (csrfToken && (!options.method || options.method !== 'GET')) {
        fetchOptions.headers = {
            ...fetchOptions.headers,
            'X-CSRF-Token': csrfToken
        };
    }
    
    try {
        const response = await fetch(url, fetchOptions);
        
        // Verificar si la respuesta indica sesión expirada (401)
        if (response.status === 401) {
            console.error('Error de autenticación: Sesión expirada o no válida');
            // Redirigir al login si es necesario
            //window.location.href = 'login.php';
            throw new Error('Sesión expirada. Por favor, recargue la página e inicie sesión nuevamente.');
        }
        
        return response;
    } catch (error) {
        console.error('Error en la petición fetch:', error);
        throw error;
    }
}

/**
 * Realiza una petición GET con credentials:include
 * @param {string} url - URL de la petición
 * @param {Object} options - Opciones adicionales para fetch (opcional)
 * @returns {Promise} - Promesa de fetch
 */
async function getWithCredentials(url, options = {}) {
    return fetchWithCredentials(url, {
        method: 'GET',
        ...options
    });
}

/**
 * Realiza una petición POST con credentials:include y contenido JSON
 * @param {string} url - URL de la petición
 * @param {Object} data - Datos a enviar como JSON
 * @param {Object} options - Opciones adicionales para fetch (opcional)
 * @returns {Promise} - Promesa de fetch
 */
async function postJsonWithCredentials(url, data, options = {}) {
    return fetchWithCredentials(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        body: JSON.stringify(data),
        ...options
    });
}

/**
 * Realiza una petición POST con credentials:include y contenido FormData
 * @param {string} url - URL de la petición
 * @param {FormData} formData - Datos de formulario a enviar
 * @param {Object} options - Opciones adicionales para fetch (opcional)
 * @returns {Promise} - Promesa de fetch
 */
async function postFormWithCredentials(url, formData, options = {}) {
    return fetchWithCredentials(url, {
        method: 'POST',
        body: formData,
        ...options
    });
}