<?php
// Script para crear y actualizar tablas de manejo de errores
header('Content-Type: text/html; charset=utf-8');

// Configuración de seguridad básica
$allowed_ips = ['127.0.0.1', '::1']; // localhost
if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips)) {
    die('Acceso no autorizado');
}

// Incluir la conexión a la base de datos
require_once 'db_connection.php';

try {
    // Obtener la conexión
    $conn = getConnection();
    
    // Leer el archivo SQL
    $sql_file = file_get_contents('create_error_audit_table.sql');
    
    // Dividir las consultas SQL por el delimitador ";"
    $queries = explode(';', $sql_file);
    
    // Inicializar contadores
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    
    echo '<html><head><title>Instalación de tablas de diagnóstico</title>';
    echo '<style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: green; }
        .error { color: red; }
        .query { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #333; }
        h1 { color: #333; }
        .summary { margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px; }
        </style>';
    echo '</head><body>';
    echo '<h1>Instalación de tablas de diagnóstico de errores</h1>';
    
    // Ejecutar cada consulta
    foreach ($queries as $key => $query) {
        $query = trim($query);
        
        // Omitir consultas vacías
        if (empty($query)) {
            continue;
        }
        
        try {
            // Ejecutar la consulta
            $result = $conn->exec($query);
            $success_count++;
            
            echo '<div class="success">✓ Consulta ejecutada con éxito:</div>';
            echo '<div class="query"><pre>' . htmlspecialchars($query) . '</pre></div>';
            
        } catch (PDOException $e) {
            $error_count++;
            $errors[] = $e->getMessage();
            
            echo '<div class="error">✗ Error al ejecutar la consulta:</div>';
            echo '<div class="query"><pre>' . htmlspecialchars($query) . '</pre></div>';
            echo '<div class="error">Mensaje: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    // Mostrar resumen
    echo '<div class="summary">';
    echo '<h2>Resumen de la instalación</h2>';
    echo '<p><strong>Consultas ejecutadas con éxito:</strong> ' . $success_count . '</p>';
    echo '<p><strong>Errores encontrados:</strong> ' . $error_count . '</p>';
    
    if ($error_count > 0) {
        echo '<h3>Detalles de los errores:</h3>';
        echo '<ul>';
        foreach ($errors as $error) {
            echo '<li class="error">' . htmlspecialchars($error) . '</li>';
        }
        echo '</ul>';
    }
    
    echo '</div>';
    
    echo '<p><a href="sobres_envio.php">Volver a la página principal</a></p>';
    echo '</body></html>';
    
} catch (Exception $e) {
    die('Error general: ' . $e->getMessage());
}
