<?php
/**
 * Archivo: sobre_debug.php
 * Descripción: Herramienta para depurar problemas con sobres de envío
 */

require_once 'debug_helper.php';
require_once 'db_connection.php';

// Inicializar el sistema de depuración
DebugHelper::init('logs', true);

// Cabecera para JSON
header('Content-Type: application/json');

// Verificar permisos de directorios críticos
$directorios = [
    'Documents/',
    'Documents/sobreEnvio/',
    'logs/'
];

$permisosDirectorios = DebugHelper::checkPermissions($directorios);

// Verificar conexión a la base de datos
try {
    $conn = getConnection();
    $dbStatus = DebugHelper::checkDatabase($conn);
} catch (Exception $e) {
    DebugHelper::log("Error al conectar con la base de datos", DebugHelper::LEVEL_ERROR, $e->getMessage());
    $dbStatus = ['connected' => false, 'error' => $e->getMessage()];
}

// Verificar archivos críticos
$archivos = [
    'generar_sobre.php',
    'enviar_sobre.php',
    'procesar_sobres_pendientes.php',
    'Documents/17365958-K.pfx'
];

$permisosArchivos = DebugHelper::checkPermissions($archivos);

// Verificar documentos pendientes
$pendientesPorTipo = [];
$totalPendientes = 0;

if ($dbStatus['connected']) {
    try {
        $stmt = $conn->query("
            SELECT tipo_dte, COUNT(*) as cantidad 
            FROM tb_facturas_dte 
            WHERE estado_sobre = 0 
            GROUP BY tipo_dte 
            ORDER BY cantidad DESC
        ");
        
        $pendientesPorTipo = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calcular total
        foreach ($pendientesPorTipo as $pendiente) {
            $totalPendientes += $pendiente['cantidad'];
        }
        
        DebugHelper::log("Documentos pendientes encontrados", DebugHelper::LEVEL_INFO, [
            'total' => $totalPendientes,
            'por_tipo' => $pendientesPorTipo
        ]);
        
        // Verificar si hay sobres generados pero no enviados
        $stmt = $conn->query("
            SELECT COUNT(*) as cantidad 
            FROM tb_sobre_envios 
            WHERE estado_envio = 0
        ");
        
        $sobresPendientes = $stmt->fetch(PDO::FETCH_ASSOC)['cantidad'];
        
        DebugHelper::log("Sobres pendientes de envío", DebugHelper::LEVEL_INFO, [
            'cantidad' => $sobresPendientes
        ]);
        
        // Obtener información detallada del último sobre generado
        if ($sobresPendientes > 0) {
            $stmt = $conn->query("
                SELECT * 
                FROM tb_sobre_envios 
                WHERE estado_envio = 0 
                ORDER BY id DESC 
                LIMIT 1
            ");
            
            $ultimoSobre = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Verificar si el archivo del sobre existe
            $archivoSobreExiste = file_exists($ultimoSobre['nombre_archivo']);
            $tamanoArchivo = $archivoSobreExiste ? filesize($ultimoSobre['nombre_archivo']) : 0;
            
            DebugHelper::log("Último sobre pendiente", DebugHelper::LEVEL_INFO, [
                'id' => $ultimoSobre['id'],
                'nombre_archivo' => $ultimoSobre['nombre_archivo'],
                'fecha' => $ultimoSobre['fecha'],
                'tipo_envio' => $ultimoSobre['tipoEnvio'],
                'archivo_existe' => $archivoSobreExiste,
                'tamano_archivo' => $tamanoArchivo
            ]);
            
            // Verificar documentos incluidos en el sobre
            $stmt = $conn->prepare("
                SELECT d.id, d.tipo_dte, d.folio, d.nombre_archivo
                FROM tb_facturas_dte d
                JOIN tb_sobre_documentos sd ON d.id = sd.documento_id
                WHERE sd.sobre_id = :sobre_id
            ");
            
            $stmt->bindParam(':sobre_id', $ultimoSobre['id']);
            $stmt->execute();
            
            $documentosEnSobre = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            DebugHelper::log("Documentos incluidos en el sobre", DebugHelper::LEVEL_INFO, [
                'cantidad' => count($documentosEnSobre),
                'documentos' => $documentosEnSobre
            ]);
        }
        
        // Verificar errores recientes
        $stmt = $conn->query("
            SELECT * 
            FROM tb_sobre_envios 
            WHERE estado_envio = 2 
            ORDER BY id DESC 
            LIMIT 5
        ");
        
        $sobresConError = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($sobresConError) > 0) {
            DebugHelper::log("Sobres con errores recientes", DebugHelper::LEVEL_WARNING, [
                'cantidad' => count($sobresConError),
                'sobres' => $sobresConError
            ]);
        }
        
    } catch (Exception $e) {
        DebugHelper::log("Error al consultar documentos pendientes", DebugHelper::LEVEL_ERROR, $e->getMessage());
    }
}

// Verificar configuración de PHP
$phpConfig = [
    'version' => PHP_VERSION,
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'post_max_size' => ini_get('post_max_size'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'display_errors' => ini_get('display_errors'),
    'error_reporting' => ini_get('error_reporting'),
    'curl_enabled' => function_exists('curl_init'),
    'xml_enabled' => extension_loaded('xml'),
    'json_enabled' => function_exists('json_encode'),
    'pdo_enabled' => extension_loaded('pdo_mysql')
];

DebugHelper::log("Configuración de PHP", DebugHelper::LEVEL_INFO, $phpConfig);

// Verificar espacio en disco
$diskFree = disk_free_space('/');
$diskTotal = disk_total_space('/');
$diskUsed = $diskTotal - $diskFree;
$diskPercent = round(($diskUsed / $diskTotal) * 100, 2);

DebugHelper::log("Espacio en disco", DebugHelper::LEVEL_INFO, [
    'total' => formatBytes($diskTotal),
    'usado' => formatBytes($diskUsed),
    'libre' => formatBytes($diskFree),
    'porcentaje_usado' => $diskPercent . '%'
]);

// Función para formatear bytes
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Preparar respuesta
$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'status' => [
        'directorios' => $permisosDirectorios,
        'archivos' => $permisosArchivos,
        'base_datos' => $dbStatus,
        'php' => $phpConfig,
        'disco' => [
            'total' => formatBytes($diskTotal),
            'usado' => formatBytes($diskUsed),
            'libre' => formatBytes($diskFree),
            'porcentaje_usado' => $diskPercent . '%'
        ]
    ],
    'documentos' => [
        'total_pendientes' => $totalPendientes,
        'pendientes_por_tipo' => $pendientesPorTipo,
        'sobres_pendientes' => $sobresPendientes ?? 0
    ],
    'logs' => DebugHelper::getLogs()
];

// Devolver respuesta
echo json_encode($response, JSON_PRETTY_PRINT);
