
<?php
require_once 'db_connection.php';

try {
    $rut = $_GET['rut'] ?? '';
    $conn = getConnection();
    
    $stmt = $conn->prepare("SELECT cliente_id, nombre, direccion, rut, telefono FROM cliente WHERE rut = ?");
    $stmt->execute([$rut]);
    $client = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($client) {
        echo json_encode(['status' => 'success', 'exists' => true, 'data' => $client]);
    } else {
        echo json_encode(['status' => 'success', 'exists' => false]);
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>
