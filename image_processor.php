<?php
/**
 * Clase para procesar imágenes y convertirlas a formato WebP
 */
class ImageProcessor {
    /**
     * Convierte una imagen a formato WebP
     *
     * @param string $sourcePath Ruta de la imagen original
     * @param string $destPath Ruta donde guardar la imagen WebP
     * @param int $quality Calidad de la imagen WebP (0-100)
     * @return bool True si la conversión fue exitosa, False en caso contrario
     */
    public static function convertToWebP($sourcePath, $destPath, $quality = 80) {
        // Verificar que la extensión GD esté habilitada
        if (!extension_loaded('gd')) {
            error_log('La extensión GD no está habilitada');
            return false;
        }

        // Obtener información de la imagen
        $imageInfo = getimagesize($sourcePath);
        if ($imageInfo === false) {
            error_log('No se pudo obtener información de la imagen: ' . $sourcePath);
            return false;
        }

        // Crear imagen desde el archivo original según su tipo
        $sourceImage = null;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                // Preservar transparencia
                imagepalettetotruecolor($sourceImage);
                imagealphablending($sourceImage, true);
                imagesavealpha($sourceImage, true);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case IMAGETYPE_BMP:
                $sourceImage = imagecreatefrombmp($sourcePath);
                break;
            case IMAGETYPE_WEBP:
                // Ya es WebP, solo copiar el archivo
                return copy($sourcePath, $destPath);
            default:
                error_log('Formato de imagen no soportado: ' . $imageInfo[2]);
                return false;
        }

        if ($sourceImage === false) {
            error_log('No se pudo crear la imagen desde el archivo: ' . $sourcePath);
            return false;
        }

        // Convertir a WebP
        $result = imagewebp($sourceImage, $destPath, $quality);

        // Liberar memoria
        imagedestroy($sourceImage);

        return $result;
    }

    /**
     * Redimensiona una imagen manteniendo su proporción
     *
     * @param string $sourcePath Ruta de la imagen original
     * @param string $destPath Ruta donde guardar la imagen redimensionada
     * @param int $maxWidth Ancho máximo
     * @param int $maxHeight Alto máximo
     * @param int $quality Calidad de la imagen (0-100)
     * @return bool True si el redimensionamiento fue exitoso, False en caso contrario
     */
    public static function resizeImage($sourcePath, $destPath, $maxWidth = 800, $maxHeight = 800, $quality = 80) {
        // Verificar que la extensión GD esté habilitada
        if (!extension_loaded('gd')) {
            error_log('La extensión GD no está habilitada');
            return false;
        }

        // Obtener información de la imagen
        $imageInfo = getimagesize($sourcePath);
        if ($imageInfo === false) {
            error_log('No se pudo obtener información de la imagen: ' . $sourcePath);
            return false;
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];

        // Calcular nuevas dimensiones manteniendo la proporción
        if ($width > $maxWidth || $height > $maxHeight) {
            $ratio = min($maxWidth / $width, $maxHeight / $height);
            $newWidth = round($width * $ratio);
            $newHeight = round($height * $ratio);
        } else {
            // La imagen ya es más pequeña que las dimensiones máximas
            $newWidth = $width;
            $newHeight = $height;
        }

        // Crear imagen desde el archivo original según su tipo
        $sourceImage = null;
        switch ($imageInfo[2]) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                // Preservar transparencia
                imagepalettetotruecolor($sourceImage);
                imagealphablending($sourceImage, true);
                imagesavealpha($sourceImage, true);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case IMAGETYPE_BMP:
                $sourceImage = imagecreatefrombmp($sourcePath);
                break;
            case IMAGETYPE_WEBP:
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                error_log('Formato de imagen no soportado: ' . $imageInfo[2]);
                return false;
        }

        if ($sourceImage === false) {
            error_log('No se pudo crear la imagen desde el archivo: ' . $sourcePath);
            return false;
        }

        // Crear imagen redimensionada
        $destImage = imagecreatetruecolor($newWidth, $newHeight);

        // Preservar transparencia para PNG
        if ($imageInfo[2] === IMAGETYPE_PNG) {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $newWidth, $newHeight, $transparent);
        }

        // Redimensionar
        imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

        // Guardar imagen redimensionada
        $result = false;
        $extension = pathinfo($destPath, PATHINFO_EXTENSION);

        switch (strtolower($extension)) {
            case 'jpg':
            case 'jpeg':
                $result = imagejpeg($destImage, $destPath, $quality);
                break;
            case 'png':
                // Para PNG, la calidad es de 0-9, convertir de 0-100 a 0-9
                $pngQuality = round(9 - (($quality / 100) * 9));
                $result = imagepng($destImage, $destPath, $pngQuality);
                break;
            case 'gif':
                $result = imagegif($destImage, $destPath);
                break;
            case 'bmp':
                $result = imagebmp($destImage, $destPath);
                break;
            case 'webp':
                $result = imagewebp($destImage, $destPath, $quality);
                break;
            default:
                error_log('Formato de destino no soportado: ' . $extension);
                break;
        }

        // Liberar memoria
        imagedestroy($sourceImage);
        imagedestroy($destImage);

        return $result;
    }

    /**
     * Procesa una imagen subida: redimensiona y convierte a WebP
     *
     * @param array $fileData Datos del archivo subido ($_FILES['imagen'])
     * @param string $uploadDir Directorio donde guardar la imagen
     * @param string $filePrefix Prefijo para el nombre del archivo (ej: 'producto_')
     * @param int $maxWidth Ancho máximo
     * @param int $maxHeight Alto máximo
     * @param int $quality Calidad de la imagen WebP (0-100)
     * @return array Información sobre el procesamiento ['success' => bool, 'message' => string, 'path' => string]
     */
    public static function processUploadedImage($fileData, $uploadDir, $filePrefix = '', $maxWidth = 800, $maxHeight = 800, $quality = 80) {
        $result = [
            'success' => false,
            'message' => '',
            'path' => ''
        ];

        // Verificar si hay error en la subida
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = 'Error en la subida del archivo: ' . $fileData['error'];
            return $result;
        }

        // Registrar información para depuración
        error_log('Procesando imagen: ' . $fileData['name'] . ', tamaño: ' . $fileData['size'] . ' bytes');
        error_log('Directorio de destino: ' . $uploadDir);

        // Verificar que la extensión GD esté habilitada
        if (!extension_loaded('gd')) {
            $result['message'] = 'La extensión GD no está habilitada en el servidor';
            error_log('Error: La extensión GD no está habilitada');
            return $result;
        }

        // Verificar soporte para WebP
        $gdInfo = gd_info();
        $webpSupported = isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'];
        error_log('Soporte para WebP: ' . ($webpSupported ? 'Sí' : 'No'));

        // Crear directorio si no existe
        if (!file_exists($uploadDir)) {
            error_log('El directorio no existe, intentando crearlo: ' . $uploadDir);
            if (!mkdir($uploadDir, 0777, true)) {
                $result['message'] = 'No se pudo crear el directorio de uploads: ' . $uploadDir;
                error_log('Error al crear directorio: ' . $uploadDir);
                return $result;
            }
            chmod($uploadDir, 0777);
            error_log('Directorio creado con éxito: ' . $uploadDir);
        }

        // Verificar permisos del directorio
        if (!is_writable($uploadDir)) {
            $result['message'] = 'El directorio no tiene permisos de escritura: ' . $uploadDir;
            error_log('Error: El directorio no tiene permisos de escritura: ' . $uploadDir);
            return $result;
        }

        // Generar nombre para el archivo
        $originalName = pathinfo($fileData['name'], PATHINFO_FILENAME);
        $safeFilename = self::sanitizeFilename($originalName);

        // Si el prefijo ya contiene un identificador único (como un ID de producto),
        // no necesitamos agregar más identificadores aleatorios
        if (strpos($filePrefix, '_') !== false) {
            // Usar solo el prefijo proporcionado y el nombre sanitizado
            $tempFilename = $filePrefix . $safeFilename;
        } else {
            // Comportamiento original para prefijos genéricos
            $uniqueId = uniqid();
            $timestamp = time();
            $tempFilename = $filePrefix . $safeFilename . '_' . $uniqueId . '_' . $timestamp;
        }

        $tempPath = $uploadDir . $tempFilename . '.tmp';

        error_log('Moviendo archivo temporal a: ' . $tempPath);

        // Mover el archivo subido a una ubicación temporal
        $tempMoved = false;

        // Intentar move_uploaded_file primero
        if (move_uploaded_file($fileData['tmp_name'], $tempPath)) {
            $tempMoved = true;
            error_log('Archivo temporal movido con éxito usando move_uploaded_file');
        }
        // Si falla, intentar copy
        else if (copy($fileData['tmp_name'], $tempPath)) {
            $tempMoved = true;
            error_log('Archivo temporal copiado con éxito usando copy');
        }
        // Si ambos fallan, intentar file_put_contents
        else {
            $fileContent = file_get_contents($fileData['tmp_name']);
            if ($fileContent !== false && file_put_contents($tempPath, $fileContent)) {
                $tempMoved = true;
                error_log('Archivo temporal guardado con éxito usando file_put_contents');
            }
        }

        if (!$tempMoved) {
            $errorMessage = 'Error al mover el archivo subido. ';
            $errorMessage .= 'Detalles: ';
            $errorMessage .= 'Permisos del directorio: ' . decoct(fileperms($uploadDir)) . ', ';
            $errorMessage .= 'Archivo temporal existe: ' . (file_exists($fileData['tmp_name']) ? 'Sí' : 'No') . ', ';
            $errorMessage .= 'Es escribible: ' . (is_writable($uploadDir) ? 'Sí' : 'No');

            $result['message'] = $errorMessage;
            error_log($errorMessage);
            return $result;
        }

        // Verificar que el archivo temporal se creó correctamente
        if (!file_exists($tempPath)) {
            $result['message'] = 'El archivo temporal no se creó correctamente';
            error_log('Error: El archivo temporal no existe: ' . $tempPath);
            return $result;
        }

        // Obtener extensión del archivo original
        $originalExtension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));

        // Si la extensión no es válida, usar jpg como predeterminado
        if (!in_array($originalExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            $originalExtension = 'jpg';
        }

        // Nombre para el archivo redimensionado (mismo formato que el original)
        if (strpos($filePrefix, '_') !== false) {
            // Usar el mismo prefijo que para el archivo temporal
            $resizedFilename = $tempFilename . '_resized.' . $originalExtension;
        } else {
            // Comportamiento original
            $resizedFilename = $filePrefix . $safeFilename . '_' . $uniqueId . '_' . $timestamp . '_resized.' . $originalExtension;
        }
        $resizedPath = $uploadDir . $resizedFilename;

        // Redimensionar la imagen
        error_log('Redimensionando imagen a ' . $maxWidth . 'x' . $maxHeight);
        if (!self::resizeImage($tempPath, $resizedPath, $maxWidth, $maxHeight, $quality)) {
            // Si falla el redimensionamiento, intentar copiar el archivo original
            error_log('Error al redimensionar la imagen, intentando copiar el archivo original');
            if (!copy($tempPath, $resizedPath)) {
                unlink($tempPath); // Eliminar archivo temporal
                $result['message'] = 'Error al redimensionar la imagen y no se pudo copiar el archivo original';
                error_log($result['message']);
                return $result;
            }
        }

        // Verificar que el archivo redimensionado existe
        if (!file_exists($resizedPath)) {
            unlink($tempPath); // Eliminar archivo temporal
            $result['message'] = 'El archivo redimensionado no se creó correctamente';
            error_log('Error: El archivo redimensionado no existe: ' . $resizedPath);
            return $result;
        }

        // Nombre para el archivo WebP final
        if (strpos($filePrefix, '_') !== false) {
            // Usar el mismo prefijo que para el archivo temporal
            $webpFilename = $tempFilename . '.webp';
        } else {
            // Comportamiento original
            $webpFilename = $filePrefix . $safeFilename . '_' . $uniqueId . '_' . $timestamp . '.webp';
        }
        $webpPath = $uploadDir . $webpFilename;

        // Intentar convertir a WebP si está soportado
        $webpSuccess = false;
        if ($webpSupported) {
            error_log('Convirtiendo imagen a WebP');
            $webpSuccess = self::convertToWebP($resizedPath, $webpPath, $quality);

            if (!$webpSuccess) {
                error_log('Error al convertir la imagen a WebP, se usará el formato original');
            } else {
                error_log('Imagen convertida a WebP con éxito');
            }
        } else {
            error_log('WebP no está soportado, se usará el formato original');
        }

        // Eliminar archivos temporales
        if (file_exists($tempPath)) {
            unlink($tempPath);
        }

        // Determinar qué archivo devolver (WebP o redimensionado)
        if ($webpSuccess && file_exists($webpPath)) {
            // Si la conversión a WebP fue exitosa, eliminar el archivo redimensionado
            if (file_exists($resizedPath)) {
                unlink($resizedPath);
            }

            $result['success'] = true;
            $result['message'] = 'Imagen procesada y convertida a WebP correctamente';
            $result['path'] = $webpPath;
            error_log('Imagen procesada y convertida a WebP correctamente: ' . $webpPath);
        } else {
            // Si la conversión a WebP falló, devolver el archivo redimensionado
            $result['success'] = true;
            $result['message'] = 'Imagen procesada correctamente (sin conversión a WebP)';
            $result['path'] = $resizedPath;
            error_log('Imagen procesada correctamente (sin conversión a WebP): ' . $resizedPath);
        }

        return $result;
    }

    /**
     * Sanitiza un nombre de archivo para que sea seguro
     *
     * @param string $filename Nombre del archivo a sanitizar
     * @return string Nombre de archivo sanitizado
     */
    private static function sanitizeFilename($filename) {
        // Eliminar caracteres especiales y espacios
        $filename = preg_replace('/[^\w\-\.]/', '_', $filename);
        // Eliminar múltiples guiones bajos consecutivos
        $filename = preg_replace('/_+/', '_', $filename);
        // Limitar longitud
        $filename = substr($filename, 0, 50);
        return $filename;
    }
}
