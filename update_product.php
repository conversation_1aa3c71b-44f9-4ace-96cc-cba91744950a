<?php
require_once 'db_connection.php';
require_once 'image_processor.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

try {
    $conn = getConnection();
    // Registrar los valores recibidos para depuración
    error_log("Datos recibidos en update_product.php: " . json_encode($_POST));

    $url_imagen = null;
    $updateImageSQL = '';
    $params = [
        $_POST['nombre'],
        $_POST['descripcion'],
        $_POST['categoria_id'],
        $_POST['subcategoria_id'] ?? null,
        $_POST['precio_compra'],
        $_POST['precio_venta'],
        $_POST['stock_minimo'],
        $_POST['stock_maximo'],
        $_POST['unidad_medida'],
        $_POST['ubicacion_almacen'],
        $_POST['es_original'],
        $_POST['fabricante'],
        $_POST['pais_origen'],
        $_POST['codigo_fabricante']
    ];

    // Verificar si el producto ya tiene una imagen
    $stmt = $conn->prepare("SELECT url_imagen FROM repuesto WHERE id = ?");
    $stmt->execute([$_POST['id']]);
    $currentImage = $stmt->fetchColumn();

    // Procesar la imagen si fue enviada
    if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
        try {
            // Directorio para guardar las imágenes
            $uploadDir = 'images/fotos_repuestos/';
            $absoluteUploadDir = __DIR__ . '/' . $uploadDir;

            // Asegurarse de que el directorio termine con una barra
            if (substr($absoluteUploadDir, -1) !== '/' && substr($absoluteUploadDir, -1) !== '\\') {
                $absoluteUploadDir .= '/';
            }

            // Si existe una imagen anterior, eliminarla
            if (!empty($currentImage)) {
                $fullImagePath = __DIR__ . '/' . $currentImage;
                error_log('Verificando imagen anterior: ' . $fullImagePath);
                if (file_exists($fullImagePath)) {
                    error_log('Eliminando imagen anterior: ' . $fullImagePath);
                    @unlink($fullImagePath);
                }
            }

            // Registrar información para depuración
            error_log('Procesando imagen en update_product.php');
            error_log('Nombre del archivo: ' . $_FILES['imagen']['name']);
            error_log('Tamaño del archivo: ' . $_FILES['imagen']['size'] . ' bytes');
            error_log('Directorio de destino: ' . $absoluteUploadDir);

            // Verificar que el directorio existe y tiene permisos
            if (!file_exists($absoluteUploadDir)) {
                error_log('El directorio no existe, intentando crearlo: ' . $absoluteUploadDir);
                if (!mkdir($absoluteUploadDir, 0777, true)) {
                    throw new Exception('No se pudo crear el directorio: ' . $absoluteUploadDir);
                }
                chmod($absoluteUploadDir, 0777);
                error_log('Directorio creado con éxito: ' . $absoluteUploadDir);
            }

            if (!is_writable($absoluteUploadDir)) {
                error_log('El directorio no tiene permisos de escritura, intentando cambiar permisos: ' . $absoluteUploadDir);
                if (!chmod($absoluteUploadDir, 0777)) {
                    throw new Exception('No se pudieron cambiar los permisos del directorio: ' . $absoluteUploadDir);
                }
                error_log('Permisos cambiados con éxito: ' . $absoluteUploadDir);
            }

            // Verificar que el archivo temporal existe
            if (!file_exists($_FILES['imagen']['tmp_name'])) {
                throw new Exception('El archivo temporal no existe: ' . $_FILES['imagen']['tmp_name']);
            }

            // Generar un nombre de archivo basado en el ID del producto para evitar duplicaciones
            $productId = $_POST['id'];
            $filePrefix = 'producto_' . $productId . '_';

            // Procesar la imagen con el prefijo específico del producto
            $imageResult = ImageProcessor::processUploadedImage(
                $_FILES['imagen'],
                $absoluteUploadDir,
                $filePrefix,  // Prefijo para el nombre del archivo basado en el ID del producto
                800,          // Ancho máximo
                800,          // Alto máximo
                85            // Calidad (0-100)
            );

            if (!$imageResult['success']) {
                error_log('Error al procesar la imagen: ' . $imageResult['message']);
                throw new Exception('Error al procesar la imagen: ' . $imageResult['message']);
            }

            // Obtener la ruta relativa para guardar en la base de datos
            $url_imagen = str_replace(__DIR__ . '/', '', $imageResult['path']);

            // Registrar información sobre el procesamiento de la imagen
            error_log('Imagen procesada correctamente: ' . $url_imagen);

            // Agregar la imagen a la consulta SQL
            $updateImageSQL = ', url_imagen = ?';
            $params[] = $url_imagen;
        } catch (Exception $e) {
            error_log('Excepción al procesar la imagen: ' . $e->getMessage());
            throw new Exception('Error al procesar la imagen: ' . $e->getMessage());
        }
    }

    // Agregar el ID del producto al final de los parámetros
    $params[] = $_POST['id'];

    $stmt = $conn->prepare("UPDATE repuesto SET
                           nombre = ?,
                           descripcion = ?,
                           categoria_id = ?,
                           id_subcategoria = ?,
                           precio_compra = ?,
                           precio_venta = ?,
                           stock_minimo = ?,
                           stock_maximo = ?,
                           unidad_medida = ?,
                           ubicacion_almacen = ?,
                           es_original = ?,
                           fabricante = ?,
                           pais_origen = ?,
                           codigo_fabricante = ?
                           $updateImageSQL
                           WHERE id = ?");

    $stmt->execute($params);

    echo json_encode([
        'status' => 'success',
        'message' => 'Producto actualizado correctamente',
        'url_imagen' => $url_imagen
    ]);
} catch(Exception $e) {
    error_log("Error en update_product.php: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
