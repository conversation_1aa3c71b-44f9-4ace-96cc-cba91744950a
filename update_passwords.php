<?php
require_once 'db_connection.php';

// Función para mostrar mensajes formateados
function showMessage($message, $type = 'info') {
    $color = 'black';
    switch ($type) {
        case 'success':
            $color = 'green';
            break;
        case 'error':
            $color = 'red';
            break;
        case 'warning':
            $color = 'orange';
            break;
    }
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid {$color}; background-color: #f9f9f9;'>{$message}</div>";
}

try {
    $conn = getConnection();
    showMessage("Conexión a la base de datos establecida correctamente.", "success");
    
    // Actualizar la contraseña del usuario nico.cornejo
    $updateSql = "UPDATE tb_usuarios SET password = ? WHERE username = ?";
    $updateStmt = $conn->prepare($updateSql);
    $hashedPassword = password_hash("N1c0l7as17", PASSWORD_DEFAULT);
    $result = $updateStmt->execute([$hashedPassword, "nico.cornejo"]);
    
    if ($result) {
        showMessage("La contraseña del usuario 'nico.cornejo' ha sido actualizada con hash.", "success");
    } else {
        showMessage("Error al actualizar la contraseña del usuario 'nico.cornejo'.", "error");
    }
    
    // Actualizar la contraseña del usuario admin
    $updateSql = "UPDATE tb_usuarios SET password = ? WHERE username = ?";
    $updateStmt = $conn->prepare($updateSql);
    $hashedPassword = password_hash("admin123", PASSWORD_DEFAULT);
    $result = $updateStmt->execute([$hashedPassword, "admin"]);
    
    if ($result) {
        showMessage("La contraseña del usuario 'admin' ha sido actualizada con hash.", "success");
    } else {
        showMessage("Error al actualizar la contraseña del usuario 'admin'.", "error");
    }
    
    // Mostrar usuarios actualizados
    $usersSql = "SELECT id, username, SUBSTRING(password, 1, 20) as password_preview, nombre, email, rol, activo FROM tb_usuarios";
    $usersStmt = $conn->query($usersSql);
    $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Usuarios actualizados:</h3>";
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Password Preview</th><th>Nombre</th><th>Email</th><th>Rol</th><th>Activo</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        foreach ($user as $key => $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    showMessage("Ahora puedes intentar iniciar sesión con las siguientes credenciales:", "info");
    echo "<ul>";
    echo "<li><strong>Usuario:</strong> admin, <strong>Contraseña:</strong> admin123</li>";
    echo "<li><strong>Usuario:</strong> nico.cornejo, <strong>Contraseña:</strong> N1c0l7as17</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    showMessage("Error: " . $e->getMessage(), "error");
}
?>
