<?php
// Configuración de cabeceras para respuesta JSON
header('Content-Type: application/json');

// Prevenir el almacenamiento en caché del navegador
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Incluir la conexión a la base de datos
require_once 'db_connection.php';

// Verificar si se recibió el folio
if (!isset($_GET['folio']) || empty($_GET['folio'])) {
    echo json_encode([
        'success' => false,
        'message' => 'No se proporcionó un número de folio'
    ]);
    exit;
}

// Obtener el folio y el tipo de documento
$folio = intval($_GET['folio']);
$tipoDTE = isset($_GET['tipo_dte']) ? intval($_GET['tipo_dte']) : null;

try {
    $conn = getConnection();

    // Registrar parámetros de búsqueda para depuración
    error_log("Búsqueda de documento - Folio: $folio, Tipo DTE: " . ($tipoDTE ?? 'no especificado') . ", Es referencia: " . (isset($_GET['is_reference']) ? 'Sí' : 'No'));

    // Construir la consulta SQL
    $sql = "SELECT * FROM tb_facturas_dte WHERE folio = :folio";
    $params = [':folio' => $folio];

    // Solo aplicar el filtro de tipo_dte si NO estamos buscando una referencia
    if ($tipoDTE !== null && !isset($_GET['is_reference'])) {
        $sql .= " AND tipo_dte = :tipo_dte";
        $params[':tipo_dte'] = $tipoDTE;
    }

    // Registrar la consulta SQL para depuración
    error_log("Consulta SQL: $sql");
    error_log("Parámetros: " . json_encode($params));

    // Preparar y ejecutar la consulta
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);

    // Obtener el resultado
    $documento = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($documento) {
        // Verificar si existe el campo json_enviado
        if (isset($documento['json_enviado']) && !empty($documento['json_enviado'])) {
            // Decodificar el JSON para asegurarnos de que es válido
            $jsonData = json_decode($documento['json_enviado'], true);

            if ($jsonData) {
                echo json_encode([
                    'success' => true,
                    'data' => $documento,
                    'json_data' => $jsonData
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'El documento existe pero el JSON no es válido',
                    'data' => $documento
                ]);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'El documento existe pero no contiene datos JSON',
                'data' => $documento
            ]);
        }
    } else {
        // Realizar una consulta adicional para verificar si existen documentos con ese folio sin filtrar por tipo
        $sqlCheck = "SELECT tipo_dte FROM tb_facturas_dte WHERE folio = :folio";
        $stmtCheck = $conn->prepare($sqlCheck);
        $stmtCheck->bindParam(':folio', $folio);
        $stmtCheck->execute();
        $tiposEncontrados = $stmtCheck->fetchAll(PDO::FETCH_COLUMN);

        $mensaje = 'No se encontró ningún documento con el folio proporcionado';
        if (!empty($tiposEncontrados)) {
            $mensaje .= ". Sin embargo, existen documentos con este folio de los siguientes tipos: " . implode(", ", $tiposEncontrados);
            error_log("Folio $folio encontrado pero con otros tipos de documento: " . implode(", ", $tiposEncontrados));
        }

        echo json_encode([
            'success' => false,
            'message' => $mensaje,
            'debug' => [
                'folio_buscado' => $folio,
                'tipo_dte_buscado' => $tipoDTE,
                'es_referencia' => isset($_GET['is_reference']),
                'tipos_encontrados' => $tiposEncontrados
            ]
        ]);
    }
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error en la base de datos: ' . $e->getMessage()
    ]);
}

