<?php
require_once 'db_connection.php';

// Función para registrar mensajes de depuración
function debugLog($message, $type = 'info') {
    $logMessage = "[" . date('Y-m-d H:i:s') . "] [" . $type . "] " . $message;
    error_log("[editar_upgrade.php] " . $logMessage);

    // También almacenar para la respuesta AJAX
    global $debugMessages;
    $debugMessages[] = [
        'time' => date('H:i:s'),
        'message' => $message,
        'type' => $type
    ];
}

// Inicializar variables
$debugMessages = [];
$ajaxResponse = [
    'success' => false,
    'message' => '',
    'debug_messages' => []
];

// Configurar cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    debugLog("Método no permitido: " . $_SERVER['REQUEST_METHOD'], 'error');
    $ajaxResponse['message'] = 'Método no permitido';
    echo json_encode($ajaxResponse);
    exit;
}

// Verificar si se proporcionó un ID
if (!isset($_POST['id']) || empty($_POST['id'])) {
    debugLog("Error: ID no proporcionado", 'error');
    $ajaxResponse['message'] = 'ID no proporcionado';
    echo json_encode($ajaxResponse);
    exit;
}

$id = intval($_POST['id']);
debugLog("Procesando edición del registro con ID: " . $id);

// Verificar si se proporcionaron los datos necesarios
if (!isset($_POST['pagina_modificada']) || empty($_POST['pagina_modificada'])) {
    debugLog("Error: Página modificada no proporcionada", 'error');
    $ajaxResponse['message'] = 'Página modificada no proporcionada';
    echo json_encode($ajaxResponse);
    exit;
}

if (!isset($_POST['descripcion']) || empty($_POST['descripcion'])) {
    debugLog("Error: Descripción no proporcionada", 'error');
    $ajaxResponse['message'] = 'Descripción no proporcionada';
    echo json_encode($ajaxResponse);
    exit;
}

// Obtener los datos del formulario
$paginaModificada = $_POST['pagina_modificada'];

// Simplemente tomar la descripción tal como viene, sin procesar los saltos de línea
$descripcion = $_POST['descripcion'];

debugLog("Datos recibidos:");
debugLog("Página modificada: " . $paginaModificada);
debugLog("Descripción: " . substr($descripcion, 0, 100) . (strlen($descripcion) > 100 ? '...' : ''));

try {
    // Conectar a la base de datos
    $conn = getConnection();
    debugLog("Conexión a la base de datos establecida");

    // Iniciar transacción para asegurar la integridad de los datos
    $conn->beginTransaction();
    debugLog("Transacción iniciada");

    // Primero, obtener información del registro actual
    $stmt = $conn->prepare("SELECT * FROM tb_upgrades WHERE id = ?");
    $stmt->execute([$id]);
    $registro = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$registro) {
        debugLog("Error: Registro con ID " . $id . " no encontrado", 'error');
        $ajaxResponse['message'] = 'Registro no encontrado';
        echo json_encode($ajaxResponse);
        exit;
    }

    debugLog("Registro encontrado: " . print_r($registro, true));

    // Variables para la actualización
    $rutaArchivo = $registro['ruta_archivo'];
    $tipoArchivo = $registro['tipo_archivo'];
    $archivoModificado = false;

    // Verificar si se subió un nuevo archivo
    if (isset($_FILES['archivo']) && $_FILES['archivo']['error'] === UPLOAD_ERR_OK && $_FILES['archivo']['size'] > 0) {
        debugLog("Se ha subido un nuevo archivo");

        $archivo = $_FILES['archivo'];
        $nombreArchivo = $archivo['name'];
        $tipoMime = $archivo['type'];
        $tamanoArchivo = $archivo['size'];
        $archivoTemporal = $archivo['tmp_name'];

        debugLog("Información del archivo:");
        debugLog("Nombre: " . $nombreArchivo);
        debugLog("Tipo MIME: " . $tipoMime);
        debugLog("Tamaño: " . $tamanoArchivo . " bytes");

        // Validar el tipo de archivo
        $esImagen = strpos($tipoMime, 'image/') === 0;
        $esVideo = strpos($tipoMime, 'video/') === 0;

        if (!$esImagen && !$esVideo) {
            debugLog("Error: Tipo de archivo no permitido", 'error');
            $ajaxResponse['message'] = 'Tipo de archivo no permitido. Solo se permiten imágenes y videos.';
            echo json_encode($ajaxResponse);
            exit;
        }

        // Determinar el tipo de archivo para la base de datos
        if ($esImagen) {
            $tipoArchivo = 'imagen';
        } elseif ($esVideo) {
            $tipoArchivo = 'video';
        }

        // Crear directorio si no existe
        $directorioDestino = 'images/upgrades/';
        if (!file_exists($directorioDestino)) {
            mkdir($directorioDestino, 0755, true);
            debugLog("Directorio creado: " . $directorioDestino);
        }

        // Generar un nombre único para el archivo
        $extension = pathinfo($nombreArchivo, PATHINFO_EXTENSION);
        $nombreUnico = 'upgrade_' . time() . '_' . uniqid() . '.' . $extension;
        $rutaDestino = $directorioDestino . $nombreUnico;

        debugLog("Ruta de destino: " . $rutaDestino);

        // Mover el archivo subido
        if (move_uploaded_file($archivoTemporal, $rutaDestino)) {
            debugLog("Archivo movido exitosamente", 'success');

            // Si hay un archivo anterior, eliminarlo
            if (!empty($registro['ruta_archivo'])) {
                $rutaArchivoAnterior = __DIR__ . '/' . $registro['ruta_archivo'];
                debugLog("Intentando eliminar archivo anterior: " . $rutaArchivoAnterior);

                if (file_exists($rutaArchivoAnterior)) {
                    if (unlink($rutaArchivoAnterior)) {
                        debugLog("Archivo anterior eliminado correctamente", 'success');
                    } else {
                        debugLog("No se pudo eliminar el archivo anterior", 'warning');
                    }
                } else {
                    debugLog("El archivo anterior no existe en la ruta especificada", 'warning');
                }
            }

            // Actualizar la ruta del archivo
            $rutaArchivo = $rutaDestino;
            $archivoModificado = true;
        } else {
            debugLog("Error al mover el archivo", 'error');
            $ajaxResponse['message'] = 'Error al guardar el archivo.';
            echo json_encode($ajaxResponse);
            exit;
        }
    } else {
        debugLog("No se ha subido un nuevo archivo, se mantiene el archivo actual");
    }

    // Preparar la consulta SQL para actualizar el registro
    $sql = "UPDATE tb_upgrades SET
            pagina_modificada = :pagina_modificada,
            descripcion = :descripcion";

    // Agregar campos de archivo solo si se modificó
    if ($archivoModificado) {
        $sql .= ", ruta_archivo = :ruta_archivo, tipo_archivo = :tipo_archivo";
    }

    $sql .= " WHERE id = :id";

    debugLog("SQL a ejecutar: " . $sql);

    $stmt = $conn->prepare($sql);
    $stmt->bindParam(':pagina_modificada', $paginaModificada);
    $stmt->bindParam(':descripcion', $descripcion);
    $stmt->bindParam(':id', $id);

    if ($archivoModificado) {
        $stmt->bindParam(':ruta_archivo', $rutaArchivo);
        $stmt->bindParam(':tipo_archivo', $tipoArchivo);
    }

    // Ejecutar la consulta
    $result = $stmt->execute();

    if ($result) {
        // Confirmar la transacción
        $conn->commit();
        debugLog("Transacción confirmada", 'success');

        $ajaxResponse['success'] = true;
        $ajaxResponse['message'] = 'Registro actualizado correctamente';
        $ajaxResponse['id'] = $id;

        if ($archivoModificado) {
            $ajaxResponse['ruta_archivo'] = $rutaArchivo;
            $ajaxResponse['tipo_archivo'] = $tipoArchivo;
        }
    } else {
        // Revertir la transacción en caso de error
        $conn->rollBack();
        debugLog("Transacción revertida debido a un error", 'error');

        $errorInfo = $stmt->errorInfo();
        debugLog("Error al actualizar el registro: " . print_r($errorInfo, true), 'error');
        $ajaxResponse['message'] = 'Error al actualizar el registro: ' . $errorInfo[2];
    }
} catch (Exception $e) {
    // Revertir la transacción en caso de excepción
    if (isset($conn) && $conn instanceof PDO) {
        $conn->rollBack();
        debugLog("Transacción revertida debido a una excepción", 'error');
    }

    debugLog("Excepción capturada: " . $e->getMessage(), 'error');
    $ajaxResponse['message'] = 'Error: ' . $e->getMessage();
}

// Agregar mensajes de depuración a la respuesta
$ajaxResponse['debug_messages'] = $debugMessages;

// Enviar la respuesta JSON
echo json_encode($ajaxResponse);
?>
